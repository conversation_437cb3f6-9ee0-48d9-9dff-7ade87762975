#!/usr/bin/env python3
"""
Integration test script to verify the fraud detection platform integration.
"""
import requests
import json
import time
from datetime import datetime

# Service URLs
MODEL_SERVICE_URL = "http://localhost:8000"
INGEST_SERVICE_URL = "http://localhost:9000"

def test_service_health(service_name, url):
    """Test if a service is healthy"""
    try:
        response = requests.get(f"{url}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ {service_name} is healthy: {data.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ {service_name} health check failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ {service_name} is not accessible: {str(e)}")
        return False

def test_authentication():
    """Test authentication endpoint"""
    try:
        # Test login with analyst credentials
        login_data = {
            "username": "analyst",
            "password": "password"
        }
        
        response = requests.post(
            f"{MODEL_SERVICE_URL}/auth/login",
            data=login_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            token = result.get("access_token")
            print(f"✅ Authentication successful, token received")
            return token
        else:
            print(f"❌ Authentication failed: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {str(e)}")
        return None

def test_transactions_endpoint(token):
    """Test transactions endpoint"""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(
            f"{MODEL_SERVICE_URL}/transactions/latest?n=10",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            transactions = response.json()
            print(f"✅ Transactions endpoint works, returned {len(transactions)} transactions")
            return True
        else:
            print(f"❌ Transactions endpoint failed: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Transactions endpoint error: {str(e)}")
        return False

def test_transaction_processing():
    """Test transaction processing through ingest service"""
    try:
        test_transaction = {
            "transaction_id": "test_integration_001",
            "step": 1,
            "type": "TRANSFER",
            "amount": 75000.0,
            "nameOrig": "C123456789",
            "oldbalanceOrg": 100000.0,
            "newbalanceOrig": 25000.0,
            "nameDest": "C987654321",
            "oldbalanceDest": 50000.0,
            "newbalanceDest": 125000.0
        }
        
        response = requests.post(
            f"{INGEST_SERVICE_URL}/process",
            json=test_transaction,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            risk_score = result.get('risk_score', 0)
            print(f"✅ Transaction processing works, risk score: {risk_score:.3f}")
            return True
        else:
            print(f"❌ Transaction processing failed: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Transaction processing error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Fraud Detection Platform Integration")
    print("=" * 60)
    
    # Test service health
    print("\n📋 Testing Service Health...")
    model_healthy = test_service_health("Model Service", MODEL_SERVICE_URL)
    ingest_healthy = test_service_health("Ingest Service", INGEST_SERVICE_URL)
    
    if not (model_healthy and ingest_healthy):
        print("\n❌ Some services are not healthy. Please start the services first.")
        return False
    
    # Test authentication
    print("\n🔐 Testing Authentication...")
    token = test_authentication()
    
    if not token:
        print("\n❌ Authentication failed. Please check database setup.")
        return False
    
    # Test transactions endpoint
    print("\n📊 Testing Transactions Endpoint...")
    transactions_work = test_transactions_endpoint(token)
    
    # Test transaction processing
    print("\n⚙️  Testing Transaction Processing...")
    processing_works = test_transaction_processing()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Integration Test Summary:")
    print(f"   Model Service Health: {'✅' if model_healthy else '❌'}")
    print(f"   Ingest Service Health: {'✅' if ingest_healthy else '❌'}")
    print(f"   Authentication: {'✅' if token else '❌'}")
    print(f"   Transactions Endpoint: {'✅' if transactions_work else '❌'}")
    print(f"   Transaction Processing: {'✅' if processing_works else '❌'}")
    
    all_tests_passed = all([
        model_healthy, ingest_healthy, token, transactions_work, processing_works
    ])
    
    if all_tests_passed:
        print("\n🎉 All integration tests passed!")
        print("The fraud detection platform is working correctly.")
    else:
        print("\n❌ Some integration tests failed.")
        print("Please check the service logs and fix any issues.")
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
