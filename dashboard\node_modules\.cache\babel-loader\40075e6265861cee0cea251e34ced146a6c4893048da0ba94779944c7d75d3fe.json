{"ast": null, "code": "/*!\n * Chart.js v4.4.9\n * https://www.chartjs.org\n * (c) 2025 Chart.js Contributors\n * Released under the MIT License\n */\nimport { Color } from '@kurkle/color';\n\n/**\n * @namespace Chart.helpers\n */ /**\n    * An empty function that can be used, for example, for optional callback.\n    */\nfunction noop() {\n  /* noop */}\n/**\n * Returns a unique id, sequentially generated from a global variable.\n */\nconst uid = (() => {\n  let id = 0;\n  return () => id++;\n})();\n/**\n * Returns true if `value` is neither null nor undefined, else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nfunction isNullOrUndef(value) {\n  return value === null || value === undefined;\n}\n/**\n * Returns true if `value` is an array (including typed arrays), else returns false.\n * @param value - The value to test.\n * @function\n */\nfunction isArray(value) {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\n/**\n * Returns true if `value` is an object (excluding null), else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nfunction isObject(value) {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\n/**\n * Returns true if `value` is a finite number, else returns false\n * @param value  - The value to test.\n */\nfunction isNumberFinite(value) {\n  return (typeof value === 'number' || value instanceof Number) && isFinite(+value);\n}\n/**\n * Returns `value` if finite, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is not finite.\n */\nfunction finiteOrDefault(value, defaultValue) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\n/**\n * Returns `value` if defined, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is undefined.\n */\nfunction valueOrDefault(value, defaultValue) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\nconst toPercentage = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 : +value / dimension;\nconst toDimension = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 * dimension : +value;\n/**\n * Calls `fn` with the given `args` in the scope defined by `thisArg` and returns the\n * value returned by `fn`. If `fn` is not a function, this method returns undefined.\n * @param fn - The function to call.\n * @param args - The arguments with which `fn` should be called.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n */\nfunction callback(fn, args, thisArg) {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\nfunction each(loopable, fn, thisArg, reverse) {\n  let i, len, keys;\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\n/**\n * Returns true if the `a0` and `a1` arrays have the same content, else returns false.\n * @param a0 - The array to compare\n * @param a1 - The array to compare\n * @private\n */\nfunction _elementsEqual(a0, a1) {\n  let i, ilen, v0, v1;\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Returns a deep copy of `source` without keeping references on objects and arrays.\n * @param source - The value to clone.\n */\nfunction clone(source) {\n  if (isArray(source)) {\n    return source.map(clone);\n  }\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone(source[keys[k]]);\n    }\n    return target;\n  }\n  return source;\n}\nfunction isValidKey(key) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\n/**\n * The default merger when Chart.helpers.merge is called without merger option.\n * Note(SB): also used by mergeConfig and mergeScaleConfig as fallback.\n * @private\n */\nfunction _merger(key, target, source, options) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone(sval);\n  }\n}\nfunction merge(target, source, options) {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n  if (!isObject(target)) {\n    return target;\n  }\n  options = options || {};\n  const merger = options.merger || _merger;\n  let current;\n  for (let i = 0; i < ilen; ++i) {\n    current = sources[i];\n    if (!isObject(current)) {\n      continue;\n    }\n    const keys = Object.keys(current);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, current, options);\n    }\n  }\n  return target;\n}\nfunction mergeIf(target, source) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  return merge(target, source, {\n    merger: _mergerIf\n  });\n}\n/**\n * Merges source[key] in target[key] only if target[key] is undefined.\n * @private\n */\nfunction _mergerIf(key, target, source) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone(sval);\n  }\n}\n/**\n * @private\n */\nfunction _deprecated(scope, value, previous, current) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous + '\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\n// resolveObjectKey resolver cache\nconst keyResolvers = {\n  // Chart.helpers.core resolveObjectKey should resolve empty key to root object\n  '': v => v,\n  // default resolvers\n  x: o => o.x,\n  y: o => o.y\n};\n/**\n * @private\n */\nfunction _splitKey(key) {\n  const parts = key.split('.');\n  const keys = [];\n  let tmp = '';\n  for (const part of parts) {\n    tmp += part;\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n  return keys;\n}\nfunction _getKeyResolver(key) {\n  const keys = _splitKey(key);\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        break;\n      }\n      obj = obj && obj[k];\n    }\n    return obj;\n  };\n}\nfunction resolveObjectKey(obj, key) {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n  return resolver(obj);\n}\n/**\n * @private\n */\nfunction _capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\nconst defined = value => typeof value !== 'undefined';\nconst isFunction = value => typeof value === 'function';\n// Adapted from https://stackoverflow.com/questions/31128855/comparing-ecma6-sets-for-equality#31129384\nconst setsEqual = (a, b) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n  return true;\n};\n/**\n * @param e - The event\n * @private\n */\nfunction _isClickEvent(e) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n\n/**\n * @alias Chart.helpers.math\n * @namespace\n */\nconst PI = Math.PI;\nconst TAU = 2 * PI;\nconst PITAU = TAU + PI;\nconst INFINITY = Number.POSITIVE_INFINITY;\nconst RAD_PER_DEG = PI / 180;\nconst HALF_PI = PI / 2;\nconst QUARTER_PI = PI / 4;\nconst TWO_THIRDS_PI = PI * 2 / 3;\nconst log10 = Math.log10;\nconst sign = Math.sign;\nfunction almostEquals(x, y, epsilon) {\n  return Math.abs(x - y) < epsilon;\n}\n/**\n * Implementation of the nice number algorithm used in determining where axis labels will go\n */\nfunction niceNum(range) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\n/**\n * Returns an array of factors sorted from 1 to sqrt(value)\n * @private\n */\nfunction _factorize(value) {\n  const result = [];\n  const sqrt = Math.sqrt(value);\n  let i;\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) {\n    result.push(sqrt);\n  }\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\n/**\n * Verifies that attempting to coerce n to string or number won't throw a TypeError.\n */\nfunction isNonPrimitive(n) {\n  return typeof n === 'symbol' || typeof n === 'object' && n !== null && !(Symbol.toPrimitive in n || 'toString' in n || 'valueOf' in n);\n}\nfunction isNumber(n) {\n  return !isNonPrimitive(n) && !isNaN(parseFloat(n)) && isFinite(n);\n}\nfunction almostWhole(x, epsilon) {\n  const rounded = Math.round(x);\n  return rounded - epsilon <= x && rounded + epsilon >= x;\n}\n/**\n * @private\n */\nfunction _setMinAndMaxByKey(array, target, property) {\n  let i, ilen, value;\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\nfunction toRadians(degrees) {\n  return degrees * (PI / 180);\n}\nfunction toDegrees(radians) {\n  return radians * (180 / PI);\n}\n/**\n * Returns the number of decimal places\n * i.e. the number of digits after the decimal point, of the value of this Number.\n * @param x - A number.\n * @returns The number of decimal places.\n * @private\n */\nfunction _decimalPlaces(x) {\n  if (!isNumberFinite(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\n// Gets the angle from vertical upright to the point about a centre.\nfunction getAngleFromPoint(centrePoint, anglePoint) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n  if (angle < -0.5 * PI) {\n    angle += TAU; // make sure the returned angle is in the range of (-PI/2, 3PI/2]\n  }\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\nfunction distanceBetweenPoints(pt1, pt2) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\n/**\n * Shortest distance between angles, in either direction.\n * @private\n */\nfunction _angleDiff(a, b) {\n  return (a - b + PITAU) % TAU - PI;\n}\n/**\n * Normalize angle to be between 0 and 2*PI\n * @private\n */\nfunction _normalizeAngle(a) {\n  return (a % TAU + TAU) % TAU;\n}\n/**\n * @private\n */\nfunction _angleBetween(angle, start, end, sameAngleIsFullCircle) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || sameAngleIsFullCircle && s === e || angleToStart > angleToEnd && startToAngle < endToAngle;\n}\n/**\n * Limit `value` between `min` and `max`\n * @param value\n * @param min\n * @param max\n * @private\n */\nfunction _limitValue(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\n/**\n * @param {number} value\n * @private\n */\nfunction _int16Range(value) {\n  return _limitValue(value, -32768, 32767);\n}\n/**\n * @param value\n * @param start\n * @param end\n * @param [epsilon]\n * @private\n */\nfunction _isBetween(value, start, end, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\nfunction _lookup(table, value, cmp) {\n  cmp = cmp || (index => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid;\n  while (hi - lo > 1) {\n    mid = lo + hi >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n  return {\n    lo,\n    hi\n  };\n}\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @param last - lookup last index\n * @private\n */\nconst _lookupByKey = (table, key, value, last) => _lookup(table, value, last ? index => {\n  const ti = table[index][key];\n  return ti < value || ti === value && table[index + 1][key] === value;\n} : index => table[index][key] < value);\n/**\n * Reverse binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @private\n */\nconst _rlookupByKey = (table, key, value) => _lookup(table, value, index => table[index][key] >= value);\n/**\n * Return subset of `values` between `min` and `max` inclusive.\n * Values are assumed to be in sorted order.\n * @param values - sorted array of values\n * @param min - min value\n * @param max - max value\n */\nfunction _filterBetween(values, min, max) {\n  let start = 0;\n  let end = values.length;\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n  return start > 0 || end < values.length ? values.slice(start, end) : values;\n}\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'];\nfunction listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n  arrayEvents.forEach(key => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value(...args) {\n        const res = base.apply(this, args);\n        array._chartjs.listeners.forEach(object => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n        return res;\n      }\n    });\n  });\n}\nfunction unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n  if (listeners.length > 0) {\n    return;\n  }\n  arrayEvents.forEach(key => {\n    delete array[key];\n  });\n  delete array._chartjs;\n}\n/**\n * @param items\n */\nfunction _arrayUnique(items) {\n  const set = new Set(items);\n  if (set.size === items.length) {\n    return items;\n  }\n  return Array.from(set);\n}\nfunction fontString(pixelSize, fontStyle, fontFamily) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\n/**\n* Request animation polyfill\n*/\nconst requestAnimFrame = function () {\n  if (typeof window === 'undefined') {\n    return function (callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}();\n/**\n * Throttles calling `fn` once per animation frame\n * Latest arguments are used on the actual call\n */\nfunction throttled(fn, thisArg) {\n  let argsToUse = [];\n  let ticking = false;\n  return function (...args) {\n    // Save the args for use later\n    argsToUse = args;\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, argsToUse);\n      });\n    }\n  };\n}\n/**\n * Debounces calling `fn` for `delay` ms\n */\nfunction debounce(fn, delay) {\n  let timeout;\n  return function (...args) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\n/**\n * Converts 'start' to 'left', 'end' to 'right' and others to 'center'\n * @private\n */\nconst _toLeftRightCenter = align => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\n/**\n * Returns `start`, `end` or `(start + end) / 2` depending on `align`. Defaults to `center`\n * @private\n */\nconst _alignStartEnd = (align, start, end) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\n/**\n * Returns `left`, `right` or `(left + right) / 2` depending on `align`. Defaults to `left`\n * @private\n */\nconst _textX = (align, left, right, rtl) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n/**\n * Return start and count of visible points.\n * @private\n */\nfunction _getStartAndCountOfVisiblePoints(meta, points, animationsDisabled) {\n  const pointCount = points.length;\n  let start = 0;\n  let count = pointCount;\n  if (meta._sorted) {\n    const {\n      iScale,\n      vScale,\n      _parsed\n    } = meta;\n    const spanGaps = meta.dataset ? meta.dataset.options ? meta.dataset.options.spanGaps : null : null;\n    const axis = iScale.axis;\n    const {\n      min,\n      max,\n      minDefined,\n      maxDefined\n    } = iScale.getUserBounds();\n    if (minDefined) {\n      start = Math.min(\n      // @ts-expect-error Need to type _parsed\n      _lookupByKey(_parsed, axis, min).lo,\n      // @ts-expect-error Need to fix types on _lookupByKey\n      animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo);\n      if (spanGaps) {\n        const distanceToDefinedLo = _parsed.slice(0, start + 1).reverse().findIndex(point => !isNullOrUndef(point[vScale.axis]));\n        start -= Math.max(0, distanceToDefinedLo);\n      }\n      start = _limitValue(start, 0, pointCount - 1);\n    }\n    if (maxDefined) {\n      let end = Math.max(\n      // @ts-expect-error Need to type _parsed\n      _lookupByKey(_parsed, iScale.axis, max, true).hi + 1,\n      // @ts-expect-error Need to fix types on _lookupByKey\n      animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1);\n      if (spanGaps) {\n        const distanceToDefinedHi = _parsed.slice(end - 1).findIndex(point => !isNullOrUndef(point[vScale.axis]));\n        end += Math.max(0, distanceToDefinedHi);\n      }\n      count = _limitValue(end, start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n  return {\n    start,\n    count\n  };\n}\n/**\n * Checks if the scale ranges have changed.\n * @param {object} meta - dataset meta.\n * @returns {boolean}\n * @private\n */\nfunction _scaleRangesChanged(meta) {\n  const {\n    xScale,\n    yScale,\n    _scaleRanges\n  } = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n  const changed = _scaleRanges.xmin !== xScale.min || _scaleRanges.xmax !== xScale.max || _scaleRanges.ymin !== yScale.min || _scaleRanges.ymax !== yScale.max;\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\nconst atEdge = t => t === 0 || t === 1;\nconst elasticIn = (t, s, p) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t, s, p) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\n/**\n * Easing functions adapted from Robert Penner's easing equations.\n * @namespace Chart.helpers.easing.effects\n * @see http://www.robertpenner.com/easing/\n */\nconst effects = {\n  linear: t => t,\n  easeInQuad: t => t * t,\n  easeOutQuad: t => -t * (t - 2),\n  easeInOutQuad: t => (t /= 0.5) < 1 ? 0.5 * t * t : -0.5 * (--t * (t - 2) - 1),\n  easeInCubic: t => t * t * t,\n  easeOutCubic: t => (t -= 1) * t * t + 1,\n  easeInOutCubic: t => (t /= 0.5) < 1 ? 0.5 * t * t * t : 0.5 * ((t -= 2) * t * t + 2),\n  easeInQuart: t => t * t * t * t,\n  easeOutQuart: t => -((t -= 1) * t * t * t - 1),\n  easeInOutQuart: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t : -0.5 * ((t -= 2) * t * t * t - 2),\n  easeInQuint: t => t * t * t * t * t,\n  easeOutQuint: t => (t -= 1) * t * t * t * t + 1,\n  easeInOutQuint: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t * t : 0.5 * ((t -= 2) * t * t * t * t + 2),\n  easeInSine: t => -Math.cos(t * HALF_PI) + 1,\n  easeOutSine: t => Math.sin(t * HALF_PI),\n  easeInOutSine: t => -0.5 * (Math.cos(PI * t) - 1),\n  easeInExpo: t => t === 0 ? 0 : Math.pow(2, 10 * (t - 1)),\n  easeOutExpo: t => t === 1 ? 1 : -Math.pow(2, -10 * t) + 1,\n  easeInOutExpo: t => atEdge(t) ? t : t < 0.5 ? 0.5 * Math.pow(2, 10 * (t * 2 - 1)) : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n  easeInCirc: t => t >= 1 ? t : -(Math.sqrt(1 - t * t) - 1),\n  easeOutCirc: t => Math.sqrt(1 - (t -= 1) * t),\n  easeInOutCirc: t => (t /= 0.5) < 1 ? -0.5 * (Math.sqrt(1 - t * t) - 1) : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n  easeInElastic: t => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n  easeOutElastic: t => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n  easeInOutElastic(t) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t : t < 0.5 ? 0.5 * elasticIn(t * 2, s, p) : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n  easeInBack(t) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n  easeOutBack(t) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n  easeInOutBack(t) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= 1.525) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= 1.525) + 1) * t + s) + 2);\n  },\n  easeInBounce: t => 1 - effects.easeOutBounce(1 - t),\n  easeOutBounce(t) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < 1 / d) {\n      return m * t * t;\n    }\n    if (t < 2 / d) {\n      return m * (t -= 1.5 / d) * t + 0.75;\n    }\n    if (t < 2.5 / d) {\n      return m * (t -= 2.25 / d) * t + 0.9375;\n    }\n    return m * (t -= 2.625 / d) * t + 0.984375;\n  },\n  easeInOutBounce: t => t < 0.5 ? effects.easeInBounce(t * 2) * 0.5 : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5\n};\nfunction isPatternOrGradient(value) {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n  return false;\n}\nfunction color(value) {\n  return isPatternOrGradient(value) ? value : new Color(value);\n}\nfunction getHoverColor(value) {\n  return isPatternOrGradient(value) ? value : new Color(value).saturate(0.5).darken(0.1).hexString();\n}\nconst numbers = ['x', 'y', 'borderWidth', 'radius', 'tension'];\nconst colors = ['color', 'borderColor', 'backgroundColor'];\nfunction applyAnimationsDefaults(defaults) {\n  defaults.set('animation', {\n    delay: undefined,\n    duration: 1000,\n    easing: 'easeOutQuart',\n    fn: undefined,\n    from: undefined,\n    loop: undefined,\n    to: undefined,\n    type: undefined\n  });\n  defaults.describe('animation', {\n    _fallback: false,\n    _indexable: false,\n    _scriptable: name => name !== 'onProgress' && name !== 'onComplete' && name !== 'fn'\n  });\n  defaults.set('animations', {\n    colors: {\n      type: 'color',\n      properties: colors\n    },\n    numbers: {\n      type: 'number',\n      properties: numbers\n    }\n  });\n  defaults.describe('animations', {\n    _fallback: 'animation'\n  });\n  defaults.set('transitions', {\n    active: {\n      animation: {\n        duration: 400\n      }\n    },\n    resize: {\n      animation: {\n        duration: 0\n      }\n    },\n    show: {\n      animations: {\n        colors: {\n          from: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          duration: 0\n        }\n      }\n    },\n    hide: {\n      animations: {\n        colors: {\n          to: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          easing: 'linear',\n          fn: v => v | 0\n        }\n      }\n    }\n  });\n}\nfunction applyLayoutsDefaults(defaults) {\n  defaults.set('layout', {\n    autoPadding: true,\n    padding: {\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0\n    }\n  });\n}\nconst intlCache = new Map();\nfunction getNumberFormat(locale, options) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\nfunction formatNumber(num, locale, options) {\n  return getNumberFormat(locale, options).format(num);\n}\nconst formatters = {\n  values(value) {\n    return isArray(value) ? value : '' + value;\n  },\n  numeric(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const locale = this.chart.options.locale;\n    let notation;\n    let delta = tickValue;\n    if (ticks.length > 1) {\n      const maxTick = Math.max(Math.abs(ticks[0].value), Math.abs(ticks[ticks.length - 1].value));\n      if (maxTick < 1e-4 || maxTick > 1e+15) {\n        notation = 'scientific';\n      }\n      delta = calculateDelta(tickValue, ticks);\n    }\n    const logDelta = log10(Math.abs(delta));\n    const numDecimal = isNaN(logDelta) ? 1 : Math.max(Math.min(-1 * Math.floor(logDelta), 20), 0);\n    const options = {\n      notation,\n      minimumFractionDigits: numDecimal,\n      maximumFractionDigits: numDecimal\n    };\n    Object.assign(options, this.options.ticks.format);\n    return formatNumber(tickValue, locale, options);\n  },\n  logarithmic(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const remain = ticks[index].significand || tickValue / Math.pow(10, Math.floor(log10(tickValue)));\n    if ([1, 2, 3, 5, 10, 15].includes(remain) || index > 0.8 * ticks.length) {\n      return formatters.numeric.call(this, tickValue, index, ticks);\n    }\n    return '';\n  }\n};\nfunction calculateDelta(tickValue, ticks) {\n  let delta = ticks.length > 3 ? ticks[2].value - ticks[1].value : ticks[1].value - ticks[0].value;\n  if (Math.abs(delta) >= 1 && tickValue !== Math.floor(tickValue)) {\n    delta = tickValue - Math.floor(tickValue);\n  }\n  return delta;\n}\nvar Ticks = {\n  formatters\n};\nfunction applyScaleDefaults(defaults) {\n  defaults.set('scale', {\n    display: true,\n    offset: false,\n    reverse: false,\n    beginAtZero: false,\n    bounds: 'ticks',\n    clip: true,\n    grace: 0,\n    grid: {\n      display: true,\n      lineWidth: 1,\n      drawOnChartArea: true,\n      drawTicks: true,\n      tickLength: 8,\n      tickWidth: (_ctx, options) => options.lineWidth,\n      tickColor: (_ctx, options) => options.color,\n      offset: false\n    },\n    border: {\n      display: true,\n      dash: [],\n      dashOffset: 0.0,\n      width: 1\n    },\n    title: {\n      display: false,\n      text: '',\n      padding: {\n        top: 4,\n        bottom: 4\n      }\n    },\n    ticks: {\n      minRotation: 0,\n      maxRotation: 50,\n      mirror: false,\n      textStrokeWidth: 0,\n      textStrokeColor: '',\n      padding: 3,\n      display: true,\n      autoSkip: true,\n      autoSkipPadding: 3,\n      labelOffset: 0,\n      callback: Ticks.formatters.values,\n      minor: {},\n      major: {},\n      align: 'center',\n      crossAlign: 'near',\n      showLabelBackdrop: false,\n      backdropColor: 'rgba(255, 255, 255, 0.75)',\n      backdropPadding: 2\n    }\n  });\n  defaults.route('scale.ticks', 'color', '', 'color');\n  defaults.route('scale.grid', 'color', '', 'borderColor');\n  defaults.route('scale.border', 'color', '', 'borderColor');\n  defaults.route('scale.title', 'color', '', 'color');\n  defaults.describe('scale', {\n    _fallback: false,\n    _scriptable: name => !name.startsWith('before') && !name.startsWith('after') && name !== 'callback' && name !== 'parser',\n    _indexable: name => name !== 'borderDash' && name !== 'tickBorderDash' && name !== 'dash'\n  });\n  defaults.describe('scales', {\n    _fallback: 'scale'\n  });\n  defaults.describe('scale.ticks', {\n    _scriptable: name => name !== 'backdropPadding' && name !== 'callback',\n    _indexable: name => name !== 'backdropPadding'\n  });\n}\nconst overrides = Object.create(null);\nconst descriptors = Object.create(null);\nfunction getScope$1(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope$1(root, scope), values);\n  }\n  return merge(getScope$1(root, ''), scope);\n}\nclass Defaults {\n  constructor(_descriptors, _appliers) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = context => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = ['mousemove', 'mouseout', 'click', 'touchstart', 'touchmove'];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n    this.describe(_descriptors);\n    this.apply(_appliers);\n  }\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n  get(scope) {\n    return getScope$1(this, scope);\n  }\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope$1(this, scope);\n    const targetScopeObject = getScope$1(this, targetScope);\n    const privateName = '_' + name;\n    Object.defineProperties(scopeObject, {\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n  apply(appliers) {\n    appliers.forEach(apply => apply(this));\n  }\n}\nvar defaults = /* #__PURE__ */new Defaults({\n  _scriptable: name => !name.startsWith('on'),\n  _indexable: name => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false\n  }\n}, [applyAnimationsDefaults, applyLayoutsDefaults, applyScaleDefaults]);\n\n/**\n * Converts the given font object into a CSS font string.\n * @param font - A font object.\n * @return The CSS font string. See https://developer.mozilla.org/en-US/docs/Web/CSS/font\n * @private\n */\nfunction toFontString(font) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n  return (font.style ? font.style + ' ' : '') + (font.weight ? font.weight + ' ' : '') + font.size + 'px ' + font.family;\n}\n/**\n * @private\n */\nfunction _measureText(ctx, data, gc, longest, string) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\n/**\n * @private\n */ // eslint-disable-next-line complexity\nfunction _longestText(ctx, font, arrayOfThings, cache) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n  ctx.save();\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i, j, jlen, thing, nestedThing;\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n    // Undefined strings and arrays should not be measured\n    if (thing !== undefined && thing !== null && !isArray(thing)) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      // if it is an array lets measure each element\n      // to do maybe simplify this function a bit so we can do this more recursively?\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        // Undefined strings and arrays should not be measured\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n  ctx.restore();\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\n/**\n * Returns the aligned pixel value to avoid anti-aliasing blur\n * @param chart - The chart instance.\n * @param pixel - A pixel value.\n * @param width - The width of the element.\n * @returns The aligned pixel value.\n * @private\n */\nfunction _alignPixel(chart, pixel, width) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\n/**\n * Clears the entire canvas.\n */\nfunction clearCanvas(canvas, ctx) {\n  if (!ctx && !canvas) {\n    return;\n  }\n  ctx = ctx || canvas.getContext('2d');\n  ctx.save();\n  // canvas.width and canvas.height do not consider the canvas transform,\n  // while clearRect does\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\nfunction drawPoint(ctx, options, x, y) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  drawPointLegend(ctx, options, x, y, null);\n}\n// eslint-disable-next-line complexity\nfunction drawPointLegend(ctx, options, x, y, w) {\n  let type, xOffset, yOffset, size, cornerRadius, width, xOffsetW, yOffsetW;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n  ctx.beginPath();\n  switch (style) {\n    // Default includes circle\n    default:\n      if (w) {\n        ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n      } else {\n        ctx.arc(x, y, radius, 0, TAU);\n      }\n      ctx.closePath();\n      break;\n    case 'triangle':\n      width = w ? w / 2 : radius;\n      ctx.moveTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n    case 'rectRounded':\n      // NOTE: the rounded rect implementation changed to use `arc` instead of\n      // `quadraticCurveTo` since it generates better results when rect is\n      // almost a circle. 0.516 (instead of 0.5) produces results with visually\n      // closer proportion to the previous impl and it is inscribed in the\n      // circle with `radius`. For more details, see the following PRs:\n      // https://github.com/chartjs/Chart.js/issues/5597\n      // https://github.com/chartjs/Chart.js/issues/5858\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      xOffsetW = Math.cos(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      yOffsetW = Math.sin(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      ctx.arc(x - xOffsetW, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffsetW, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffsetW, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffsetW, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        width = w ? w / 2 : size;\n        ctx.rect(x - width, y - size, 2 * width, 2 * size);\n        break;\n      }\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'rectRot':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      ctx.closePath();\n      break;\n    case 'crossRot':\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'cross':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'star':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      rad += QUARTER_PI;\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'line':\n      xOffset = w ? w / 2 : Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * (w ? w / 2 : radius), y + Math.sin(rad) * radius);\n      break;\n    case false:\n      ctx.closePath();\n      break;\n  }\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\n/**\n * Returns true if the point is inside the rectangle\n * @param point - The point to test\n * @param area - The rectangle\n * @param margin - allowed margin\n * @private\n */\nfunction _isPointInArea(point, area, margin) {\n  margin = margin || 0.5; // margin - default is to match rounded decimals\n  return !area || point && point.x > area.left - margin && point.x < area.right + margin && point.y > area.top - margin && point.y < area.bottom + margin;\n}\nfunction clipArea(ctx, area) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\nfunction unclipArea(ctx) {\n  ctx.restore();\n}\n/**\n * @private\n */\nfunction _steppedLineTo(ctx, previous, target, flip, mode) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\n/**\n * @private\n */\nfunction _bezierCurveTo(ctx, previous, target, flip) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(flip ? previous.cp1x : previous.cp2x, flip ? previous.cp1y : previous.cp2y, flip ? target.cp2x : target.cp1x, flip ? target.cp2y : target.cp1y, target.x, target.y);\n}\nfunction setRenderOpts(ctx, opts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\nfunction decorateText(ctx, x, y, line, opts) {\n  if (opts.strikethrough || opts.underline) {\n    /**\n    * Now that IE11 support has been dropped, we can use more\n    * of the TextMetrics object. The actual bounding boxes\n    * are unflagged in Chrome, Firefox, Edge, and Safari so they\n    * can be safely used.\n    * See https://developer.mozilla.org/en-US/docs/Web/API/TextMetrics#Browser_compatibility\n    */\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\nfunction drawBackdrop(ctx, opts) {\n  const oldColor = ctx.fillStyle;\n  ctx.fillStyle = opts.color;\n  ctx.fillRect(opts.left, opts.top, opts.width, opts.height);\n  ctx.fillStyle = oldColor;\n}\n/**\n * Render text onto the canvas\n */\nfunction renderText(ctx, text, x, y, font, opts = {}) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i, line;\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n    if (opts.backdrop) {\n      drawBackdrop(ctx, opts.backdrop);\n    }\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n    y += Number(font.lineHeight);\n  }\n  ctx.restore();\n}\n/**\n * Add a path of a rectangle with rounded corners to the current sub-path\n * @param ctx - Context\n * @param rect - Bounding rect\n */\nfunction addRoundedRectPath(ctx, rect) {\n  const {\n    x,\n    y,\n    w,\n    h,\n    radius\n  } = rect;\n  // top left arc\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, 1.5 * PI, PI, true);\n  // line from top left to bottom left\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n  // bottom left arc\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n  // line from bottom left to bottom right\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n  // bottom right arc\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n  // line from bottom right to top right\n  ctx.lineTo(x + w, y + radius.topRight);\n  // top right arc\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n  // line from top right to top left\n  ctx.lineTo(x + radius.topLeft, y);\n}\nconst LINE_HEIGHT = /^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/;\nconst FONT_STYLE = /^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;\n/**\n * @alias Chart.helpers.options\n * @namespace\n */ /**\n    * Converts the given line height `value` in pixels for a specific font `size`.\n    * @param value - The lineHeight to parse (eg. 1.6, '14px', '75%', '1.6em').\n    * @param size - The font size (in pixels) used to resolve relative `value`.\n    * @returns The effective line height in pixels (size * 1.2 if value is invalid).\n    * @see https://developer.mozilla.org/en-US/docs/Web/CSS/line-height\n    * @since 2.7.0\n    */\nfunction toLineHeight(value, size) {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n  value = +matches[2];\n  switch (matches[3]) {\n    case 'px':\n      return value;\n    case '%':\n      value /= 100;\n      break;\n  }\n  return size * value;\n}\nconst numberOrZero = v => +v || 0;\nfunction _readValueToProps(value, props) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value) ? objProps ? prop => valueOrDefault(value[prop], value[props[prop]]) : prop => value[prop] : () => value;\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\n/**\n * Converts the given value into a TRBL object.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left)\n * @since 3.0.0\n */\nfunction toTRBL(value) {\n  return _readValueToProps(value, {\n    top: 'y',\n    right: 'x',\n    bottom: 'y',\n    left: 'x'\n  });\n}\n/**\n * Converts the given value into a TRBL corners object (similar with css border-radius).\n * @param value - If a number, set the value to all TRBL corner components,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n * @returns The TRBL corner values (topLeft, topRight, bottomLeft, bottomRight)\n * @since 3.0.0\n */\nfunction toTRBLCorners(value) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\n/**\n * Converts the given value into a padding object with pre-computed width/height.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left, width, height)\n * @since 2.7.0\n */\nfunction toPadding(value) {\n  const obj = toTRBL(value);\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n  return obj;\n}\n/**\n * Parses font options and returns the font object.\n * @param options - A object that contains font options to be parsed.\n * @param fallback - A object that contains fallback font options.\n * @return The font object.\n * @private\n */\nfunction toFont(options, fallback) {\n  options = options || {};\n  fallback = fallback || defaults.font;\n  let size = valueOrDefault(options.size, fallback.size);\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = undefined;\n  }\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n  font.string = toFontString(font);\n  return font;\n}\n/**\n * Evaluates the given `inputs` sequentially and returns the first defined value.\n * @param inputs - An array of values, falling back to the last value.\n * @param context - If defined and the current value is a function, the value\n * is called with `context` as first argument and the result becomes the new input.\n * @param index - If defined and the current value is an array, the value\n * at `index` become the new input.\n * @param info - object to return information about resolution in\n * @param info.cacheable - Will be set to `false` if option is not cacheable.\n * @since 2.7.0\n */\nfunction resolve(inputs, context, index, info) {\n  let cacheable = true;\n  let i, ilen, value;\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\n/**\n * @param minmax\n * @param grace\n * @param beginAtZero\n * @private\n */\nfunction _addGrace(minmax, grace, beginAtZero) {\n  const {\n    min,\n    max\n  } = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value, add) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\nfunction createContext(parentContext, context) {\n  return Object.assign(Object.create(parentContext), context);\n}\n\n/**\n * Creates a Proxy for resolving raw values for options.\n * @param scopes - The option scopes to look for values, in resolution order\n * @param prefixes - The prefixes for values, in resolution order.\n * @param rootScopes - The root option scopes\n * @param fallback - Parent scopes fallback\n * @param getTarget - callback for getting the target for changed values\n * @returns Proxy\n * @private\n */\nfunction _createResolver(scopes, prefixes = [''], rootScopes, fallback, getTarget = () => scopes[0]) {\n  const finalRootScopes = rootScopes || scopes;\n  if (typeof fallback === 'undefined') {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: finalRootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: scope => _createResolver([scope, ...scopes], prefixes, finalRootScopes, fallback)\n  };\n  return new Proxy(cache, {\n    /**\n    * A trap for the delete operator.\n    */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete target._keys; // remove cached keys\n      delete scopes[0][prop]; // remove from top level scope\n      return true;\n    },\n    /**\n    * A trap for getting property values.\n    */\n    get(target, prop) {\n      return _cached(target, prop, () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n    /**\n    * A trap for Object.getOwnPropertyDescriptor.\n    * Also used by Object.hasOwnProperty.\n    */\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n    /**\n    * A trap for Object.getPrototypeOf.\n    */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n    /**\n    * A trap for the in operator.\n    */\n    has(target, prop) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n    /**\n    * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n    */\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n    /**\n    * A trap for setting property values.\n    */\n    set(target, prop, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value; // set to top level scope + cache\n      delete target._keys; // remove cached keys\n      return true;\n    }\n  });\n}\n/**\n * Returns an Proxy for resolving option values with context.\n * @param proxy - The Proxy returned by `_createResolver`\n * @param context - Context object for scriptable/indexable options\n * @param subProxy - The proxy provided for scriptable options\n * @param descriptorDefaults - Defaults for descriptors\n * @private\n */\nfunction _attachContext(proxy, context, subProxy, descriptorDefaults) {\n  const cache = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: ctx => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: scope => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    /**\n    * A trap for the delete operator.\n    */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete proxy[prop]; // remove from proxy\n      return true;\n    },\n    /**\n    * A trap for getting property values.\n    */\n    get(target, prop, receiver) {\n      return _cached(target, prop, () => _resolveWithContext(target, prop, receiver));\n    },\n    /**\n    * A trap for Object.getOwnPropertyDescriptor.\n    * Also used by Object.hasOwnProperty.\n    */\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys ? Reflect.has(proxy, prop) ? {\n        enumerable: true,\n        configurable: true\n      } : undefined : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n    /**\n    * A trap for Object.getPrototypeOf.\n    */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n    /**\n    * A trap for the in operator.\n    */\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n    /**\n    * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n    */\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n    /**\n    * A trap for setting property values.\n    */\n    set(target, prop, value) {\n      proxy[prop] = value; // set to proxy\n      delete target[prop]; // remove from cache\n      return true;\n    }\n  });\n}\n/**\n * @private\n */\nfunction _descriptors(proxy, defaults = {\n  scriptable: true,\n  indexable: true\n}) {\n  const {\n    _scriptable = defaults.scriptable,\n    _indexable = defaults.indexable,\n    _allKeys = defaults.allKeys\n  } = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\nconst readKey = (prefix, name) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop, value) => isObject(value) && prop !== 'adapters' && (Object.getPrototypeOf(value) === null || value.constructor === Object);\nfunction _cached(target, prop, resolve) {\n  if (Object.prototype.hasOwnProperty.call(target, prop) || prop === 'constructor') {\n    return target[prop];\n  }\n  const value = resolve();\n  // cache the resolved value\n  target[prop] = value;\n  return value;\n}\nfunction _resolveWithContext(target, prop, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  let value = _proxy[prop]; // resolve from proxy\n  // resolve with context\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    // if the resolved value is an object, create a sub resolver for it\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\nfunction _resolveScriptable(prop, getValue, target, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _stack\n  } = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  let value = getValue(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    // When scriptable option returns an object, create a resolver on that.\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\nfunction _resolveArray(prop, value, target, isIndexable) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  if (typeof _context.index !== 'undefined' && isIndexable(prop)) {\n    return value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    // Array of objects, return array or resolvers\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\nfunction resolveFallback(fallback, prop, value) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\nconst getScope = (key, parent) => key === true ? parent : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\nfunction addScopes(set, parentScopes, key, parentFallback, value) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (typeof fallback !== 'undefined' && fallback !== key && fallback !== parentFallback) {\n        // When we reach the descriptor that defines a new _fallback, return that.\n        // The fallback will resume to that new scope.\n        return fallback;\n      }\n    } else if (scope === false && typeof parentFallback !== 'undefined' && key !== parentFallback) {\n      // Fallback to `false` results to `false`, when falling back to different key.\n      // For example `interaction` from `hover` or `plugins.tooltip` and `animation` from `animations`\n      return null;\n    }\n  }\n  return false;\n}\nfunction createSubResolver(parentScopes, resolver, prop, value) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (typeof fallback !== 'undefined' && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback, () => subGetTarget(resolver, prop, value));\n}\nfunction addScopesFromKey(set, allScopes, key, fallback, item) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\nfunction subGetTarget(resolver, prop, value) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    // For array of objects, the object is used to store updated values\n    return value;\n  }\n  return target || {};\n}\nfunction _resolveWithPrefixes(prop, prefixes, scopes, proxy) {\n  let value;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (typeof value !== 'undefined') {\n      return needsSubResolver(prop, value) ? createSubResolver(scopes, proxy, prop, value) : value;\n    }\n  }\n}\nfunction _resolve(key, scopes) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n  }\n}\nfunction getKeysFromAllScopes(target) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\nfunction resolveKeysFromAllScopes(scopes) {\n  const set = new Set();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\nfunction _parseObjectDataRadialScale(meta, data, start, count) {\n  const {\n    iScale\n  } = meta;\n  const {\n    key = 'r'\n  } = this._parsing;\n  const parsed = new Array(count);\n  let i, ilen, index, item;\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n  return parsed;\n}\nconst EPSILON = Number.EPSILON || 1e-14;\nconst getPoint = (points, i) => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = indexAxis => indexAxis === 'x' ? 'y' : 'x';\nfunction splineCurve(firstPoint, middlePoint, afterPoint, t) {\n  // Props to Rob Spencer at scaled innovation for his post on splining between points\n  // http://scaledinnovation.com/analytics/splines/aboutSplines.html\n  // This function must also respect \"skipped\" points\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n  // If all points are the same, s01 & s02 will be inf\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n  const fa = t * s01; // scaling factor for triangle Ta\n  const fb = t * s12;\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\n/**\n * Adjust tangents to ensure monotonic properties\n */\nfunction monotoneAdjust(points, deltaK, mK) {\n  const pointsLen = points.length;\n  let alphaK, betaK, tauK, squaredMagnitude, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\nfunction monotoneCompute(points, mK, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\n/**\n * This function calculates Bézier control points in a similar way than |splineCurve|,\n * but preserves monotonicity of the provided data and ensures no local extremums are added\n * between the dataset discrete points due to the interpolation.\n * See : https://en.wikipedia.org/wiki/Monotone_cubic_interpolation\n */\nfunction splineCurveMonotone(points, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK = Array(pointsLen).fill(0);\n  const mK = Array(pointsLen);\n  // Calculate slopes (deltaK) and initialize tangents (mK)\n  let i, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n      // In the case of two points that appear at the same x pixel, slopeDeltaX is 0\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i] : !pointAfter ? deltaK[i - 1] : sign(deltaK[i - 1]) !== sign(deltaK[i]) ? 0 : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n  monotoneAdjust(points, deltaK, mK);\n  monotoneCompute(points, mK, indexAxis);\n}\nfunction capControlPoint(pt, min, max) {\n  return Math.max(Math.min(pt, max), min);\n}\nfunction capBezierPoints(points, area) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\n/**\n * @private\n */\nfunction _updateBezierControlPoints(points, options, area, loop, indexAxis) {\n  let i, ilen, point, controlPoints;\n  // Only consider points that are drawn in case the spanGaps option is used\n  if (options.spanGaps) {\n    points = points.filter(pt => !pt.skip);\n  }\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(prev, point, points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen], options.tension);\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n\n/**\n * @private\n */\nfunction _isDomSupported() {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n/**\n * @private\n */\nfunction _getParentNode(domNode) {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = parent.host;\n  }\n  return parent;\n}\n/**\n * convert max-width/max-height values that may be percentages into a number\n * @private\n */\nfunction parseMaxStyle(styleValue, node, parentProperty) {\n  let valueInPixels;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n    if (styleValue.indexOf('%') !== -1) {\n      // percentage * size in dimension\n      valueInPixels = valueInPixels / 100 * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n  return valueInPixels;\n}\nconst getComputedStyle = element => element.ownerDocument.defaultView.getComputedStyle(element, null);\nfunction getStyle(el, property) {\n  return getComputedStyle(el).getPropertyValue(property);\n}\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles, style, suffix) {\n  const result = {};\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\nconst useOffsetPos = (x, y, target) => (x > 0 || y > 0) && (!target || !target.shadowRoot);\n/**\n * @param e\n * @param canvas\n * @returns Canvas position\n */\nfunction getCanvasPosition(e, canvas) {\n  const touches = e.touches;\n  const source = touches && touches.length ? touches[0] : e;\n  const {\n    offsetX,\n    offsetY\n  } = source;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {\n    x,\n    y,\n    box\n  };\n}\n/**\n * Gets an event's x, y coordinates, relative to the chart area\n * @param event\n * @param chart\n * @returns x and y coordinates of the event\n */\nfunction getRelativePosition(event, chart) {\n  if ('native' in event) {\n    return event;\n  }\n  const {\n    canvas,\n    currentDevicePixelRatio\n  } = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {\n    x,\n    y,\n    box\n  } = getCanvasPosition(event, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n  let {\n    width,\n    height\n  } = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\nfunction getContainerSize(canvas, width, height) {\n  let maxWidth, maxHeight;\n  if (width === undefined || height === undefined) {\n    const container = canvas && _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect(); // this is the border box of the container\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\nconst round1 = v => Math.round(v * 10) / 10;\n// eslint-disable-next-line complexity\nfunction getMaximumSize(canvas, bbWidth, bbHeight, aspectRatio) {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {\n    width,\n    height\n  } = containerSize;\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? width / aspectRatio : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    // https://github.com/chartjs/Chart.js/issues/4659\n    // If the canvas has width, but no height, default to aspectRatio of 2 (canvas default)\n    height = round1(width / 2);\n  }\n  const maintainHeight = bbWidth !== undefined || bbHeight !== undefined;\n  if (maintainHeight && aspectRatio && containerSize.height && height > containerSize.height) {\n    height = containerSize.height;\n    width = round1(Math.floor(height * aspectRatio));\n  }\n  return {\n    width,\n    height\n  };\n}\n/**\n * @param chart\n * @param forceRatio\n * @param forceStyle\n * @returns True if the canvas context size or transformation has changed.\n */\nfunction retinaScale(chart, forceRatio, forceStyle) {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n  chart.height = Math.floor(chart.height);\n  chart.width = Math.floor(chart.width);\n  const canvas = chart.canvas;\n  // If no style has been set on the canvas, the render size is used as display size,\n  // making the chart visually bigger, so let's enforce it to the \"correct\" values.\n  // See https://github.com/chartjs/Chart.js/issues/3575\n  if (canvas.style && (forceStyle || !canvas.style.height && !canvas.style.width)) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n  if (chart.currentDevicePixelRatio !== pixelRatio || canvas.height !== deviceHeight || canvas.width !== deviceWidth) {\n    chart.currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\n/**\n * Detects support for options object argument in addEventListener.\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n * @private\n */\nconst supportsEventListenerOptions = function () {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() {\n        passiveSupported = true;\n        return false;\n      }\n    };\n    if (_isDomSupported()) {\n      window.addEventListener('test', null, options);\n      window.removeEventListener('test', null, options);\n    }\n  } catch (e) {\n    // continue regardless of error\n  }\n  return passiveSupported;\n}();\n/**\n * The \"used\" size is the final value of a dimension property after all calculations have\n * been performed. This method uses the computed style of `element` but returns undefined\n * if the computed style is not expressed in pixels. That can happen in some cases where\n * `element` has a size relative to its parent and this last one is not yet displayed,\n * for example because of `display: none` on a parent node.\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n * @returns Size in pixels or undefined if unknown.\n */\nfunction readUsedSize(element, property) {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n\n/**\n * @private\n */\nfunction _pointInLine(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\n/**\n * @private\n */\nfunction _steppedInterpolation(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y : mode === 'after' ? t < 1 ? p1.y : p2.y : t > 0 ? p2.y : p1.y\n  };\n}\n/**\n * @private\n */\nfunction _bezierInterpolation(p1, p2, t, mode) {\n  const cp1 = {\n    x: p1.cp2x,\n    y: p1.cp2y\n  };\n  const cp2 = {\n    x: p2.cp1x,\n    y: p2.cp1y\n  };\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\nconst getRightToLeftAdapter = function (rectX, width) {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    }\n  };\n};\nconst getLeftToRightAdapter = function () {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) {},\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) {\n      return x;\n    }\n  };\n};\nfunction getRtlAdapter(rtl, rectX, width) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\nfunction overrideTextDirection(ctx, direction) {\n  let style, original;\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [style.getPropertyValue('direction'), style.getPropertyPriority('direction')];\n    style.setProperty('direction', direction, 'important');\n    ctx.prevTextDirection = original;\n  }\n}\nfunction restoreTextDirection(ctx, original) {\n  if (original !== undefined) {\n    delete ctx.prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\nfunction normalizeSegment({\n  start,\n  end,\n  count,\n  loop,\n  style\n}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\nfunction getSegment(segment, points, bounds) {\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const {\n    between,\n    normalize\n  } = propertyFn(property);\n  const count = points.length;\n  let {\n    start,\n    end,\n    loop\n  } = segment;\n  let i, ilen;\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n  if (end < start) {\n    end += count;\n  }\n  return {\n    start,\n    end,\n    loop,\n    style: segment.style\n  };\n}\nfunction _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const count = points.length;\n  const {\n    compare,\n    between,\n    normalize\n  } = propertyFn(property);\n  const {\n    start,\n    end,\n    loop,\n    style\n  } = getSegment(segment, points, bounds);\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n    if (point.skip) {\n      continue;\n    }\n    value = normalize(point[property]);\n    if (value === prevValue) {\n      continue;\n    }\n    inside = between(value, startBound, endBound);\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({\n        start: subStart,\n        end: i,\n        loop,\n        count,\n        style\n      }));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n  if (subStart !== null) {\n    result.push(normalizeSegment({\n      start: subStart,\n      end,\n      loop,\n      count,\n      style\n    }));\n  }\n  return result;\n}\nfunction _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n  if (loop && !spanGaps) {\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n  while (start < count && points[start].skip) {\n    start++;\n  }\n  start %= count;\n  if (loop) {\n    end += start;\n  }\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n  end %= count;\n  return {\n    start,\n    end\n  };\n}\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({\n          start: start % count,\n          end: (end - 1) % count,\n          loop\n        });\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n  if (last !== null) {\n    result.push({\n      start: start % count,\n      end: last % count,\n      loop\n    });\n  }\n  return result;\n}\nfunction _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n  if (!count) {\n    return [];\n  }\n  const loop = !!line._loop;\n  const {\n    start,\n    end\n  } = findStartAndEnd(points, count, loop, spanGaps);\n  if (spanGaps === true) {\n    return splitByStyles(line, [{\n      start,\n      end,\n      loop\n    }], points, segmentOptions);\n  }\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {\n    _datasetIndex: datasetIndex,\n    options: {\n      spanGaps\n    }\n  } = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({\n        start: s % count,\n        end: e % count,\n        loop: l,\n        style: st\n      });\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n  return result;\n}\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\nfunction styleChanged(style, prevStyle) {\n  if (!prevStyle) {\n    return false;\n  }\n  const cache = [];\n  const replacer = function (key, value) {\n    if (!isPatternOrGradient(value)) {\n      return value;\n    }\n    if (!cache.includes(value)) {\n      cache.push(value);\n    }\n    return cache.indexOf(value);\n  };\n  return JSON.stringify(style, replacer) !== JSON.stringify(prevStyle, replacer);\n}\nfunction getSizeForArea(scale, chartArea, field) {\n  return scale.options.clip ? scale[field] : chartArea[field];\n}\nfunction getDatasetArea(meta, chartArea) {\n  const {\n    xScale,\n    yScale\n  } = meta;\n  if (xScale && yScale) {\n    return {\n      left: getSizeForArea(xScale, chartArea, 'left'),\n      right: getSizeForArea(xScale, chartArea, 'right'),\n      top: getSizeForArea(yScale, chartArea, 'top'),\n      bottom: getSizeForArea(yScale, chartArea, 'bottom')\n    };\n  }\n  return chartArea;\n}\nfunction getDatasetClipArea(chart, meta) {\n  const clip = meta._clip;\n  if (clip.disabled) {\n    return false;\n  }\n  const area = getDatasetArea(meta, chart.chartArea);\n  return {\n    left: clip.left === false ? 0 : area.left - (clip.left === true ? 0 : clip.left),\n    right: clip.right === false ? chart.width : area.right + (clip.right === true ? 0 : clip.right),\n    top: clip.top === false ? 0 : area.top - (clip.top === true ? 0 : clip.top),\n    bottom: clip.bottom === false ? chart.height : area.bottom + (clip.bottom === true ? 0 : clip.bottom)\n  };\n}\nexport { unclipArea as $, _rlookupByKey as A, _lookupByKey as B, _isPointInArea as C, getAngleFromPoint as D, toPadding as E, each as F, getMaximumSize as G, HALF_PI as H, _getParentNode as I, readUsedSize as J, supportsEventListenerOptions as K, throttled as L, _isDomSupported as M, _factorize as N, finiteOrDefault as O, PI as P, callback as Q, _addGrace as R, _limitValue as S, TAU as T, toDegrees as U, _measureText as V, _int16Range as W, _alignPixel as X, clipArea as Y, renderText as Z, _arrayUnique as _, resolve as a, getStyle as a$, toFont as a0, _toLeftRightCenter as a1, _alignStartEnd as a2, overrides as a3, merge as a4, _capitalize as a5, descriptors as a6, isFunction as a7, _attachContext as a8, _createResolver as a9, getRtlAdapter as aA, overrideTextDirection as aB, _textX as aC, restoreTextDirection as aD, drawPointLegend as aE, distanceBetweenPoints as aF, noop as aG, _setMinAndMaxByKey as aH, niceNum as aI, almostWhole as aJ, almostEquals as aK, _decimalPlaces as aL, Ticks as aM, log10 as aN, _longestText as aO, _filterBetween as aP, _lookup as aQ, isPatternOrGradient as aR, getHoverColor as aS, clone as aT, _merger as aU, _mergerIf as aV, _deprecated as aW, _splitKey as aX, toFontString as aY, splineCurve as aZ, splineCurveMonotone as a_, _descriptors as aa, mergeIf as ab, uid as ac, debounce as ad, retinaScale as ae, clearCanvas as af, setsEqual as ag, getDatasetClipArea as ah, _elementsEqual as ai, _isClickEvent as aj, _isBetween as ak, _readValueToProps as al, _updateBezierControlPoints as am, _computeSegments as an, _boundSegments as ao, _steppedInterpolation as ap, _bezierInterpolation as aq, _pointInLine as ar, _steppedLineTo as as, _bezierCurveTo as at, drawPoint as au, addRoundedRectPath as av, toTRBL as aw, toTRBLCorners as ax, _boundSegment as ay, _normalizeAngle as az, isArray as b, fontString as b0, toLineHeight as b1, PITAU as b2, INFINITY as b3, RAD_PER_DEG as b4, QUARTER_PI as b5, TWO_THIRDS_PI as b6, _angleDiff as b7, color as c, defaults as d, effects as e, resolveObjectKey as f, isNumberFinite as g, defined as h, isObject as i, createContext as j, isNullOrUndef as k, listenArrayEvents as l, toPercentage as m, toDimension as n, formatNumber as o, _angleBetween as p, _getStartAndCountOfVisiblePoints as q, requestAnimFrame as r, sign as s, toRadians as t, unlistenArrayEvents as u, valueOrDefault as v, _scaleRangesChanged as w, isNumber as x, _parseObjectDataRadialScale as y, getRelativePosition as z };\n//# sourceMappingURL=helpers.dataset.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}