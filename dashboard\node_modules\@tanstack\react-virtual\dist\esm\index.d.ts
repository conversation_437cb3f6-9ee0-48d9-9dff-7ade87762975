import { Virtualizer, PartialKeys, VirtualizerOptions } from '@tanstack/virtual-core';
export * from '@tanstack/virtual-core';
export declare function useVirtualizer<TScrollElement extends Element, TItemElement extends Element>(options: PartialKeys<VirtualizerOptions<TScrollElement, TItemElement>, 'observeElementRect' | 'observeElementOffset' | 'scrollToFn'>): Virtualizer<TScrollElement, TItemElement>;
export declare function useWindowVirtualizer<TItemElement extends Element>(options: PartialKeys<VirtualizerOptions<Window, TItemElement>, 'getScrollElement' | 'observeElementRect' | 'observeElementOffset' | 'scrollToFn'>): Virtualizer<Window, TItemElement>;
