"""
Database models for ingest service.
"""
from sqlalchemy import Column, Integer, String, Float, DateTime
from sqlalchemy.ext.declarative import declarative_base
import datetime

Base = declarative_base()


class Transaction(Base):
    """Transaction model for storing scored transactions"""
    __tablename__ = "transactions"

    id = Column(Integer, primary_key=True)
    transaction_id = Column(String(100), nullable=False, unique=True, index=True)
    step = Column(Integer, nullable=False)
    type = Column(String(20), nullable=False)
    amount = Column(Float, nullable=False)
    name_orig = Column(String(100), nullable=False)
    old_balance_orig = Column(Float, nullable=False)
    new_balance_orig = Column(Float, nullable=False)
    name_dest = Column(String(100), nullable=False)
    old_balance_dest = Column(Float, nullable=False)
    new_balance_dest = Column(Float, nullable=False)
    risk_score = Column(Float, nullable=False)
    is_fraud = Column(Integer, nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
