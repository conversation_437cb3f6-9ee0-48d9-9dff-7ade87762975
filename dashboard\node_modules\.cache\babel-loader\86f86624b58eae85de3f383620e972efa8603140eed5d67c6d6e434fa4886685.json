{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\components\\\\RiskHeatmap.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';\nimport { Pie } from 'react-chartjs-2';\nimport '../styles/RiskHeatmap.css';\n\n// Register ChartJS components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(ArcElement, Tooltip, Legend);\nconst RiskHeatmap = ({\n  transactions\n}) => {\n  _s();\n  const [heatmapData, setHeatmapData] = useState({\n    hourly: Array(24).fill(0).map(() => ({\n      total: 0,\n      high: 0,\n      medium: 0,\n      low: 0\n    })),\n    daily: Array(7).fill(0).map(() => ({\n      total: 0,\n      high: 0,\n      medium: 0,\n      low: 0\n    }))\n  });\n  const [view, setView] = useState('hourly');\n  useEffect(() => {\n    if (!transactions || transactions.length === 0) return;\n    const hourlyData = Array(24).fill(0).map(() => ({\n      total: 0,\n      high: 0,\n      medium: 0,\n      low: 0\n    }));\n    const dailyData = Array(7).fill(0).map(() => ({\n      total: 0,\n      high: 0,\n      medium: 0,\n      low: 0\n    }));\n    transactions.forEach(transaction => {\n      if (!transaction.timestamp) return;\n      const date = new Date(transaction.timestamp);\n      const hour = date.getHours();\n      const day = date.getDay();\n\n      // Update hourly data\n      hourlyData[hour].total += 1;\n      if (transaction.risk_score >= 0.8) {\n        hourlyData[hour].high += 1;\n      } else if (transaction.risk_score >= 0.5) {\n        hourlyData[hour].medium += 1;\n      } else {\n        hourlyData[hour].low += 1;\n      }\n\n      // Update daily data\n      dailyData[day].total += 1;\n      if (transaction.risk_score >= 0.8) {\n        dailyData[day].high += 1;\n      } else if (transaction.risk_score >= 0.5) {\n        dailyData[day].medium += 1;\n      } else {\n        dailyData[day].low += 1;\n      }\n    });\n    setHeatmapData({\n      hourly: hourlyData,\n      daily: dailyData\n    });\n  }, [transactions]);\n  const getChartData = () => {\n    const data = view === 'hourly' ? heatmapData.hourly : heatmapData.daily;\n    return {\n      labels: ['High Risk', 'Medium Risk', 'Low Risk'],\n      datasets: [{\n        data: [data.reduce((sum, item) => sum + item.high, 0), data.reduce((sum, item) => sum + item.medium, 0), data.reduce((sum, item) => sum + item.low, 0)],\n        backgroundColor: ['rgba(255, 99, 132, 0.7)', 'rgba(255, 206, 86, 0.7)', 'rgba(75, 192, 192, 0.7)'],\n        borderColor: ['rgba(255, 99, 132, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)'],\n        borderWidth: 1\n      }]\n    };\n  };\n  const renderHeatmap = () => {\n    const data = view === 'hourly' ? heatmapData.hourly : heatmapData.daily;\n    const labels = view === 'hourly' ? Array(24).fill(0).map((_, i) => `${i}:00`) : ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"heatmap-grid\",\n      children: data.map((item, index) => {\n        const total = item.total || 1; // Avoid division by zero\n        const highPercent = item.high / total * 100;\n        const mediumPercent = item.medium / total * 100;\n        const lowPercent = item.low / total * 100;\n\n        // Calculate intensity for cell color\n        const intensity = Math.min(item.total / 5, 1); // Normalize by 5 transactions\n\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"heatmap-cell\",\n          style: {\n            backgroundColor: `rgba(0, 0, 0, ${intensity * 0.1})`,\n            borderColor: item.high > 0 ? 'rgba(255, 99, 132, 0.5)' : 'transparent'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cell-label\",\n            children: labels[index]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cell-count\",\n            children: item.total\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 15\n          }, this), item.total > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"risk-bars\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"risk-bar high\",\n              style: {\n                width: `${highPercent}%`\n              },\n              title: `High Risk: ${item.high}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"risk-bar medium\",\n              style: {\n                width: `${mediumPercent}%`\n              },\n              title: `Medium Risk: ${item.medium}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"risk-bar low\",\n              style: {\n                width: `${lowPercent}%`\n              },\n              title: `Low Risk: ${item.low}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"risk-heatmap-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"heatmap-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `view-button ${view === 'hourly' ? 'active' : ''}`,\n        onClick: () => setView('hourly'),\n        children: \"By Hour\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `view-button ${view === 'daily' ? 'active' : ''}`,\n        onClick: () => setView('daily'),\n        children: \"By Day\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"heatmap-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"heatmap-chart\",\n        children: renderHeatmap()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"risk-distribution\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Risk Distribution\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pie-chart-container\",\n          children: /*#__PURE__*/_jsxDEV(Pie, {\n            data: getChartData(),\n            options: {\n              maintainAspectRatio: false\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 137,\n    columnNumber: 5\n  }, this);\n};\n_s(RiskHeatmap, \"nDkB++/1XJNlTkhRjRQNzGebzYY=\");\n_c = RiskHeatmap;\nexport default RiskHeatmap;\nvar _c;\n$RefreshReg$(_c, \"RiskHeatmap\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Chart", "ChartJS", "ArcElement", "<PERSON><PERSON><PERSON>", "Legend", "Pie", "jsxDEV", "_jsxDEV", "register", "RiskHeatmap", "transactions", "_s", "heatmapData", "setHeatmapData", "hourly", "Array", "fill", "map", "total", "high", "medium", "low", "daily", "view", "<PERSON><PERSON><PERSON><PERSON>", "length", "hourlyData", "dailyData", "for<PERSON>ach", "transaction", "timestamp", "date", "Date", "hour", "getHours", "day", "getDay", "risk_score", "getChartData", "data", "labels", "datasets", "reduce", "sum", "item", "backgroundColor", "borderColor", "borderWidth", "renderHeatmap", "_", "i", "className", "children", "index", "highPercent", "mediumPercent", "lowPercent", "intensity", "Math", "min", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "title", "onClick", "options", "maintainAspectRatio", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/components/RiskHeatmap.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';\nimport { Pie } from 'react-chartjs-2';\nimport '../styles/RiskHeatmap.css';\n\n// Register ChartJS components\nChartJS.register(ArcE<PERSON>, Tooltip, Legend);\n\nconst RiskHeatmap = ({ transactions }) => {\n  const [heatmapData, setHeatmapData] = useState({\n    hourly: Array(24).fill(0).map(() => ({ total: 0, high: 0, medium: 0, low: 0 })),\n    daily: Array(7).fill(0).map(() => ({ total: 0, high: 0, medium: 0, low: 0 }))\n  });\n  \n  const [view, setView] = useState('hourly');\n  \n  useEffect(() => {\n    if (!transactions || transactions.length === 0) return;\n    \n    const hourlyData = Array(24).fill(0).map(() => ({ total: 0, high: 0, medium: 0, low: 0 }));\n    const dailyData = Array(7).fill(0).map(() => ({ total: 0, high: 0, medium: 0, low: 0 }));\n    \n    transactions.forEach(transaction => {\n      if (!transaction.timestamp) return;\n      \n      const date = new Date(transaction.timestamp);\n      const hour = date.getHours();\n      const day = date.getDay();\n      \n      // Update hourly data\n      hourlyData[hour].total += 1;\n      if (transaction.risk_score >= 0.8) {\n        hourlyData[hour].high += 1;\n      } else if (transaction.risk_score >= 0.5) {\n        hourlyData[hour].medium += 1;\n      } else {\n        hourlyData[hour].low += 1;\n      }\n      \n      // Update daily data\n      dailyData[day].total += 1;\n      if (transaction.risk_score >= 0.8) {\n        dailyData[day].high += 1;\n      } else if (transaction.risk_score >= 0.5) {\n        dailyData[day].medium += 1;\n      } else {\n        dailyData[day].low += 1;\n      }\n    });\n    \n    setHeatmapData({ hourly: hourlyData, daily: dailyData });\n  }, [transactions]);\n  \n  const getChartData = () => {\n    const data = view === 'hourly' ? heatmapData.hourly : heatmapData.daily;\n    \n    return {\n      labels: ['High Risk', 'Medium Risk', 'Low Risk'],\n      datasets: [\n        {\n          data: [\n            data.reduce((sum, item) => sum + item.high, 0),\n            data.reduce((sum, item) => sum + item.medium, 0),\n            data.reduce((sum, item) => sum + item.low, 0)\n          ],\n          backgroundColor: [\n            'rgba(255, 99, 132, 0.7)',\n            'rgba(255, 206, 86, 0.7)',\n            'rgba(75, 192, 192, 0.7)'\n          ],\n          borderColor: [\n            'rgba(255, 99, 132, 1)',\n            'rgba(255, 206, 86, 1)',\n            'rgba(75, 192, 192, 1)'\n          ],\n          borderWidth: 1\n        }\n      ]\n    };\n  };\n  \n  const renderHeatmap = () => {\n    const data = view === 'hourly' ? heatmapData.hourly : heatmapData.daily;\n    const labels = view === 'hourly' \n      ? Array(24).fill(0).map((_, i) => `${i}:00`)\n      : ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\n    \n    return (\n      <div className=\"heatmap-grid\">\n        {data.map((item, index) => {\n          const total = item.total || 1; // Avoid division by zero\n          const highPercent = (item.high / total) * 100;\n          const mediumPercent = (item.medium / total) * 100;\n          const lowPercent = (item.low / total) * 100;\n          \n          // Calculate intensity for cell color\n          const intensity = Math.min(item.total / 5, 1); // Normalize by 5 transactions\n          \n          return (\n            <div \n              key={index} \n              className=\"heatmap-cell\"\n              style={{\n                backgroundColor: `rgba(0, 0, 0, ${intensity * 0.1})`,\n                borderColor: item.high > 0 ? 'rgba(255, 99, 132, 0.5)' : 'transparent'\n              }}\n            >\n              <div className=\"cell-label\">{labels[index]}</div>\n              <div className=\"cell-count\">{item.total}</div>\n              {item.total > 0 && (\n                <div className=\"risk-bars\">\n                  <div \n                    className=\"risk-bar high\" \n                    style={{ width: `${highPercent}%` }}\n                    title={`High Risk: ${item.high}`}\n                  ></div>\n                  <div \n                    className=\"risk-bar medium\" \n                    style={{ width: `${mediumPercent}%` }}\n                    title={`Medium Risk: ${item.medium}`}\n                  ></div>\n                  <div \n                    className=\"risk-bar low\" \n                    style={{ width: `${lowPercent}%` }}\n                    title={`Low Risk: ${item.low}`}\n                  ></div>\n                </div>\n              )}\n            </div>\n          );\n        })}\n      </div>\n    );\n  };\n  \n  return (\n    <div className=\"risk-heatmap-container\">\n      <div className=\"heatmap-controls\">\n        <button \n          className={`view-button ${view === 'hourly' ? 'active' : ''}`}\n          onClick={() => setView('hourly')}\n        >\n          By Hour\n        </button>\n        <button \n          className={`view-button ${view === 'daily' ? 'active' : ''}`}\n          onClick={() => setView('daily')}\n        >\n          By Day\n        </button>\n      </div>\n      \n      <div className=\"heatmap-content\">\n        <div className=\"heatmap-chart\">\n          {renderHeatmap()}\n        </div>\n        \n        <div className=\"risk-distribution\">\n          <h3>Risk Distribution</h3>\n          <div className=\"pie-chart-container\">\n            <Pie data={getChartData()} options={{ maintainAspectRatio: false }} />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RiskHeatmap;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,IAAIC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,MAAM,QAAQ,UAAU;AACxE,SAASC,GAAG,QAAQ,iBAAiB;AACrC,OAAO,2BAA2B;;AAElC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAN,OAAO,CAACO,QAAQ,CAACN,UAAU,EAAEC,OAAO,EAAEC,MAAM,CAAC;AAE7C,MAAMK,WAAW,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC;IAC7CgB,MAAM,EAAEC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,OAAO;MAAEC,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC,CAAC,CAAC;IAC/EC,KAAK,EAAEP,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,OAAO;MAAEC,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC,CAAC;EAC9E,CAAC,CAAC;EAEF,MAAM,CAACE,IAAI,EAAEC,OAAO,CAAC,GAAG1B,QAAQ,CAAC,QAAQ,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACd,IAAI,CAACW,YAAY,IAAIA,YAAY,CAACe,MAAM,KAAK,CAAC,EAAE;IAEhD,MAAMC,UAAU,GAAGX,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,OAAO;MAAEC,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC,CAAC,CAAC;IAC1F,MAAMM,SAAS,GAAGZ,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,OAAO;MAAEC,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC,CAAC,CAAC;IAExFX,YAAY,CAACkB,OAAO,CAACC,WAAW,IAAI;MAClC,IAAI,CAACA,WAAW,CAACC,SAAS,EAAE;MAE5B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACH,WAAW,CAACC,SAAS,CAAC;MAC5C,MAAMG,IAAI,GAAGF,IAAI,CAACG,QAAQ,CAAC,CAAC;MAC5B,MAAMC,GAAG,GAAGJ,IAAI,CAACK,MAAM,CAAC,CAAC;;MAEzB;MACAV,UAAU,CAACO,IAAI,CAAC,CAACf,KAAK,IAAI,CAAC;MAC3B,IAAIW,WAAW,CAACQ,UAAU,IAAI,GAAG,EAAE;QACjCX,UAAU,CAACO,IAAI,CAAC,CAACd,IAAI,IAAI,CAAC;MAC5B,CAAC,MAAM,IAAIU,WAAW,CAACQ,UAAU,IAAI,GAAG,EAAE;QACxCX,UAAU,CAACO,IAAI,CAAC,CAACb,MAAM,IAAI,CAAC;MAC9B,CAAC,MAAM;QACLM,UAAU,CAACO,IAAI,CAAC,CAACZ,GAAG,IAAI,CAAC;MAC3B;;MAEA;MACAM,SAAS,CAACQ,GAAG,CAAC,CAACjB,KAAK,IAAI,CAAC;MACzB,IAAIW,WAAW,CAACQ,UAAU,IAAI,GAAG,EAAE;QACjCV,SAAS,CAACQ,GAAG,CAAC,CAAChB,IAAI,IAAI,CAAC;MAC1B,CAAC,MAAM,IAAIU,WAAW,CAACQ,UAAU,IAAI,GAAG,EAAE;QACxCV,SAAS,CAACQ,GAAG,CAAC,CAACf,MAAM,IAAI,CAAC;MAC5B,CAAC,MAAM;QACLO,SAAS,CAACQ,GAAG,CAAC,CAACd,GAAG,IAAI,CAAC;MACzB;IACF,CAAC,CAAC;IAEFR,cAAc,CAAC;MAAEC,MAAM,EAAEY,UAAU;MAAEJ,KAAK,EAAEK;IAAU,CAAC,CAAC;EAC1D,CAAC,EAAE,CAACjB,YAAY,CAAC,CAAC;EAElB,MAAM4B,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,IAAI,GAAGhB,IAAI,KAAK,QAAQ,GAAGX,WAAW,CAACE,MAAM,GAAGF,WAAW,CAACU,KAAK;IAEvE,OAAO;MACLkB,MAAM,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,UAAU,CAAC;MAChDC,QAAQ,EAAE,CACR;QACEF,IAAI,EAAE,CACJA,IAAI,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACzB,IAAI,EAAE,CAAC,CAAC,EAC9CoB,IAAI,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACxB,MAAM,EAAE,CAAC,CAAC,EAChDmB,IAAI,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACvB,GAAG,EAAE,CAAC,CAAC,CAC9C;QACDwB,eAAe,EAAE,CACf,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,CAC1B;QACDC,WAAW,EAAE,CACX,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,CACxB;QACDC,WAAW,EAAE;MACf,CAAC;IAEL,CAAC;EACH,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMT,IAAI,GAAGhB,IAAI,KAAK,QAAQ,GAAGX,WAAW,CAACE,MAAM,GAAGF,WAAW,CAACU,KAAK;IACvE,MAAMkB,MAAM,GAAGjB,IAAI,KAAK,QAAQ,GAC5BR,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACgC,CAAC,EAAEC,CAAC,KAAK,GAAGA,CAAC,KAAK,CAAC,GAC1C,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAErD,oBACE3C,OAAA;MAAK4C,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1Bb,IAAI,CAACtB,GAAG,CAAC,CAAC2B,IAAI,EAAES,KAAK,KAAK;QACzB,MAAMnC,KAAK,GAAG0B,IAAI,CAAC1B,KAAK,IAAI,CAAC,CAAC,CAAC;QAC/B,MAAMoC,WAAW,GAAIV,IAAI,CAACzB,IAAI,GAAGD,KAAK,GAAI,GAAG;QAC7C,MAAMqC,aAAa,GAAIX,IAAI,CAACxB,MAAM,GAAGF,KAAK,GAAI,GAAG;QACjD,MAAMsC,UAAU,GAAIZ,IAAI,CAACvB,GAAG,GAAGH,KAAK,GAAI,GAAG;;QAE3C;QACA,MAAMuC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACf,IAAI,CAAC1B,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAE/C,oBACEX,OAAA;UAEE4C,SAAS,EAAC,cAAc;UACxBS,KAAK,EAAE;YACLf,eAAe,EAAE,iBAAiBY,SAAS,GAAG,GAAG,GAAG;YACpDX,WAAW,EAAEF,IAAI,CAACzB,IAAI,GAAG,CAAC,GAAG,yBAAyB,GAAG;UAC3D,CAAE;UAAAiC,QAAA,gBAEF7C,OAAA;YAAK4C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEZ,MAAM,CAACa,KAAK;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjDzD,OAAA;YAAK4C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAER,IAAI,CAAC1B;UAAK;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC7CpB,IAAI,CAAC1B,KAAK,GAAG,CAAC,iBACbX,OAAA;YAAK4C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7C,OAAA;cACE4C,SAAS,EAAC,eAAe;cACzBS,KAAK,EAAE;gBAAEK,KAAK,EAAE,GAAGX,WAAW;cAAI,CAAE;cACpCY,KAAK,EAAE,cAActB,IAAI,CAACzB,IAAI;YAAG;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACPzD,OAAA;cACE4C,SAAS,EAAC,iBAAiB;cAC3BS,KAAK,EAAE;gBAAEK,KAAK,EAAE,GAAGV,aAAa;cAAI,CAAE;cACtCW,KAAK,EAAE,gBAAgBtB,IAAI,CAACxB,MAAM;YAAG;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACPzD,OAAA;cACE4C,SAAS,EAAC,cAAc;cACxBS,KAAK,EAAE;gBAAEK,KAAK,EAAE,GAAGT,UAAU;cAAI,CAAE;cACnCU,KAAK,EAAE,aAAatB,IAAI,CAACvB,GAAG;YAAG;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA,GA3BIX,KAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4BP,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,oBACEzD,OAAA;IAAK4C,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBACrC7C,OAAA;MAAK4C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B7C,OAAA;QACE4C,SAAS,EAAE,eAAe5B,IAAI,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC9D4C,OAAO,EAAEA,CAAA,KAAM3C,OAAO,CAAC,QAAQ,CAAE;QAAA4B,QAAA,EAClC;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTzD,OAAA;QACE4C,SAAS,EAAE,eAAe5B,IAAI,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC7D4C,OAAO,EAAEA,CAAA,KAAM3C,OAAO,CAAC,OAAO,CAAE;QAAA4B,QAAA,EACjC;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENzD,OAAA;MAAK4C,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B7C,OAAA;QAAK4C,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BJ,aAAa,CAAC;MAAC;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAENzD,OAAA;QAAK4C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC7C,OAAA;UAAA6C,QAAA,EAAI;QAAiB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BzD,OAAA;UAAK4C,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClC7C,OAAA,CAACF,GAAG;YAACkC,IAAI,EAAED,YAAY,CAAC,CAAE;YAAC8B,OAAO,EAAE;cAAEC,mBAAmB,EAAE;YAAM;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrD,EAAA,CA9JIF,WAAW;AAAA6D,EAAA,GAAX7D,WAAW;AAgKjB,eAAeA,WAAW;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}