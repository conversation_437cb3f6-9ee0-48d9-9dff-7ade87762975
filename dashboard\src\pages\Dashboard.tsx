import React, { useState, useEffect, useRef, useCallback } from 'react';
import TransactionTable from '../components/TransactionTable';
import RiskHeatmap from '../components/RiskHeatmap';
import CaseDrawer from '../components/CaseDrawer';
import { useWebSocket } from '../services/WebSocketContext';
import { fetchLatestTransactions } from '../services/api';
import { Transaction, TransactionWebSocketMessage } from '../types';
import '../styles/Dashboard.css';

const Dashboard: React.FC = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { lastMessage } = useWebSocket();
  const transactionsRef = useRef<Transaction[]>([]);

  useEffect(() => {
    // Load initial transactions
    const loadTransactions = async (): Promise<void> => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await fetchLatestTransactions(100);
        setTransactions(data);
        transactionsRef.current = data;
      } catch (error) {
        console.error('Error fetching transactions:', error);
        setError('Failed to load transactions');
      } finally {
        setIsLoading(false);
      }
    };

    loadTransactions();
  }, []);

  useEffect(() => {
    // Handle new transactions from WebSocket
    if (lastMessage) {
      try {
        const messageData = JSON.parse(lastMessage) as TransactionWebSocketMessage;
        if (messageData.type === 'transaction' && messageData.data?.transaction && messageData.data?.risk_score !== undefined) {
          const newTransaction: Transaction = {
            ...messageData.data.transaction,
            risk_score: messageData.data.risk_score,
            timestamp: messageData.data.timestamp
          };

          // Add to beginning of array and limit size
          const updatedTransactions = [newTransaction, ...transactionsRef.current].slice(0, 1000);
          setTransactions(updatedTransactions);
          transactionsRef.current = updatedTransactions;
        }
      } catch (error) {
        console.error('Error processing WebSocket message:', error);
      }
    }
  }, [lastMessage]);

  const handleTransactionSelect = useCallback((transaction: Transaction): void => {
    setSelectedTransaction(transaction);
    setIsDrawerOpen(true);
  }, []);

  const handleDrawerClose = useCallback((): void => {
    setIsDrawerOpen(false);
  }, []);

  // Calculate statistics
  const totalTransactions = transactions.length;
  const highRiskCount = transactions.filter(t => (t.risk_score || 0) >= 0.8).length;
  const mediumRiskCount = transactions.filter(t => (t.risk_score || 0) >= 0.5 && (t.risk_score || 0) < 0.8).length;

  if (error) {
    return (
      <div className="dashboard-container">
        <div className="error-message">
          <h2>Error Loading Dashboard</h2>
          <p>{error}</p>
          <button onClick={() => window.location.reload()}>Retry</button>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <h1>Fraud Detection Dashboard</h1>
        <div className="dashboard-stats">
          <div className="stat-card">
            <h3>Total Transactions</h3>
            <p>{totalTransactions}</p>
          </div>
          <div className="stat-card">
            <h3>High Risk</h3>
            <p>{highRiskCount}</p>
          </div>
          <div className="stat-card">
            <h3>Medium Risk</h3>
            <p>{mediumRiskCount}</p>
          </div>
        </div>
      </div>

      <div className="dashboard-content">
        <div className="dashboard-main">
          <div className="card transaction-table-card">
            <h2>Live Transaction Feed</h2>
            <TransactionTable
              transactions={transactions}
              onSelectTransaction={handleTransactionSelect}
              isLoading={isLoading}
            />
          </div>
        </div>

        <div className="dashboard-sidebar">
          <div className="card">
            <h2>Risk Heatmap</h2>
            <RiskHeatmap transactions={transactions} />
          </div>
        </div>
      </div>

      <CaseDrawer
        isOpen={isDrawerOpen}
        onClose={handleDrawerClose}
        transaction={selectedTransaction}
      />
    </div>
  );
};

export default Dashboard;
