import React from 'react';

interface LoadingProps {
  size?: 'small' | 'medium' | 'large';
  variant?: 'spinner' | 'dots' | 'pulse' | 'skeleton';
  message?: string;
  overlay?: boolean;
  className?: string;
}

/**
 * Loading component with multiple variants and sizes
 */
export const Loading: React.FC<LoadingProps> = ({
  size = 'medium',
  variant = 'spinner',
  message,
  overlay = false,
  className = ''
}) => {
  const sizeClasses = {
    small: 'w-4 h-4',
    medium: 'w-8 h-8',
    large: 'w-12 h-12'
  };

  const renderSpinner = () => (
    <div className={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]}`} />
  );

  const renderDots = () => (
    <div className="flex space-x-1">
      <div className={`bg-blue-600 rounded-full animate-bounce ${size === 'small' ? 'w-2 h-2' : size === 'medium' ? 'w-3 h-3' : 'w-4 h-4'}`} style={{ animationDelay: '0ms' }} />
      <div className={`bg-blue-600 rounded-full animate-bounce ${size === 'small' ? 'w-2 h-2' : size === 'medium' ? 'w-3 h-3' : 'w-4 h-4'}`} style={{ animationDelay: '150ms' }} />
      <div className={`bg-blue-600 rounded-full animate-bounce ${size === 'small' ? 'w-2 h-2' : size === 'medium' ? 'w-3 h-3' : 'w-4 h-4'}`} style={{ animationDelay: '300ms' }} />
    </div>
  );

  const renderPulse = () => (
    <div className={`bg-blue-600 rounded-full animate-pulse ${sizeClasses[size]}`} />
  );

  const renderSkeleton = () => (
    <div className="animate-pulse space-y-2">
      <div className="h-4 bg-gray-300 rounded w-3/4"></div>
      <div className="h-4 bg-gray-300 rounded w-1/2"></div>
      <div className="h-4 bg-gray-300 rounded w-5/6"></div>
    </div>
  );

  const renderLoader = () => {
    switch (variant) {
      case 'dots':
        return renderDots();
      case 'pulse':
        return renderPulse();
      case 'skeleton':
        return renderSkeleton();
      default:
        return renderSpinner();
    }
  };

  const content = (
    <div className={`flex flex-col items-center justify-center space-y-2 ${className}`}>
      {renderLoader()}
      {message && (
        <p className={`text-gray-600 ${size === 'small' ? 'text-sm' : size === 'medium' ? 'text-base' : 'text-lg'}`}>
          {message}
        </p>
      )}
    </div>
  );

  if (overlay) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6">
          {content}
        </div>
      </div>
    );
  }

  return content;
};

/**
 * Inline loading spinner for buttons
 */
export const ButtonLoading: React.FC<{ size?: 'small' | 'medium' }> = ({ size = 'small' }) => (
  <div className={`animate-spin rounded-full border-2 border-white border-t-transparent ${size === 'small' ? 'w-4 h-4' : 'w-5 h-5'}`} />
);

/**
 * Table loading skeleton
 */
export const TableLoading: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => (
  <div className="animate-pulse">
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div key={rowIndex} className="flex space-x-4 py-3 border-b border-gray-200">
        {Array.from({ length: columns }).map((_, colIndex) => (
          <div key={colIndex} className="flex-1">
            <div className="h-4 bg-gray-300 rounded"></div>
          </div>
        ))}
      </div>
    ))}
  </div>
);

/**
 * Card loading skeleton
 */
export const CardLoading: React.FC = () => (
  <div className="animate-pulse">
    <div className="bg-white rounded-lg shadow p-6">
      <div className="h-6 bg-gray-300 rounded w-1/3 mb-4"></div>
      <div className="space-y-3">
        <div className="h-4 bg-gray-300 rounded"></div>
        <div className="h-4 bg-gray-300 rounded w-5/6"></div>
        <div className="h-4 bg-gray-300 rounded w-4/6"></div>
      </div>
      <div className="mt-6 flex space-x-2">
        <div className="h-8 bg-gray-300 rounded w-20"></div>
        <div className="h-8 bg-gray-300 rounded w-16"></div>
      </div>
    </div>
  </div>
);

/**
 * Chart loading skeleton
 */
export const ChartLoading: React.FC<{ height?: string }> = ({ height = 'h-64' }) => (
  <div className={`animate-pulse bg-gray-300 rounded ${height} flex items-end justify-center space-x-2 p-4`}>
    {Array.from({ length: 8 }).map((_, index) => (
      <div
        key={index}
        className="bg-gray-400 rounded-t"
        style={{
          height: `${Math.random() * 60 + 20}%`,
          width: '12px'
        }}
      />
    ))}
  </div>
);

/**
 * Page loading component
 */
export const PageLoading: React.FC<{ message?: string }> = ({ 
  message = "Loading..." 
}) => (
  <div className="min-h-screen flex items-center justify-center">
    <Loading size="large" message={message} />
  </div>
);

/**
 * Section loading component
 */
export const SectionLoading: React.FC<{ 
  height?: string;
  message?: string;
}> = ({ 
  height = "h-32",
  message = "Loading..." 
}) => (
  <div className={`${height} flex items-center justify-center bg-gray-50 rounded-lg`}>
    <Loading message={message} />
  </div>
);

/**
 * List loading skeleton
 */
export const ListLoading: React.FC<{ items?: number }> = ({ items = 5 }) => (
  <div className="space-y-3">
    {Array.from({ length: items }).map((_, index) => (
      <div key={index} className="animate-pulse flex items-center space-x-3 p-3 bg-white rounded-lg shadow">
        <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
        <div className="flex-1 space-y-2">
          <div className="h-4 bg-gray-300 rounded w-3/4"></div>
          <div className="h-3 bg-gray-300 rounded w-1/2"></div>
        </div>
        <div className="w-16 h-8 bg-gray-300 rounded"></div>
      </div>
    ))}
  </div>
);

/**
 * Form loading skeleton
 */
export const FormLoading: React.FC<{ fields?: number }> = ({ fields = 4 }) => (
  <div className="space-y-4">
    {Array.from({ length: fields }).map((_, index) => (
      <div key={index} className="animate-pulse">
        <div className="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
        <div className="h-10 bg-gray-300 rounded"></div>
      </div>
    ))}
    <div className="animate-pulse flex space-x-2 pt-4">
      <div className="h-10 bg-gray-300 rounded w-24"></div>
      <div className="h-10 bg-gray-300 rounded w-20"></div>
    </div>
  </div>
);

/**
 * Loading wrapper component
 */
interface LoadingWrapperProps {
  loading: boolean;
  error?: string | null;
  children: React.ReactNode;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  onRetry?: () => void;
}

export const LoadingWrapper: React.FC<LoadingWrapperProps> = ({
  loading,
  error,
  children,
  loadingComponent,
  errorComponent,
  onRetry
}) => {
  if (loading) {
    return <>{loadingComponent || <Loading message="Loading..." />}</>;
  }

  if (error) {
    return (
      <>
        {errorComponent || (
          <div className="text-center py-8">
            <div className="text-red-600 mb-4">
              <p className="text-lg font-semibold">Error</p>
              <p className="text-sm">{error}</p>
            </div>
            {onRetry && (
              <button
                onClick={onRetry}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Retry
              </button>
            )}
          </div>
        )}
      </>
    );
  }

  return <>{children}</>;
};

export default Loading;
