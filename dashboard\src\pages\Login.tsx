import React, { useState, useCallback, useEffect } from 'react';
import { useAuth } from '../services/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { ButtonLoading } from '../components/Loading';
import { useValidation, schemas } from '../utils/validation';
import { ApiError } from '../services/api';
import '../styles/Login.css';

interface LocationState {
  from?: {
    pathname: string;
  };
}

const Login: React.FC = () => {
  const [username, setUsername] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { login, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const { errors, validate, clearFieldError } = useValidation(schemas.login);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const state = location.state as LocationState;
      const from = state?.from?.pathname || '/';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location.state]);

  const handleSubmit = useCallback(async (e: React.FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();
    setError('');

    const formData = { username, password };

    // Validate form
    if (!validate(formData)) {
      return;
    }

    setIsLoading(true);

    try {
      await login(username, password);
      const state = location.state as LocationState;
      const from = state?.from?.pathname || '/';
      navigate(from, { replace: true });
    } catch (err) {
      const apiError = err as ApiError;
      setError(apiError.message || 'Invalid username or password');
    } finally {
      setIsLoading(false);
    }
  }, [username, password, login, navigate, location.state, validate]);

  const handleUsernameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    setUsername(value);
    if (errors.username) {
      clearFieldError('username');
    }
    if (error) {
      setError('');
    }
  }, [errors.username, clearFieldError, error]);

  const handlePasswordChange = useCallback((e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    setPassword(value);
    if (errors.password) {
      clearFieldError('password');
    }
    if (error) {
      setError('');
    }
  }, [errors.password, clearFieldError, error]);

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <h1>Fraud Detection Platform</h1>
          <h2>Login</h2>
        </div>

        {error && <div className="error-message">{error}</div>}

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="username">Username</label>
            <input
              type="text"
              id="username"
              value={username}
              onChange={handleUsernameChange}
              className={errors.username ? 'error' : ''}
              disabled={isLoading}
              required
              autoComplete="username"
            />
            {errors.username && <span className="field-error">{errors.username}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={handlePasswordChange}
              className={errors.password ? 'error' : ''}
              disabled={isLoading}
              required
              autoComplete="current-password"
            />
            {errors.password && <span className="field-error">{errors.password}</span>}
          </div>

          <button
            type="submit"
            className="login-button"
            disabled={isLoading || !username || !password}
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <ButtonLoading size="small" />
                <span className="ml-2">Logging in...</span>
              </span>
            ) : (
              'Login'
            )}
          </button>
        </form>

        <div className="login-footer">
          <p className="demo-credentials">
            <strong>Demo Credentials:</strong><br />
            Username: analyst | Password: password<br />
            Username: admin | Password: password
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
