{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\components\\\\Navbar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useAuth } from '../services/AuthContext';\nimport '../styles/Navbar.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const {\n    logout,\n    user\n  } = useAuth();\n  const location = useLocation();\n  const handleLogout = () => {\n    logout();\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-brand\",\n      children: /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Fraud Detection\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-menu\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: `navbar-item ${location.pathname === '/' ? 'active' : ''}`,\n        children: \"Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/cases\",\n        className: `navbar-item ${location.pathname === '/cases' ? 'active' : ''}`,\n        children: \"Case Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/analytics\",\n        className: `navbar-item ${location.pathname === '/analytics' ? 'active' : ''}`,\n        children: \"Analytics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-user\",\n      children: user && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"user-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"username\",\n            children: user.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"role\",\n            children: user.role\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"logout-button\",\n          onClick: handleLogout,\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"eFdjhKjI2YBFAyzgvchp/iYijWM=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "_s", "logout", "user", "location", "handleLogout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "pathname", "username", "role", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/components/Navbar.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useAuth } from '../services/AuthContext';\nimport '../styles/Navbar.css';\n\nconst Navbar = () => {\n  const { logout, user } = useAuth();\n  const location = useLocation();\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  return (\n    <nav className=\"navbar\">\n      <div className=\"navbar-brand\">\n        <h1>Fraud Detection</h1>\n      </div>\n      <div className=\"navbar-menu\">\n        <Link \n          to=\"/\" \n          className={`navbar-item ${location.pathname === '/' ? 'active' : ''}`}\n        >\n          Dashboard\n        </Link>\n        <Link \n          to=\"/cases\" \n          className={`navbar-item ${location.pathname === '/cases' ? 'active' : ''}`}\n        >\n          Case Management\n        </Link>\n        <Link \n          to=\"/analytics\" \n          className={`navbar-item ${location.pathname === '/analytics' ? 'active' : ''}`}\n        >\n          Analytics\n        </Link>\n      </div>\n      <div className=\"navbar-user\">\n        {user && (\n          <>\n            <span className=\"user-info\">\n              <span className=\"username\">{user.username}</span>\n              <span className=\"role\">{user.role}</span>\n            </span>\n            <button className=\"logout-button\" onClick={handleLogout}>\n              Logout\n            </button>\n          </>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAClC,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,YAAY,GAAGA,CAAA,KAAM;IACzBH,MAAM,CAAC,CAAC;EACV,CAAC;EAED,oBACEL,OAAA;IAAKS,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACrBV,OAAA;MAAKS,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BV,OAAA;QAAAU,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eACNd,OAAA;MAAKS,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BV,OAAA,CAACJ,IAAI;QACHmB,EAAE,EAAC,GAAG;QACNN,SAAS,EAAE,eAAeF,QAAQ,CAACS,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAN,QAAA,EACvE;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPd,OAAA,CAACJ,IAAI;QACHmB,EAAE,EAAC,QAAQ;QACXN,SAAS,EAAE,eAAeF,QAAQ,CAACS,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAN,QAAA,EAC5E;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPd,OAAA,CAACJ,IAAI;QACHmB,EAAE,EAAC,YAAY;QACfN,SAAS,EAAE,eAAeF,QAAQ,CAACS,QAAQ,KAAK,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAN,QAAA,EAChF;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNd,OAAA;MAAKS,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzBJ,IAAI,iBACHN,OAAA,CAAAE,SAAA;QAAAQ,QAAA,gBACEV,OAAA;UAAMS,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACzBV,OAAA;YAAMS,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEJ,IAAI,CAACW;UAAQ;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjDd,OAAA;YAAMS,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAEJ,IAAI,CAACY;UAAI;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACPd,OAAA;UAAQS,SAAS,EAAC,eAAe;UAACU,OAAO,EAAEX,YAAa;UAAAE,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,eACT;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACV,EAAA,CAhDID,MAAM;EAAA,QACeL,OAAO,EACfD,WAAW;AAAA;AAAAuB,EAAA,GAFxBjB,MAAM;AAkDZ,eAAeA,MAAM;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}