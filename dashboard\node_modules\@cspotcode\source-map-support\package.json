{"name": "@cspotcode/source-map-support", "description": "Fixes stack traces for files with source maps", "version": "0.8.1", "main": "./source-map-support.js", "types": "./source-map-support.d.ts", "scripts": {"build": "node build.js", "serve-tests": "http-server -p 1336", "test": "mocha"}, "files": ["/register.d.ts", "/register.js", "/register-hook-require.d.ts", "/register-hook-require.js", "/source-map-support.d.ts", "/source-map-support.js", "/browser-source-map-support.js"], "dependencies": {"@jridgewell/trace-mapping": "0.3.9"}, "devDependencies": {"@types/lodash": "^4.14.182", "browserify": "^4.2.3", "coffeescript": "^1.12.7", "http-server": "^0.11.1", "lodash": "^4.17.21", "mocha": "^3.5.3", "semver": "^7.3.7", "source-map": "0.6.1", "webpack": "^1.15.0"}, "repository": {"type": "git", "url": "https://github.com/cspotcode/node-source-map-support"}, "bugs": {"url": "https://github.com/cspotcode/node-source-map-support/issues"}, "license": "MIT", "engines": {"node": ">=12"}, "volta": {"node": "16.11.0", "npm": "7.24.2"}}