{"ast": null, "code": "import axios from 'axios';\n\n// API base URL\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add request interceptor for authentication\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers['Authorization'] = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Authentication API\nexport const loginUser = async (username, password) => {\n  const formData = new FormData();\n  formData.append('username', username);\n  formData.append('password', password);\n  const response = await api.post('/auth/login', formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  });\n  return response.data;\n};\n\n// Transaction API\nexport const fetchLatestTransactions = async (limit = 50) => {\n  const response = await api.get(`/transactions/latest?n=${limit}`);\n  return response.data;\n};\n\n// Case Management API\nexport const fetchCases = async (filter = {}) => {\n  let url = '/cases';\n  const params = new URLSearchParams();\n  if (filter.status && filter.status !== 'all') {\n    params.append('status', filter.status);\n  }\n  if (filter.transaction_id) {\n    params.append('transaction_id', filter.transaction_id);\n  }\n  if (params.toString()) {\n    url += `?${params.toString()}`;\n  }\n  const response = await api.get(url);\n  return response.data;\n};\nexport const fetchCase = async id => {\n  const response = await api.get(`/cases/${id}`);\n  return response.data;\n};\nexport const createCase = async caseData => {\n  const response = await api.post('/cases', caseData);\n  return response.data;\n};\nexport const updateCase = async (id, caseData) => {\n  const response = await api.put(`/cases/${id}`, caseData);\n  return response.data;\n};\n\n// Analytics API\nexport const fetchCaseStats = async () => {\n  const response = await api.get('/cases/stats');\n  return response.data;\n};\nexport const fetchFeatureImportance = async () => {\n  const response = await api.get('/feature-importance');\n  return response.data || [{\n    feature: 'type_TRANSFER',\n    importance: 0.35\n  }, {\n    feature: 'amount',\n    importance: 0.25\n  }, {\n    feature: 'oldbalanceOrg',\n    importance: 0.15\n  }, {\n    feature: 'newbalanceOrig',\n    importance: 0.10\n  }, {\n    feature: 'oldbalanceDest',\n    importance: 0.08\n  }, {\n    feature: 'newbalanceDest',\n    importance: 0.07\n  }];\n};\nexport const fetchFraudTrend = async (dateRange = 'week') => {\n  const response = await api.get(`/fraud-trend?range=${dateRange}`);\n  return response.data || generateMockTrendData(dateRange);\n};\n\n// Helper function to generate mock trend data for demo purposes\nconst generateMockTrendData = dateRange => {\n  const now = new Date();\n  const data = [];\n  let days;\n  switch (dateRange) {\n    case 'day':\n      days = 1;\n      break;\n    case 'week':\n      days = 7;\n      break;\n    case 'month':\n      days = 30;\n      break;\n    case 'year':\n      days = 365;\n      break;\n    default:\n      days = 7;\n  }\n  for (let i = days - 1; i >= 0; i--) {\n    const date = new Date(now);\n    date.setDate(date.getDate() - i);\n    data.push({\n      date: date.toISOString().split('T')[0],\n      count: Math.floor(Math.random() * 10) + 1\n    });\n  }\n  return data;\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "error", "Promise", "reject", "loginUser", "username", "password", "formData", "FormData", "append", "response", "post", "data", "fetchLatestTransactions", "limit", "get", "fetchCases", "filter", "url", "params", "URLSearchParams", "status", "transaction_id", "toString", "fetchCase", "id", "createCase", "caseData", "updateCase", "put", "fetchCaseStats", "fetchFeatureImportance", "feature", "importance", "fetchFraudTrend", "date<PERSON><PERSON><PERSON>", "generateMockTrendData", "now", "Date", "days", "i", "date", "setDate", "getDate", "push", "toISOString", "split", "count", "Math", "floor", "random"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\n\n// API base URL\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add request interceptor for authentication\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers['Authorization'] = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Authentication API\nexport const loginUser = async (username, password) => {\n  const formData = new FormData();\n  formData.append('username', username);\n  formData.append('password', password);\n  \n  const response = await api.post('/auth/login', formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  });\n  return response.data;\n};\n\n// Transaction API\nexport const fetchLatestTransactions = async (limit = 50) => {\n  const response = await api.get(`/transactions/latest?n=${limit}`);\n  return response.data;\n};\n\n// Case Management API\nexport const fetchCases = async (filter = {}) => {\n  let url = '/cases';\n  const params = new URLSearchParams();\n  \n  if (filter.status && filter.status !== 'all') {\n    params.append('status', filter.status);\n  }\n  \n  if (filter.transaction_id) {\n    params.append('transaction_id', filter.transaction_id);\n  }\n  \n  if (params.toString()) {\n    url += `?${params.toString()}`;\n  }\n  \n  const response = await api.get(url);\n  return response.data;\n};\n\nexport const fetchCase = async (id) => {\n  const response = await api.get(`/cases/${id}`);\n  return response.data;\n};\n\nexport const createCase = async (caseData) => {\n  const response = await api.post('/cases', caseData);\n  return response.data;\n};\n\nexport const updateCase = async (id, caseData) => {\n  const response = await api.put(`/cases/${id}`, caseData);\n  return response.data;\n};\n\n// Analytics API\nexport const fetchCaseStats = async () => {\n  const response = await api.get('/cases/stats');\n  return response.data;\n};\n\nexport const fetchFeatureImportance = async () => {\n  const response = await api.get('/feature-importance');\n  return response.data || [\n    { feature: 'type_TRANSFER', importance: 0.35 },\n    { feature: 'amount', importance: 0.25 },\n    { feature: 'oldbalanceOrg', importance: 0.15 },\n    { feature: 'newbalanceOrig', importance: 0.10 },\n    { feature: 'oldbalanceDest', importance: 0.08 },\n    { feature: 'newbalanceDest', importance: 0.07 }\n  ];\n};\n\nexport const fetchFraudTrend = async (dateRange = 'week') => {\n  const response = await api.get(`/fraud-trend?range=${dateRange}`);\n  return response.data || generateMockTrendData(dateRange);\n};\n\n// Helper function to generate mock trend data for demo purposes\nconst generateMockTrendData = (dateRange) => {\n  const now = new Date();\n  const data = [];\n  let days;\n  \n  switch (dateRange) {\n    case 'day':\n      days = 1;\n      break;\n    case 'week':\n      days = 7;\n      break;\n    case 'month':\n      days = 30;\n      break;\n    case 'year':\n      days = 365;\n      break;\n    default:\n      days = 7;\n  }\n  \n  for (let i = days - 1; i >= 0; i--) {\n    const date = new Date(now);\n    date.setDate(date.getDate() - i);\n    \n    data.push({\n      date: date.toISOString().split('T')[0],\n      count: Math.floor(Math.random() * 10) + 1\n    });\n  }\n  \n  return data;\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAExE;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,OAAO;EAChBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUK,KAAK,EAAE;EACrD;EACA,OAAOD,MAAM;AACf,CAAC,EACAI,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMG,SAAS,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;EACrD,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,QAAQ,CAAC;EACrCE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,QAAQ,CAAC;EAErC,MAAMI,QAAQ,GAAG,MAAMpB,GAAG,CAACqB,IAAI,CAAC,aAAa,EAAEJ,QAAQ,EAAE;IACvDd,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACF,OAAOiB,QAAQ,CAACE,IAAI;AACtB,CAAC;;AAED;AACA,OAAO,MAAMC,uBAAuB,GAAG,MAAAA,CAAOC,KAAK,GAAG,EAAE,KAAK;EAC3D,MAAMJ,QAAQ,GAAG,MAAMpB,GAAG,CAACyB,GAAG,CAAC,0BAA0BD,KAAK,EAAE,CAAC;EACjE,OAAOJ,QAAQ,CAACE,IAAI;AACtB,CAAC;;AAED;AACA,OAAO,MAAMI,UAAU,GAAG,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;EAC/C,IAAIC,GAAG,GAAG,QAAQ;EAClB,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;EAEpC,IAAIH,MAAM,CAACI,MAAM,IAAIJ,MAAM,CAACI,MAAM,KAAK,KAAK,EAAE;IAC5CF,MAAM,CAACV,MAAM,CAAC,QAAQ,EAAEQ,MAAM,CAACI,MAAM,CAAC;EACxC;EAEA,IAAIJ,MAAM,CAACK,cAAc,EAAE;IACzBH,MAAM,CAACV,MAAM,CAAC,gBAAgB,EAAEQ,MAAM,CAACK,cAAc,CAAC;EACxD;EAEA,IAAIH,MAAM,CAACI,QAAQ,CAAC,CAAC,EAAE;IACrBL,GAAG,IAAI,IAAIC,MAAM,CAACI,QAAQ,CAAC,CAAC,EAAE;EAChC;EAEA,MAAMb,QAAQ,GAAG,MAAMpB,GAAG,CAACyB,GAAG,CAACG,GAAG,CAAC;EACnC,OAAOR,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMY,SAAS,GAAG,MAAOC,EAAE,IAAK;EACrC,MAAMf,QAAQ,GAAG,MAAMpB,GAAG,CAACyB,GAAG,CAAC,UAAUU,EAAE,EAAE,CAAC;EAC9C,OAAOf,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMc,UAAU,GAAG,MAAOC,QAAQ,IAAK;EAC5C,MAAMjB,QAAQ,GAAG,MAAMpB,GAAG,CAACqB,IAAI,CAAC,QAAQ,EAAEgB,QAAQ,CAAC;EACnD,OAAOjB,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMgB,UAAU,GAAG,MAAAA,CAAOH,EAAE,EAAEE,QAAQ,KAAK;EAChD,MAAMjB,QAAQ,GAAG,MAAMpB,GAAG,CAACuC,GAAG,CAAC,UAAUJ,EAAE,EAAE,EAAEE,QAAQ,CAAC;EACxD,OAAOjB,QAAQ,CAACE,IAAI;AACtB,CAAC;;AAED;AACA,OAAO,MAAMkB,cAAc,GAAG,MAAAA,CAAA,KAAY;EACxC,MAAMpB,QAAQ,GAAG,MAAMpB,GAAG,CAACyB,GAAG,CAAC,cAAc,CAAC;EAC9C,OAAOL,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMmB,sBAAsB,GAAG,MAAAA,CAAA,KAAY;EAChD,MAAMrB,QAAQ,GAAG,MAAMpB,GAAG,CAACyB,GAAG,CAAC,qBAAqB,CAAC;EACrD,OAAOL,QAAQ,CAACE,IAAI,IAAI,CACtB;IAAEoB,OAAO,EAAE,eAAe;IAAEC,UAAU,EAAE;EAAK,CAAC,EAC9C;IAAED,OAAO,EAAE,QAAQ;IAAEC,UAAU,EAAE;EAAK,CAAC,EACvC;IAAED,OAAO,EAAE,eAAe;IAAEC,UAAU,EAAE;EAAK,CAAC,EAC9C;IAAED,OAAO,EAAE,gBAAgB;IAAEC,UAAU,EAAE;EAAK,CAAC,EAC/C;IAAED,OAAO,EAAE,gBAAgB;IAAEC,UAAU,EAAE;EAAK,CAAC,EAC/C;IAAED,OAAO,EAAE,gBAAgB;IAAEC,UAAU,EAAE;EAAK,CAAC,CAChD;AACH,CAAC;AAED,OAAO,MAAMC,eAAe,GAAG,MAAAA,CAAOC,SAAS,GAAG,MAAM,KAAK;EAC3D,MAAMzB,QAAQ,GAAG,MAAMpB,GAAG,CAACyB,GAAG,CAAC,sBAAsBoB,SAAS,EAAE,CAAC;EACjE,OAAOzB,QAAQ,CAACE,IAAI,IAAIwB,qBAAqB,CAACD,SAAS,CAAC;AAC1D,CAAC;;AAED;AACA,MAAMC,qBAAqB,GAAID,SAAS,IAAK;EAC3C,MAAME,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;EACtB,MAAM1B,IAAI,GAAG,EAAE;EACf,IAAI2B,IAAI;EAER,QAAQJ,SAAS;IACf,KAAK,KAAK;MACRI,IAAI,GAAG,CAAC;MACR;IACF,KAAK,MAAM;MACTA,IAAI,GAAG,CAAC;MACR;IACF,KAAK,OAAO;MACVA,IAAI,GAAG,EAAE;MACT;IACF,KAAK,MAAM;MACTA,IAAI,GAAG,GAAG;MACV;IACF;MACEA,IAAI,GAAG,CAAC;EACZ;EAEA,KAAK,IAAIC,CAAC,GAAGD,IAAI,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAClC,MAAMC,IAAI,GAAG,IAAIH,IAAI,CAACD,GAAG,CAAC;IAC1BI,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,GAAGH,CAAC,CAAC;IAEhC5B,IAAI,CAACgC,IAAI,CAAC;MACRH,IAAI,EAAEA,IAAI,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtCC,KAAK,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;IAC1C,CAAC,CAAC;EACJ;EAEA,OAAOtC,IAAI;AACb,CAAC;AAED,eAAetB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}