#!/usr/bin/env python3
"""
High-risk fraud test data generator and tester.

This script generates transaction data designed to trigger high fraud risk scores
based on the fraud detection model's risk factors.
"""
import requests
import json
from datetime import datetime
from typing import List, Dict, Any


def get_high_risk_transactions() -> List[Dict[str, Any]]:
    """
    Generate high-risk fraud test transactions based on REAL fraud patterns
    from the trained model's training data.

    Real fraud patterns identified from training data:
    1. TRANSFER transactions where entire balance is transferred (oldbalanceOrg = amount, newbalanceOrig = 0)
    2. CASH_OUT transactions where destination balance goes to 0 (newbalanceDest = 0)
    3. Account emptying patterns
    4. Suspicious balance inconsistencies

    Returns:
        List of high-risk transaction dictionaries
    """

    high_risk_transactions = [
        {
            # Pattern 1: TRANSFER with complete account emptying (like training samples)
            "transaction_id": "high_risk_001_transfer_empty_account",
            "step": 1,
            "type": "TRANSFER",
            "amount": 2500.0,
            "nameOrig": "C123456789",
            "oldbalanceOrg": 2500.0,  # Same as amount
            "newbalanceOrig": 0.0,    # Account completely emptied
            "nameDest": "C987654321",
            "oldbalanceDest": 10000.0,
            "newbalanceDest": 12500.0  # Destination receives the money
            # This matches the fraud pattern from training sample line 3
        },

        {
            # Pattern 2: CASH_OUT with destination balance going to zero
            "transaction_id": "high_risk_002_cashout_dest_zero",
            "step": 1,
            "type": "CASH_OUT",
            "amount": 1900.0,
            "nameOrig": "C555666777",
            "oldbalanceOrg": 2200.0,
            "newbalanceOrig": 300.0,
            "nameDest": "C444555666",
            "oldbalanceDest": 1000.0,
            "newbalanceDest": 0.0     # Destination balance goes to zero (suspicious)
            # This matches the fraud pattern from training sample line 23
        },

        {
            # Pattern 3: Large TRANSFER with complete emptying
            "transaction_id": "high_risk_003_large_transfer_empty",
            "step": 1,
            "type": "TRANSFER",
            "amount": 50000.0,
            "nameOrig": "C888999000",
            "oldbalanceOrg": 50000.0,  # Same as amount
            "newbalanceOrig": 0.0,     # Complete emptying
            "nameDest": "C111222333",
            "oldbalanceDest": 0.0,
            "newbalanceDest": 50000.0
            # Larger version of the fraud pattern
        },

        {
            # Pattern 4: CASH_OUT with suspicious destination pattern
            "transaction_id": "high_risk_004_cashout_suspicious",
            "step": 1,
            "type": "CASH_OUT",
            "amount": 3000.0,
            "nameOrig": "C777888999",
            "oldbalanceOrg": 4000.0,
            "newbalanceOrig": 1000.0,
            "nameDest": "C333444555",
            "oldbalanceDest": 5000.0,
            "newbalanceDest": 2000.0   # Destination loses money (very suspicious)
            # This matches the fraud pattern from training sample line 4
        },

        {
            # Pattern 5: Multiple small TRANSFER emptying pattern
            "transaction_id": "high_risk_005_small_transfer_empty",
            "step": 1,
            "type": "TRANSFER",
            "amount": 1200.0,
            "nameOrig": "C111000111",
            "oldbalanceOrg": 1200.0,   # Same as amount
            "newbalanceOrig": 0.0,     # Complete emptying
            "nameDest": "C222000222",
            "oldbalanceDest": 0.0,
            "newbalanceDest": 1200.0
            # This matches the fraud pattern from training sample line 7
        },

        {
            # Pattern 6: Large amount with fraud characteristics
            "transaction_id": "high_risk_006_large_amount_fraud",
            "step": 1,
            "type": "TRANSFER",
            "amount": 100000.0,
            "nameOrig": "C999888777",
            "oldbalanceOrg": 100000.0,  # Same as amount
            "newbalanceOrig": 0.0,      # Complete emptying
            "nameDest": "C666555444",
            "oldbalanceDest": 0.0,
            "newbalanceDest": 100000.0
            # Large amount version of proven fraud pattern
        }
    ]

    return high_risk_transactions


def get_medium_risk_transactions() -> List[Dict[str, Any]]:
    """Generate medium-risk transactions for comparison."""

    medium_risk_transactions = [
        {
            # Medium risk: Large PAYMENT (safer transaction type)
            "transaction_id": "medium_risk_001_large_payment",
            "step": 1,
            "type": "PAYMENT",
            "amount": 80000.0,  # Large amount (+0.25)
            "nameOrig": "C333444555",
            "oldbalanceOrg": 100000.0,
            "newbalanceOrig": 20000.0,  # 80% of balance
            "nameDest": "M333444555",  # Merchant destination
            "oldbalanceDest": 0.0,
            "newbalanceDest": 0.0  # Merchant doesn't show balance
            # Expected risk factors: +0.25 (large) +0.2 (80% balance) +0.1 (round) = ~0.55
        },

        {
            # Medium risk: Moderate TRANSFER
            "transaction_id": "medium_risk_002_moderate_transfer",
            "step": 1,
            "type": "TRANSFER",
            "amount": 25000.0,  # Medium amount (+0.15)
            "nameOrig": "C666777888",
            "oldbalanceOrg": 50000.0,
            "newbalanceOrig": 25000.0,  # 50% of balance
            "nameDest": "C999000111",
            "oldbalanceDest": 10000.0,
            "newbalanceDest": 35000.0
            # Expected risk factors: +0.15 (medium) +0.25 (TRANSFER) = ~0.4
        }
    ]

    return medium_risk_transactions


def test_transactions_with_api(transactions: List[Dict[str, Any]],
                              service_url: str = "http://localhost:8001") -> List[Dict[str, Any]]:
    """
    Test transactions with the fraud detection API.

    Args:
        transactions: List of transaction dictionaries
        service_url: URL of the model service

    Returns:
        List of results with risk scores
    """
    results = []

    for transaction in transactions:
        try:
            # Prepare API request
            api_request = {"transactions": [transaction]}

            # Call the API
            response = requests.post(
                f"{service_url}/score",
                json=api_request,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                risk_score = result['results'][0]['risk']

                # Add result to our list
                test_result = {
                    "transaction_id": transaction["transaction_id"],
                    "transaction": transaction,
                    "risk_score": risk_score,
                    "risk_percentage": f"{risk_score * 100:.1f}%",
                    "risk_level": get_risk_level(risk_score),
                    "api_success": True
                }
                results.append(test_result)

                print(f"✅ {transaction['transaction_id']}: {risk_score:.3f} ({risk_score * 100:.1f}%) - {get_risk_level(risk_score)}")

            else:
                print(f"❌ API Error for {transaction['transaction_id']}: HTTP {response.status_code}")
                results.append({
                    "transaction_id": transaction["transaction_id"],
                    "transaction": transaction,
                    "error": f"HTTP {response.status_code}",
                    "api_success": False
                })

        except Exception as e:
            print(f"❌ Exception for {transaction['transaction_id']}: {str(e)}")
            results.append({
                "transaction_id": transaction["transaction_id"],
                "transaction": transaction,
                "error": str(e),
                "api_success": False
            })

    return results


def get_risk_level(risk_score: float) -> str:
    """Convert risk score to risk level."""
    if risk_score >= 0.8:
        return "HIGH RISK"
    elif risk_score >= 0.5:
        return "MEDIUM RISK"
    else:
        return "LOW RISK"


def print_transaction_analysis(transactions: List[Dict[str, Any]]):
    """Print detailed analysis of why transactions are high-risk."""

    print("\n" + "="*80)
    print("TRANSACTION RISK FACTOR ANALYSIS")
    print("="*80)

    for transaction in transactions:
        print(f"\n📊 Transaction: {transaction['transaction_id']}")
        print(f"   Type: {transaction['type']}")
        print(f"   Amount: ${transaction['amount']:,.2f}")
        print(f"   Origin: {transaction['nameOrig']} (${transaction['oldbalanceOrg']:,.2f} → ${transaction['newbalanceOrig']:,.2f})")
        print(f"   Destination: {transaction['nameDest']} (${transaction['oldbalanceDest']:,.2f} → ${transaction['newbalanceDest']:,.2f})")

        print("   🚨 Risk Factors:")

        # Amount-based risk
        amount = transaction['amount']
        if amount > 100000:
            print(f"      • Large amount (>${amount:,.2f} > $100k) → +40% risk")
        elif amount > 50000:
            print(f"      • Medium-large amount (${amount:,.2f} > $50k) → +25% risk")
        elif amount > 10000:
            print(f"      • Medium amount (${amount:,.2f} > $10k) → +15% risk")

        # Transaction type risk
        tx_type = transaction['type']
        if tx_type == 'CASH_OUT':
            print(f"      • CASH_OUT transaction → +35% risk")
        elif tx_type == 'TRANSFER':
            print(f"      • TRANSFER transaction → +25% risk")

        # Account emptying
        old_balance = transaction['oldbalanceOrg']
        new_balance = transaction['newbalanceOrig']
        if old_balance > 0 and new_balance == 0:
            print(f"      • Complete account emptying → +30% risk")

        # Large percentage of balance
        if old_balance > 0:
            percentage = amount / old_balance
            if percentage > 0.8:
                print(f"      • Large percentage of balance ({percentage*100:.1f}%) → +20% risk")

        # Merchant flag
        if transaction['nameDest'].startswith('M'):
            if tx_type in ['CASH_OUT', 'TRANSFER']:
                print(f"      • Merchant destination with risky transaction type → +10% risk")

        # Round number
        if amount > 1000 and amount % 1000 == 0:
            print(f"      • Round number amount → +10% risk")


def main():
    """Main function to test high-risk transactions."""

    print("🧪 High-Risk Fraud Detection Test Data Generator")
    print("="*60)

    # Generate test data
    high_risk_txns = get_high_risk_transactions()
    medium_risk_txns = get_medium_risk_transactions()

    # Print analysis
    print_transaction_analysis(high_risk_txns)

    # Test with API
    print(f"\n🚀 Testing {len(high_risk_txns)} high-risk transactions with API...")
    high_risk_results = test_transactions_with_api(high_risk_txns)

    print(f"\n🚀 Testing {len(medium_risk_txns)} medium-risk transactions for comparison...")
    medium_risk_results = test_transactions_with_api(medium_risk_txns)

    # Summary
    print("\n" + "="*60)
    print("📋 TEST RESULTS SUMMARY")
    print("="*60)

    high_risk_count = sum(1 for r in high_risk_results if r.get('api_success') and r.get('risk_score', 0) >= 0.8)
    medium_risk_count = sum(1 for r in medium_risk_results if r.get('api_success') and 0.5 <= r.get('risk_score', 0) < 0.8)

    print(f"High-risk transactions (≥80%): {high_risk_count}/{len(high_risk_txns)}")
    print(f"Medium-risk transactions (50-79%): {medium_risk_count}/{len(medium_risk_txns)}")

    # Export JSON for dashboard testing
    export_data = {
        "high_risk_transactions": high_risk_txns,
        "medium_risk_transactions": medium_risk_txns,
        "test_results": {
            "high_risk": high_risk_results,
            "medium_risk": medium_risk_results
        },
        "generated_at": datetime.now().isoformat()
    }

    with open("high_risk_test_data.json", "w") as f:
        json.dump(export_data, f, indent=2)

    print(f"\n💾 Test data exported to: high_risk_test_data.json")
    print("📱 You can copy these transactions to the HTML dashboard for testing!")


if __name__ == "__main__":
    main()
