{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\components\\\\CaseForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport '../styles/CaseForm.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CaseForm = ({\n  initialCase,\n  onSubmit\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    transaction_id: (initialCase === null || initialCase === void 0 ? void 0 : initialCase.transaction_id) || '',\n    tag: (initialCase === null || initialCase === void 0 ? void 0 : initialCase.tag) || 'NEEDS_REVIEW',\n    comment: (initialCase === null || initialCase === void 0 ? void 0 : initialCase.comment) || '',\n    status: (initialCase === null || initialCase === void 0 ? void 0 : initialCase.status) || 'open'\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    try {\n      await onSubmit(formData);\n    } catch (error) {\n      console.error('Error submitting form:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"case-form-wrapper\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: initialCase ? 'Update Case' : 'Create New Case'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"transaction_id\",\n          children: \"Transaction ID:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          id: \"transaction_id\",\n          name: \"transaction_id\",\n          value: formData.transaction_id,\n          onChange: handleChange,\n          required: true,\n          disabled: !!initialCase\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"tag\",\n          children: \"Tag:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          id: \"tag\",\n          name: \"tag\",\n          value: formData.tag,\n          onChange: handleChange,\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"NEEDS_REVIEW\",\n            children: \"Needs Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"SUSPICIOUS\",\n            children: \"Suspicious\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"CONFIRMED\",\n            children: \"Confirmed Fraud\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"FP\",\n            children: \"False Positive\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"status\",\n          children: \"Status:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          id: \"status\",\n          name: \"status\",\n          value: formData.status,\n          onChange: handleChange,\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"open\",\n            children: \"Open\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"pending\",\n            children: \"Pending\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"closed\",\n            children: \"Closed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"comment\",\n          children: \"Comment:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          id: \"comment\",\n          name: \"comment\",\n          value: formData.comment,\n          onChange: handleChange,\n          rows: 4\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"submit-button\",\n        disabled: isSubmitting,\n        children: isSubmitting ? 'Submitting...' : initialCase ? 'Update' : 'Create'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(CaseForm, \"hKEvZy7EKiub0Yb8IylXiPYkS+A=\");\n_c = CaseForm;\nexport default CaseForm;\nvar _c;\n$RefreshReg$(_c, \"CaseForm\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "CaseForm", "initialCase", "onSubmit", "_s", "formData", "setFormData", "transaction_id", "tag", "comment", "status", "isSubmitting", "setIsSubmitting", "handleChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "type", "id", "onChange", "required", "disabled", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/components/CaseForm.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport '../styles/CaseForm.css';\n\nconst CaseForm = ({ initialCase, onSubmit }) => {\n  const [formData, setFormData] = useState({\n    transaction_id: initialCase?.transaction_id || '',\n    tag: initialCase?.tag || 'NEEDS_REVIEW',\n    comment: initialCase?.comment || '',\n    status: initialCase?.status || 'open'\n  });\n  \n  const [isSubmitting, setIsSubmitting] = useState(false);\n  \n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  \n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    try {\n      await onSubmit(formData);\n    } catch (error) {\n      console.error('Error submitting form:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  \n  return (\n    <div className=\"case-form-wrapper\">\n      <h2>{initialCase ? 'Update Case' : 'Create New Case'}</h2>\n      \n      <form onSubmit={handleSubmit}>\n        <div className=\"form-group\">\n          <label htmlFor=\"transaction_id\">Transaction ID:</label>\n          <input\n            type=\"text\"\n            id=\"transaction_id\"\n            name=\"transaction_id\"\n            value={formData.transaction_id}\n            onChange={handleChange}\n            required\n            disabled={!!initialCase}\n          />\n        </div>\n        \n        <div className=\"form-group\">\n          <label htmlFor=\"tag\">Tag:</label>\n          <select\n            id=\"tag\"\n            name=\"tag\"\n            value={formData.tag}\n            onChange={handleChange}\n            required\n          >\n            <option value=\"NEEDS_REVIEW\">Needs Review</option>\n            <option value=\"SUSPICIOUS\">Suspicious</option>\n            <option value=\"CONFIRMED\">Confirmed Fraud</option>\n            <option value=\"FP\">False Positive</option>\n          </select>\n        </div>\n        \n        <div className=\"form-group\">\n          <label htmlFor=\"status\">Status:</label>\n          <select\n            id=\"status\"\n            name=\"status\"\n            value={formData.status}\n            onChange={handleChange}\n            required\n          >\n            <option value=\"open\">Open</option>\n            <option value=\"pending\">Pending</option>\n            <option value=\"closed\">Closed</option>\n          </select>\n        </div>\n        \n        <div className=\"form-group\">\n          <label htmlFor=\"comment\">Comment:</label>\n          <textarea\n            id=\"comment\"\n            name=\"comment\"\n            value={formData.comment}\n            onChange={handleChange}\n            rows={4}\n          ></textarea>\n        </div>\n        \n        <button\n          type=\"submit\"\n          className=\"submit-button\"\n          disabled={isSubmitting}\n        >\n          {isSubmitting ? 'Submitting...' : initialCase ? 'Update' : 'Create'}\n        </button>\n      </form>\n    </div>\n  );\n};\n\nexport default CaseForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,WAAW;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC;IACvCS,cAAc,EAAE,CAAAL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,cAAc,KAAI,EAAE;IACjDC,GAAG,EAAE,CAAAN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEM,GAAG,KAAI,cAAc;IACvCC,OAAO,EAAE,CAAAP,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEO,OAAO,KAAI,EAAE;IACnCC,MAAM,EAAE,CAAAR,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEQ,MAAM,KAAI;EACjC,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMe,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCX,WAAW,CAACY,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOL,CAAC,IAAK;IAChCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBR,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF,MAAMT,QAAQ,CAACE,QAAQ,CAAC;IAC1B,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRT,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACEZ,OAAA;IAAKuB,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCxB,OAAA;MAAAwB,QAAA,EAAKtB,WAAW,GAAG,aAAa,GAAG;IAAiB;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAE1D5B,OAAA;MAAMG,QAAQ,EAAEgB,YAAa;MAAAK,QAAA,gBAC3BxB,OAAA;QAAKuB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxB,OAAA;UAAO6B,OAAO,EAAC,gBAAgB;UAAAL,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvD5B,OAAA;UACE8B,IAAI,EAAC,MAAM;UACXC,EAAE,EAAC,gBAAgB;UACnBhB,IAAI,EAAC,gBAAgB;UACrBC,KAAK,EAAEX,QAAQ,CAACE,cAAe;UAC/ByB,QAAQ,EAAEnB,YAAa;UACvBoB,QAAQ;UACRC,QAAQ,EAAE,CAAC,CAAChC;QAAY;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN5B,OAAA;QAAKuB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxB,OAAA;UAAO6B,OAAO,EAAC,KAAK;UAAAL,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjC5B,OAAA;UACE+B,EAAE,EAAC,KAAK;UACRhB,IAAI,EAAC,KAAK;UACVC,KAAK,EAAEX,QAAQ,CAACG,GAAI;UACpBwB,QAAQ,EAAEnB,YAAa;UACvBoB,QAAQ;UAAAT,QAAA,gBAERxB,OAAA;YAAQgB,KAAK,EAAC,cAAc;YAAAQ,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClD5B,OAAA;YAAQgB,KAAK,EAAC,YAAY;YAAAQ,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9C5B,OAAA;YAAQgB,KAAK,EAAC,WAAW;YAAAQ,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClD5B,OAAA;YAAQgB,KAAK,EAAC,IAAI;YAAAQ,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN5B,OAAA;QAAKuB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxB,OAAA;UAAO6B,OAAO,EAAC,QAAQ;UAAAL,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvC5B,OAAA;UACE+B,EAAE,EAAC,QAAQ;UACXhB,IAAI,EAAC,QAAQ;UACbC,KAAK,EAAEX,QAAQ,CAACK,MAAO;UACvBsB,QAAQ,EAAEnB,YAAa;UACvBoB,QAAQ;UAAAT,QAAA,gBAERxB,OAAA;YAAQgB,KAAK,EAAC,MAAM;YAAAQ,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClC5B,OAAA;YAAQgB,KAAK,EAAC,SAAS;YAAAQ,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxC5B,OAAA;YAAQgB,KAAK,EAAC,QAAQ;YAAAQ,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN5B,OAAA;QAAKuB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxB,OAAA;UAAO6B,OAAO,EAAC,SAAS;UAAAL,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzC5B,OAAA;UACE+B,EAAE,EAAC,SAAS;UACZhB,IAAI,EAAC,SAAS;UACdC,KAAK,EAAEX,QAAQ,CAACI,OAAQ;UACxBuB,QAAQ,EAAEnB,YAAa;UACvBsB,IAAI,EAAE;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAEN5B,OAAA;QACE8B,IAAI,EAAC,QAAQ;QACbP,SAAS,EAAC,eAAe;QACzBW,QAAQ,EAAEvB,YAAa;QAAAa,QAAA,EAEtBb,YAAY,GAAG,eAAe,GAAGT,WAAW,GAAG,QAAQ,GAAG;MAAQ;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACxB,EAAA,CArGIH,QAAQ;AAAAmC,EAAA,GAARnC,QAAQ;AAuGd,eAAeA,QAAQ;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}