{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;AAAA,+BAAkE;AAClE,mCAAgC;AAChC,6BAA6B;AAC7B,6BAAoC;AAGpC,2CAAuC;AAIvC,iCAWgB;AAChB,mDAAkE;AAElE,qEAGkC;AAClC,6DAA+D;AAE/D,2DAG6B;AAC7B,+EAA+D;AAI/D,uDAA8D;AAC9D,+DAAgE;AAGhE,+BAKgB;AAJd,kGAAA,UAAU,OAAA;AAmBZ;;;GAGG;AACH,MAAM,8BAA8B,GAClC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC;AAE1D;;;;;GAKG;AACH,IAAI,wBAAwB,GAIhB,8BAA8B;IACxC,CAAC,CACG,OAAO,CAAC,8CAA8C,CACvD,CAAC,4BAA4B;IAChC,CAAC,CAAC,GAAG,EAAE;QACH,UAAU;IACZ,CAAC,CAAC;AAEN;;GAEG;AACU,QAAA,iBAAiB,GAAG,MAAM,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;AAazE,gBAAgB;AACH,QAAA,GAAG,GAAG,OAAO,CAAC,GAAiB,CAAC;AAkC7C;;GAEG;AACU,QAAA,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC;AAE/D;;GAEG;AACH,MAAM,WAAW,GAAG,IAAA,SAAE,EAAC,WAAG,CAAC,aAAa,CAAC,CAAC;AAC1C,gBAAgB;AACH,QAAA,KAAK,GAAG,WAAW;IAC9B,CAAC,CAAC,CAAC,GAAG,IAAS,EAAE,EAAE,CACf,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACjE,CAAC,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC;AACpB,MAAM,OAAO,GAAG,WAAW;IACzB,CAAC,CAAC,CAAO,GAAW,EAAE,EAAiB,EAAE,EAAE;QACvC,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,CAAC,CAAI,EAAE,EAAE;YACd,IAAA,aAAK,EAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YACnB,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC;IACJ,CAAC;IACH,CAAC,CAAC,CAAO,CAAS,EAAE,EAAiB,EAAE,EAAE,CAAC,EAAE,CAAC;AAE/C;;GAEG;AACU,QAAA,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC;AAsR1D;;;;GAIG;AACU,QAAA,QAAQ,GAAoB;IACvC,GAAG,EAAE,MAAA,WAAG,CAAC,WAAW,mCAAI,WAAG,CAAC,WAAW;IACvC,IAAI,EAAE,IAAA,SAAE,EAAC,WAAG,CAAC,YAAY,CAAC;IAC1B,KAAK,EAAE,IAAA,SAAE,EAAC,WAAG,CAAC,aAAa,CAAC;IAC5B,QAAQ,EAAE,WAAG,CAAC,iBAAiB;IAC/B,KAAK,EAAE,IAAA,SAAE,EAAC,WAAG,CAAC,aAAa,CAAC;IAC5B,MAAM,EAAE,IAAA,SAAE,EAAC,WAAG,CAAC,cAAc,CAAC;IAC9B,QAAQ,EAAE,WAAG,CAAC,gBAAgB;IAC9B,eAAe,EAAE,IAAA,YAAK,EAAC,WAAG,CAAC,wBAAwB,CAAC;IACpD,MAAM,EAAE,IAAA,YAAK,EAAC,WAAG,CAAC,cAAc,CAAC;IACjC,OAAO,EAAE,WAAG,CAAC,eAAe;IAC5B,WAAW,EAAE,IAAA,SAAE,EAAC,WAAG,CAAC,oBAAoB,CAAC;IACzC,UAAU,EAAE,IAAA,SAAE,EAAC,WAAG,CAAC,mBAAmB,CAAC;IACvC,YAAY,EAAE,IAAA,SAAE,EAAC,WAAG,CAAC,sBAAsB,CAAC;IAC5C,iBAAiB,EAAE,IAAA,YAAK,EAAC,WAAG,CAAC,0BAA0B,CAAC;IACxD,aAAa,EAAE,IAAA,SAAE,EAAC,WAAG,CAAC,sBAAsB,CAAC;IAC7C,SAAS,EAAE,IAAA,SAAE,EAAC,WAAG,CAAC,kBAAkB,CAAC;IACrC,YAAY,EAAE,IAAA,SAAE,EAAC,WAAG,CAAC,qBAAqB,CAAC;IAC3C,QAAQ,EAAE,IAAA,SAAE,EAAC,WAAG,CAAC,iBAAiB,CAAC;IACnC,qBAAqB,EAAE,MAAA,IAAA,SAAE,EAAC,WAAG,CAAC,+BAA+B,CAAC,mCAAI,SAAS;IAC3E,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;CACnC,CAAC;AAEF;;GAEG;AACH,MAAa,OAAQ,SAAQ,sBAAS;IAKpC,YACE,cAAsB,EACf,eAAyB,EAChC,cAA6C,EAAE;QAE/C,KAAK,CAAC,oCAAoC,cAAc,EAAE,CAAC,CAAC;QAHrD,oBAAe,GAAf,eAAe,CAAU;QANlC,SAAI,GAAG,SAAS,CAAC;QAUf,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,EAAE;YAC5C,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,cAAc;SACtB,CAAC,CAAC;QACH,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,aAAa,EAAE;YACzC,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,WAAW;SACnB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,CAAC,sBAAc,CAAC;QACd,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;CACF;AA7BD,0BA6BC;AAED,MAAM,qBAAqB,GAAG,MAAM,CAAC,uBAAuB,CAAC,CAAC;AA8E9D,SAAgB,QAAQ,CACtB,aAAoD;IAEpD,0CAA0C;IAC1C,IAAI,OAAO,GAAG,aAAwB,CAAC;IACvC,IAAI,CAAC,CAAC,aAAyB,aAAzB,aAAa,uBAAb,aAAa,CAAe,qBAAqB,CAAC,CAAA,EAAE;QACxD,4BAA4B;QAC5B,OAAO,GAAG,MAAM,CAAC,CAAC,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,EAAE,CAAoB,CAAC,CAAC;KAC5D;IAED,MAAM,iBAAiB,GAAG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAEpD,uCAAuC;IACvC,OAAO,CAAC,yBAAiB,CAAC,GAAG,OAAO,CAAC;IAErC,2BAA2B;IAC3B,kBAAkB,CAChB,OAAO,CAAC,OAAO,CAAC,YAAY,EAC5B,OAAO,CAAC,UAAU,CAAC,QAAQ,EAC3B,OAAO,EACP,iBAAiB,CAClB,CAAC;IAEF,IAAA,0DAAsC,EAAC,OAAO,CAAC,CAAC;IAEhD,6CAA6C;IAC5C,eAAyC,CAAC,eAAe,CACxD,OAAO,CAAC,OAAO,CAAC,OAAO,CACxB,CAAC;IAEF,OAAO,OAAO,CAAC;AACjB,CAAC;AA/BD,4BA+BC;AAED;;;;GAIG;AACH,SAAgB,MAAM,CAAC,aAA4B,EAAE;IACnD,MAAM,iBAAiB,GAAG,IAAA,iCAAiB,EAAC,UAAU,CAAC,CAAC;IACxD,OAAO,yBAAyB,CAAC,iBAAiB,CAAC,CAAC;AACtD,CAAC;AAHD,wBAGC;AAED,gBAAgB;AAChB,SAAgB,yBAAyB,CACvC,iBAAuD;;IAEvD,MAAM,EACJ,cAAc,EACd,GAAG,EACH,OAAO,EACP,MAAM,EACN,QAAQ,EACR,sBAAsB,EACtB,eAAe,GAChB,GAAG,iBAAiB,CAAC;IAEtB,MAAM,yBAAyB,GAAG,IAAA,sCAA+B,EAC/D,sBAAsB,CACvB,CAAC;IAEF,MAAM,EAAE,GAAG,IAAA,4BAAY,EAAC,QAAQ,CAAC,CAAC;IAElC,sEAAsE;IACtE,MAAM,iBAAiB,GAAG,MAAM,CAAC,OAAO,CAAC,MAAO,IAAI,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC;IAC3E,IAAI,OAAO,CAAC,qBAAqB,KAAK,IAAI,IAAI,CAAC,iBAAiB,EAAE;QAChE,MAAM,IAAI,KAAK,CACb,0EAA0E,CAC3E,CAAC;KACH;IACD,sCAAsC;IACtC,MAAM,oBAAoB,GAAG,IAAA,mBAAY,EAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC/D,IAAI,OAAO,CAAC,qBAAqB,KAAK,IAAI,IAAI,CAAC,oBAAoB,EAAE;QACnE,MAAM,IAAI,KAAK,CACb,mFAAmF,CACpF,CAAC;KACH;IAED,MAAM,eAAe,GACnB,OAAO,CAAC,qBAAqB,KAAK,KAAK;QACvC,oBAAoB;QACpB,iBAAiB,CAAC;IAEpB,gCAAgC;IAChC,iHAAiH;IACjH,qDAAqD;IACrD,IAAI,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;QACrC,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,EAAE;YACnC,MAAM,IAAI,KAAK,CACb,yFAAyF,CAC1F,CAAC;SACH;QACD,IAAI,OAAO,CAAC,UAAU,EAAE;YACtB,MAAM,IAAI,KAAK,CACb,8FAA8F,CAC/F,CAAC;SACH;KACF;IAED,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC;IACrD,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC;IAC3D,oFAAoF;IACpF,MAAM,aAAa,GACjB,CAAC,OAAO,CAAC,aAAa,KAAK,IAAI,IAAI,OAAO,CAAC,GAAG,KAAK,IAAI,CAAC;QACxD,OAAO,CAAC,SAAS,KAAK,IAAI,CAAC;IAC7B,IAAI,UAAU,GAA8C,SAAS,CAAC;IACtE,IAAI,kBAAkB,GAAuB,SAAS,CAAC;IACvD,IAAI,OAAO,CAAC,UAAU,EAAE;QACtB,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QAChC,kBAAkB,GAAG,eAAe,CAAC,UAAU,CAAC;KACjD;SAAM,IAAI,OAAO,CAAC,GAAG,EAAE;QACtB,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QACrD,kBAAkB,GAAG,eAAe,CAAC,GAAG,CAAC;KAC1C;IACD,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,SAAS,CAAC;IACvD,MAAM,iBAAiB,GAA4B;QACjD;YACE,iBAAiB,EAAE,IAAI;YACvB,iBAAiB,EAAE,EAAE;YACrB,kBAAkB,EAAE;gBAClB,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,GAAG,CAAC,OAAO,CAAC,8BAA8B;oBACxC,CAAC,CAAC;wBACE,IAAI,EAAE,4GAA4G;qBACnH;oBACH,CAAC,CAAC,EAAE,CAAC;gBACP,GAAG,CAAC,OAAO,CAAC,iBAAiB,IAAI,EAAE,CAAC;aACrC,CAAC,GAAG,CAAC,MAAM,CAAC;SACd;KACF,CAAC;IAEF,MAAM,oBAAoB,GAAG,iBAAiB,CAC5C,MAAM,CAAC,MAAM,EACb,iBAAiB,CAClB,CAAC;IACF,MAAM,WAAW,GAAG,IAAI,GAAG,EAKxB,CAAC;IAEJ,MAAM,iBAAiB,GAAG,cAAc,CAAC,CAAC,CAAC,IAAA,cAAO,EAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1E,MAAM,QAAQ,GACZ,MAAA,MAAA,MAAA,OAAO,CAAC,QAAQ,mCAAI,MAAM,CAAC,OAAO,CAAC,OAAO,mCAAI,iBAAiB,mCAAI,GAAG,CAAC;IACzE,MAAM,aAAa,GAAG,iBAAiB,aAAjB,iBAAiB,cAAjB,iBAAiB,GAAI,GAAG,CAAC;IAC/C,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK;QAC5B,CAAC,CAAC,CAAC,QAAgB,EAAE,EAAE,CAAC,IAAA,eAAQ,EAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;QACtE,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;IACf,MAAM,YAAY,GAAG,YAAY,CAC/B,aAAa,EACb,OAAO,CAAC,UAAU;QAChB,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,GAAG,CAC9C,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CACzB,CACN,CAAC;IAEF,MAAM,cAAc,GAA8B;QAChD,UAAU,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO;QAChC,mBAAmB,EAAE,GAAG,EAAE,CAAC,GAAG;QAC9B,uEAAuE;QACvE,oBAAoB,EAAE,EAAE,CAAC,GAAG,CAAC,yBAAyB;YACpD,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE;KAC3B,CAAC;IAEF,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE;QAC/D,MAAM,IAAI,SAAS,CACjB,4DAA4D,CAC7D,CAAC;KACH;IACD,IAAI,gBAAgB,GAAG,2BAA2B,EAAE,CAAC;IACrD,SAAS,2BAA2B;;QAClC,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,aAAa;gBAChB,MAAM,IAAI,KAAK,CACb,mEAAmE,CACpE,CAAC;YACJ,MAAM,cAAc,GAClB,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC9D,MAAM,iBAAiB,GACrB,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAA,UAAU,CAAC,CAAC,CAAC,mCAAI,EAAE,CAAC;YAC5D,MAAM,kCAAkC,GAAG,kBAAkB;gBAC3D,CAAC,CAAC,IAAA,sCAA+B,EAAC,kBAAkB,CAAC;gBACrD,CAAC,CAAC,yBAAyB,CAAC;YAC9B,MAAM,cAAc,GAAG,kCAAkC,CACvD,cAAc,EACd,IAAI,CACL,CAAC;YACF,MAAM,iBAAiB,GAAG,OAAO,CAAC,cAAc,CAAC;iBAC9C,MAA2B,CAAC;YAC/B,OAAO,gBAAgB,CAAC;YAExB,SAAS,gBAAgB,CACvB,eAAyC,EACzC,kBAAuC;gBAEvC,OAAO,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAG;oBACzB,OAAO,EAAE;wBACP,OAAO;wBACP,MAAM,EAAE;4BACN,GAAG,MAAM;4BACT,OAAO,EAAE,eAAe;yBACzB;wBACD,yBAAyB;qBAC1B;oBACD,kCAAkC;oBAClC,kBAAkB;oBAClB,GAAG,iBAAiB;iBACrB,CAAC,CAAC;YACL,CAAC;SACF;IACH,CAAC;IAED;;;OAGG;IACH,IAAI,qBAAqB,GAAG,KAAK,CAAC;IAClC,SAAS,kCAAkC;QACzC,qBAAqB,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED,yDAAyD;IACzD,uBAAuB,EAAE,CAAC;IAC1B,SAAS,uBAAuB;QAC9B,MAAM,gBAAgB,GACpB,OAAO,CAAC,+BAA+B,CAA6B,CAAC;QACvE,gBAAgB,CAAC,OAAO,CAAC;YACvB,WAAW,EAAE,MAAM;YACnB,YAAY,CAAC,SAAiB;;gBAC5B,IAAI,IAAI,GAAG,SAAS,CAAC;gBACrB,4CAA4C;gBAC5C,uDAAuD;gBACvD,uEAAuE;gBACvE,IAAI,qBAAqB,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;oBACvD,IAAI;wBACF,IAAI,GAAG,IAAA,mBAAa,EAAC,IAAI,CAAC,CAAC;qBAC5B;oBAAC,OAAO,CAAC,EAAE;wBACV,mBAAmB;qBACpB;iBACF;gBACD,IAAI,GAAG,IAAA,uBAAgB,EAAC,IAAI,CAAC,CAAC;gBAC9B,OAAO,CAAA,MAAA,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,0CAAE,OAAO,KAAI,EAAE,CAAC;YAC9C,CAAC;YACD,0BAA0B,EAAE,IAAI;YAChC,4BAA4B,CAC1B,OAAO,EACP,MAAM,EACN,MAAM,EACN,OAAO,EACP,iBAAiB;gBAEjB,IAAA,aAAK,EACH,2GACG,MAAwB,CAAC,QAC5B,sCAAsC,OAAO,4BAA4B,iBAAiB,IAAI,CAC/F,CAAC;YACJ,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED,MAAM,sBAAsB,GAC1B,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IAEvE,MAAM,iBAAiB,GAAG,sBAAsB;QAC9C,CAAC,CAAC,EAAE,CAAC,oCAAoC,IAAI,EAAE,CAAC,iBAAiB;QACjE,CAAC,CAAC,EAAE,CAAC,iBAAiB,CAAC;IAEzB,SAAS,aAAa,CAAC,WAA0C;QAC/D,MAAM,cAAc,GAAG,iBAAiB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QACtE,MAAM,eAAe,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACvD,OAAO,IAAI,OAAO,CAAC,cAAc,EAAE,eAAe,EAAE,WAAW,CAAC,CAAC;IACnE,CAAC;IAED,SAAS,aAAa,CAAC,oBAAsC;QAC3D,MAAM,KAAK,GAAG,aAAa,CAAC,oBAAoB,CAAC,CAAC;QAClD,IAAI,OAAO,CAAC,QAAQ,EAAE;YACpB,mDAAmD;YACnD,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;SAC3C;aAAM;YACL,mCAAmC;YACnC,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED,mCAAmC;IACnC,IAAI,oBAAoB,CAAC,MAAM;QAAE,aAAa,CAAC,oBAAoB,CAAC,CAAC;IAErE,MAAM,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,KAAK,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;IACnE;;;OAGG;IACH,SAAS,gBAAgB,CAAC,IAAY;QACpC,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,YAAY,IAAI,CAAC,EAAE;YACrB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YACrC,QAAQ,GAAG,EAAE;gBACX,KAAK,KAAK,CAAC;gBACX,KAAK,KAAK;oBACR,OAAO,KAAK,CAAC;gBACf,KAAK,MAAM,CAAC;gBACZ,KAAK,MAAM;oBACT,OAAO,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;gBAC1C,KAAK,MAAM,CAAC;gBACZ,KAAK,MAAM;oBACT,OAAO,MAAM,CAAC;gBAChB,KAAK,MAAM,CAAC;gBACZ,KAAK,MAAM;oBACT,OAAO,MAAM,CAAC;aACjB;SACF;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAGD;;;OAGG;IACH,IAAI,SAAwC,CAAC;IAC7C,IAAI,WAIS,CAAC;IAEd,MAAM,oBAAoB,GACxB,EACD,CAAC,0BAA0B,CAAC,EAAE,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IAE/D,MAAM,oBAAoB,GAAG,IAAA,mDAA0B,EAAC;QACtD,QAAQ,EAAE,MAAA,OAAO,CAAC,eAAe,0CAAE,WAAW;QAC9C,QAAQ,EAAE,OAAO,CAAC,WAAW;KAC9B,CAAC,CAAC;IAEH,MAAM,UAAU,GAAG,IAAA,+BAAa,EAAC,MAAM,EAAE,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;IAE9D,+DAA+D;IAC/D,IAAI,CAAC,aAAa,EAAE;QAClB,MAAM,YAAY,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC/C,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAChD,MAAM,cAAc,GAAG,IAAA,mBAAY,EAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;QAEnE,mCAAmC;QACnC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YACzB,IAAI,cAAc,GAAG,CAAC,CAAC;YACvB,MAAM,YAAY,GAAG,IAAI,GAAG,CAC1B,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAC3D,CAAC;YAEF,MAAM,qBAAqB,GAAG,GAAG,EAAE;gBACjC,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE;oBACtC,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;oBACrC,OAAO,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;iBACpD;gBAED,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC;YAEF,8CAA8C;YAC9C,MAAM,WAAW,GACsD;gBACrE,iBAAiB,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC/C,kBAAkB,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;gBACnD,gBAAgB,EAAE,CAAC,QAAgB,EAAE,EAAE;oBACrC,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAC3C,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC3C,CAAC;gBACD,iBAAiB,CAAC,QAAgB;oBAChC,qEAAqE;oBACrE,IAAI,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAE1C,8CAA8C;oBAC9C,IAAI,QAAQ,KAAK,SAAS,EAAE;wBAC1B,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;wBACpC,IAAI,QAAQ,KAAK,SAAS;4BAAE,OAAO;wBAEnC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;wBAC9B,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;wBACrC,cAAc,EAAE,CAAC;qBAClB;oBAED,OAAO,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAChD,CAAC;gBACD,QAAQ,EAAE,cAAc;gBACxB,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,aAAa;gBACnC,cAAc,EAAE,IAAA,mBAAY,EAC1B,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC,CACjD;gBACD,UAAU,EAAE,IAAA,mBAAY,EAAC,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;gBAC3D,eAAe,EAAE,IAAA,mBAAY,EAC3B,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,CACnD;gBACD,QAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ;oBACvB,CAAC,CAAC,IAAA,mBAAY,EAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACpD,CAAC,CAAC,SAAS;gBACb,UAAU,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO;gBAChC,yBAAyB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,yBAAyB;gBACjE,mBAAmB,EAAE,GAAG,EAAE,CAAC,GAAG;gBAC9B,sBAAsB,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO;gBAC5C,qBAAqB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,CAAC;gBACrE,qBAAqB,EAAE,qBAAqB;gBAC5C,KAAK,EAAE,OAAO,CAAC,OAAO;aACvB,CAAC;YACF,MAAM,EACJ,kBAAkB,EAClB,mDAAmD,EACnD,8BAA8B,EAC9B,uBAAuB,EACvB,4BAA4B,GAC7B,GAAG,IAAA,4CAAuB,EAAC;gBAC1B,IAAI,EAAE,WAAW;gBACjB,oBAAoB;gBACpB,EAAE;gBACF,GAAG;gBACH,MAAM;gBACN,yBAAyB;gBACzB,OAAO;gBACP,UAAU;aACX,CAAC,CAAC;YACH,WAAW,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;YACpD,WAAW,CAAC,mDAAmD;gBAC7D,mDAAmD,CAAC;YACtD,WAAW,CAAC,8BAA8B;gBACxC,8BAA8B,CAAC;YAEjC,MAAM,QAAQ,GAAG,EAAE,CAAC,sBAAsB,CACxC,EAAE,CAAC,GAAG,CAAC,yBAAyB,EAChC,GAAG,CACJ,CAAC;YACF,MAAM,OAAO,GAAG,EAAE,CAAC,qBAAqB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAEhE,MAAM,iBAAiB,GAAG,CAAC,QAAgB,EAAE,QAAgB,EAAE,EAAE;gBAC/D,qFAAqF;gBACrF,wEAAwE;gBACxE,IACE,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC;oBAC5B,CAAC,uBAAuB,CAAC,QAAQ,CAAC,EAClC;oBACA,4BAA4B,CAAC,QAAQ,CAAC,CAAC;oBACvC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAC5B,+DAA+D;oBAC/D,cAAc,EAAE,CAAC;iBAClB;gBAED,MAAM,eAAe,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACxD,MAAM,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACpD,qDAAqD;gBACrD,IAAI,QAAQ,KAAK,gBAAgB,EAAE;oBACjC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,GAAG,CAAC,CAAC,CAAC;oBAChD,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;oBACrC,mDAAmD;oBACnD,cAAc,EAAE,CAAC;iBAClB;YACH,CAAC,CAAC;YAEF,IAAI,eAAe,GAA4B,SAAS,CAAC;YAEzD,SAAS,GAAG,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE;gBAC7C,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAElC,MAAM,aAAa,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC3C,IAAI,aAAa,KAAK,eAAe,EAAE;oBACrC,IAAA,aAAK,EACH,6DAA6D,QAAQ,EAAE,CACxE,CAAC;iBACH;gBAED,MAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;gBAE/C,iFAAiF;gBACjF,MAAM,WAAW,GAAG,OAAO;qBACxB,sBAAsB,CAAC,QAAQ,CAAC;qBAChC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAErD,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;gBAE1C,IAAA,aAAK,EACH,6HAA6H,EAC7H,aAAa,KAAK,YAAY,CAC/B,CAAC;gBAEF,eAAe,GAAG,YAAY,CAAC;gBAE/B,MAAM,cAAc,GAAG,iBAAiB,CACtC,WAAW,EACX,iBAAiB,CAClB,CAAC;gBACF,IAAI,cAAc,CAAC,MAAM;oBAAE,aAAa,CAAC,cAAc,CAAC,CAAC;gBAEzD,IAAI,MAAM,CAAC,WAAW,EAAE;oBACtB,OAAO,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;iBACrC;gBAED,+CAA+C;gBAC/C,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;oBACnC,MAAM,IAAI,SAAS,CACjB,2BAA2B,IAAA,eAAQ,EAAC,GAAG,EAAE,QAAQ,CAAC,IAAI;wBACpD,kEAAkE;wBAClE,yEAAyE;wBACzE,6CAA6C,CAChD,CAAC;iBACH;gBAED,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YACzE,CAAC,CAAC;YAEF,WAAW,GAAG,CAAC,IAAY,EAAE,QAAgB,EAAE,QAAgB,EAAE,EAAE;gBACjE,MAAM,kBAAkB,GAAG,IAAA,uBAAgB,EAAC,QAAQ,CAAC,CAAC;gBACtD,iBAAiB,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;gBAE5C,MAAM,IAAI,GAAG,OAAO,CAAC,sBAAsB,CACzC,kBAAkB,EAClB,QAAQ,CACT,CAAC;gBACF,MAAM,IAAI,GAAG,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACpE,MAAM,OAAO,GAAG,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAExE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;YAC3B,CAAC,CAAC;SACH;aAAM;YACL,MAAM,GAAG,GAA2C;gBAClD,GAAG,EAAE,CAAC,GAAG;gBACT,GAAG,cAAc;gBACjB,QAAQ,EAAE,CAAC,QAAgB,EAAE,EAAE;oBAC7B,MAAM,aAAa,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACjD,IAAI,aAAa,KAAK,SAAS;wBAAE,OAAO,aAAa,CAAC;oBACtD,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;oBAC1C,IAAI,QAAQ;wBAAE,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;oBACnD,OAAO,QAAQ,CAAC;gBAClB,CAAC;gBACD,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,aAAa;gBACnC,cAAc,EAAE,IAAA,mBAAY,EAC1B,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC,CACjD;gBACD,UAAU,EAAE,IAAA,mBAAY,EAAC,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;gBAC3D,eAAe,EAAE,IAAA,mBAAY,EAC3B,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,CACnD;gBACD,WAAW,EAAE,IAAA,mBAAY,EAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACrE,QAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ;oBACvB,CAAC,CAAC,IAAA,mBAAY,EAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACpD,CAAC,CAAC,SAAS;aACd,CAAC;YAEF,MAAM,IAAI,GAAqB,EAAE,CAAC,6BAA6B;gBAC7D,CAAC,CAAC,EAAE,CAAC,6BAA6B,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC;gBACvD,CAAC,CAAC;oBACE,GAAG,GAAG;oBACN,aAAa,EAAE,CAAC,QAAQ,EAAE,eAAe,EAAE,EAAE;wBAC3C,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;wBACxC,IAAI,QAAQ,KAAK,SAAS;4BAAE,OAAO;wBACnC,OAAO,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;oBAClE,CAAC;oBACD,qBAAqB,EAAE,GAAG,EAAE,CAAC,IAAA,uBAAgB,EAAC,IAAA,cAAO,EAAC,QAAQ,CAAC,CAAC;oBAChE,qBAAqB,EAAE,GAAG,EAAE,CAC1B,IAAA,uBAAgB,EACd,IAAA,WAAI,EACF,IAAA,cAAO,EAAC,QAAQ,CAAC,EACjB,EAAE,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,CAAC,CACzC,CACF;oBACH,yBAAyB,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,yBAAyB;iBAC/D,CAAC;YACN,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC;YAC7B,MAAM,EACJ,kBAAkB,EAClB,8BAA8B,EAC9B,uBAAuB,EACvB,4BAA4B,GAC7B,GAAG,IAAA,4CAAuB,EAAC;gBAC1B,IAAI;gBACJ,GAAG;gBACH,MAAM;gBACN,EAAE;gBACF,oBAAoB;gBACpB,yBAAyB;gBACzB,OAAO;gBACP,UAAU;aACX,CAAC,CAAC;YACH,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;YAC7C,IAAI,CAAC,8BAA8B,GAAG,8BAA8B,CAAC;YAErE,kEAAkE;YAClE,IAAI,cAAc,GAAG,EAAE,CAAC,wBAAwB;gBAC9C,CAAC,CAAC,EAAE,CAAC,wBAAwB,CAAC;oBAC1B,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;oBACpC,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,IAAI;oBACJ,4BAA4B,EAAE,MAAM,CAAC,MAAM;oBAC3C,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;iBAC5C,CAAC;gBACJ,CAAC,CAAC,EAAE,CAAC,8CAA8C,CAC/C,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EACzB,MAAM,CAAC,OAAO,EACd,IAAI,EACJ,SAAS,EACT,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,iBAAiB,CACzB,CAAC;YAEN,sCAAsC;YACtC,MAAM,kBAAkB,GACtB,OAAO,YAAY,KAAK,UAAU;gBAChC,CAAC,CAAC,YAAY,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;gBAC3C,CAAC,CAAC,YAAY,CAAC;YAEnB,6CAA6C;YAC7C,MAAM,iBAAiB,GAAG,CAAC,QAAgB,EAAE,QAAgB,EAAE,EAAE;gBAC/D,MAAM,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACpD,MAAM,eAAe,GAAG,gBAAgB,KAAK,QAAQ,CAAC;gBACtD,IAAI,eAAe,EAAE;oBACnB,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;iBACtC;gBAED,qEAAqE;gBACrE,IAAI,oBAAoB,GAAG,KAAK,CAAC;gBACjC,IACE,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC;oBAC5B,CAAC,uBAAuB,CAAC,QAAQ,CAAC,EAClC;oBACA,4BAA4B,CAAC,QAAQ,CAAC,CAAC;oBACvC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAC5B,oBAAoB,GAAG,IAAI,CAAC;iBAC7B;gBAED,oCAAoC;gBACpC,IAAI,oBAAoB,IAAI,eAAe,EAAE;oBAC3C,cAAc,GAAG,EAAE,CAAC,8CAA8C,CAChE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EACzB,MAAM,CAAC,OAAO,EACd,IAAI,EACJ,cAAc,EACd,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,iBAAiB,CACzB,CAAC;iBACH;YACH,CAAC,CAAC;YAEF,SAAS,GAAG,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE;gBAC7C,IAAI,OAAO,GAAG,EAAE,CAAC;gBACjB,IAAI,MAAM,GAAG,EAAE,CAAC;gBAEhB,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAElC,MAAM,UAAU,GAAG,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;gBAC1D,IAAI,CAAC,UAAU;oBACb,MAAM,IAAI,SAAS,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAC;gBAE1D,MAAM,OAAO,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;gBAC5C,MAAM,WAAW,GAAG,EAAE,CAAC,qBAAqB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;gBAClE,MAAM,cAAc,GAAG,iBAAiB,CACtC,WAAW,EACX,iBAAiB,CAClB,CAAC;gBACF,IAAI,cAAc,CAAC,MAAM;oBAAE,aAAa,CAAC,cAAc,CAAC,CAAC;gBAEzD,MAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAChC,UAAU,EACV,CAAC,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE,EAAE;oBACjC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;wBACzB,MAAM,GAAG,IAAI,CAAC;qBACf;yBAAM;wBACL,OAAO,GAAG,IAAI,CAAC;qBAChB;oBAED,IAAI,OAAO,CAAC,IAAI;wBAAE,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC;gBAClE,CAAC,EACD,SAAS,EACT,SAAS,EACT,kBAAkB,CACnB,CAAC;gBAEF,IAAI,MAAM,CAAC,WAAW,EAAE;oBACtB,OAAO,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;iBACrC;gBAED,+DAA+D;gBAC/D,IAAI,OAAO,KAAK,EAAE,EAAE;oBAClB,IAAI,OAAO,CAAC,+BAA+B,CAAC,UAAU,CAAC,EAAE;wBACvD,MAAM,IAAI,SAAS,CACjB,iDAAiD,IAAA,eAAQ,EACvD,GAAG,EACH,QAAQ,CACT,EAAE,CACJ,CAAC;qBACH;oBAED,MAAM,IAAI,SAAS,CACjB,2BAA2B,IAAA,eAAQ,EAAC,GAAG,EAAE,QAAQ,CAAC,IAAI;wBACpD,kEAAkE;wBAClE,yEAAyE;wBACzE,6CAA6C,CAChD,CAAC;iBACH;gBAED,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YAClC,CAAC,CAAC;YAEF,WAAW,GAAG,CAAC,IAAY,EAAE,QAAgB,EAAE,QAAgB,EAAE,EAAE;gBACjE,MAAM,kBAAkB,GAAG,IAAA,uBAAgB,EAAC,QAAQ,CAAC,CAAC;gBACtD,iBAAiB,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;gBAE5C,MAAM,UAAU,GAAG,cAAc,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;gBACpE,IAAI,CAAC,UAAU;oBACb,MAAM,IAAI,SAAS,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAC;gBAE1D,MAAM,IAAI,GAAG,kBAAkB,CAAC,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;gBAC1D,MAAM,OAAO,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC,cAAc,EAAE,CAAC;gBAC7D,MAAM,MAAM,GAAG,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAEjD,IAAI,CAAC,MAAM;oBAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;gBAE9C,MAAM,IAAI,GAAG,OAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBAC7D,MAAM,UAAU,GAAG;oBACjB,GAAG,IAAI,CAAC,sBAAsB,EAAE;oBAChC,GAAG,IAAI,CAAC,iBAAiB,EAAE;iBAC5B,CAAC;gBAEF,OAAO;oBACL,IAAI,EAAE,UAAU,CAAC,MAAM;wBACrB,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;wBAChE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC;oBAC9B,OAAO,EAAE,EAAE,CAAC,oBAAoB,CAC9B,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CACtD;iBACF,CAAC;YACJ,CAAC,CAAC;YAEF,kDAAkD;YAClD,IAAI,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE;gBAC9C,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;oBACtB,sCAAsC;oBACrC,cAAc,CAAC,UAAU,EAAU,CAAC,aAAa,EAAE,CAAC;gBACvD,CAAC,CAAC,CAAC;aACJ;SACF;KACF;SAAM;QACL,WAAW,GAAG,GAAG,EAAE;YACjB,MAAM,IAAI,SAAS,CACjB,uDAAuD,CACxD,CAAC;QACJ,CAAC,CAAC;KACH;IAED,SAAS,oCAAoC,CAC3C,kBAAmC,EACnC,kBAAuC;QAEvC,MAAM,eAAe,GAAG,EAAE,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;QAC9C,IAAI,kBAAkB,KAAK,SAAS;YAClC,eAAe,CAAC,MAAM,GAAG,kBAAkB,CAAC;QAC9C,IAAI,gBAAgB,GAAG,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CACrC,eAAe,EACf,kBAAkB,CACnB,CAAC;QACF,IAAI,iBAAiB,GAAG,IAAA,mBAAY,EAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;YACvD,CAAC,CAAC,IAAA,6CAAuB,EAAC,EAAE,EAAE;gBAC1B,eAAe;gBACf,iBAAiB,EAAE,IAAI;gBACvB,YAAY,EAAE,YAAkD;aACjE,CAAC;YACJ,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,CAAC,IAAY,EAAE,QAAgB,EAAgB,EAAE;YACtD,IAAI,MAA2B,CAAC;YAChC,IAAI,gBAAgB,EAAE;gBACpB,MAAM,GAAG,gBAAgB,CAAC,SAAS,CAAC,IAAI,EAAE;oBACxC,QAAQ;iBACT,CAAC,CAAC;aACJ;iBAAM,IAAI,iBAAiB,EAAE;gBAC5B,MAAM,GAAG,iBAAiB,CACxB,IAAI,EACJ;oBACE,QAAQ;iBACT,EACD,kBAAkB,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CACzD,CAAC;aACH;iBAAM;gBACL,MAAM,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,EAAE;oBAChC,QAAQ;oBACR,eAAe;oBACf,iBAAiB,EAAE,IAAI;oBACvB,YAAY,EAAE,YAAkD;iBACjE,CAAC,CAAC;aACJ;YAED,MAAM,cAAc,GAAG,iBAAiB,CACtC,MAAM,CAAC,WAAW,IAAI,EAAE,EACxB,iBAAiB,CAClB,CAAC;YACF,IAAI,cAAc,CAAC,MAAM;gBAAE,aAAa,CAAC,cAAc,CAAC,CAAC;YAEzD,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,aAAuB,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC,CAAC;IACJ,CAAC;IAED,iFAAiF;IACjF,iEAAiE;IACjE,MAAM,sCAAsC,GAC1C,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC;IACnD,mCAAmC;IACnC,MAAM,iCAAiC,GAAG,CAAC,CACzC,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC,UAAU,CAAC,MAAM;QAC9C,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC;QACxE,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC;QACxE,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC,UAAU,CAAC,MAAM,CAC/C,CAAC;IACF;;;OAGG;IACH,MAAM,gBAAgB,GACpB,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC;QACxE,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ;YACrB,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACtD,MAAM,sBAAsB,GAAG,oCAAoC,CACjE,EAAE,CAAC,UAAU,CAAC,QAAQ,CACvB,CAAC;IACF,MAAM,0BAA0B,GAAG,oCAAoC,CACrE,EAAE,CAAC,UAAU,CAAC,QAAQ,EACtB,SAAS,CACV,CAAC;IACF,MAAM,qBAAqB,GAAG,oCAAoC,CAChE,EAAE,CAAC,UAAU,CAAC,QAAQ,EACtB,SAAS,CACV,CAAC;IACF,mCAAmC;IACnC,MAAM,iBAAiB,GAAG,oCAAoC,CAC5D,EAAE,CAAC,UAAU,CAAC,MAAM,IAAI,EAAE,CAAC,UAAU,CAAC,MAAM,IAAI,EAAE,CAAC,UAAU,CAAC,MAAM,CACrE,CAAC;IACF,MAAM,sBAAsB,GAAG,oCAAoC,EAAE,CAAC;IAEtE,6CAA6C;IAC7C,SAAS,OAAO,CAAC,IAAY,EAAE,QAAgB,EAAE,UAAU,GAAG,CAAC;QAC7D,MAAM,kBAAkB,GAAG,IAAA,uBAAgB,EAAC,QAAQ,CAAC,CAAC;QACtD,MAAM,cAAc,GAClB,oBAAoB,CAAC,mCAAmC,CACtD,kBAAkB,CACnB,CAAC;QACJ,IAAI,KAAK,GAAuB,EAAE,CAAC;QACnC,IAAI,SAAS,GAAuB,EAAE,CAAC;QACvC,IAAI,WAAW,GAAG,IAAI,CAAC;QACvB,IAAI,SAAS,EAAE;YACb,iEAAiE;YACjE,CAAC,KAAK,EAAE,SAAS,EAAE,WAAW,CAAC,GAAG,SAAS,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;SACvE;QACD,+EAA+E;QAC/E,IACE,cAAc,CAAC,UAAU,KAAK,KAAK;YACnC,CAAC,sCAAsC,IAAI,WAAW,CAAC,EACvD;YACA,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,sBAAsB,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;SACvE;aAAM,IACL,cAAc,CAAC,UAAU,KAAK,KAAK;YACnC,CAAC,iCAAiC,IAAI,WAAW,CAAC,EAClD;YACA,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,iBAAiB,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;SAClE;aAAM,IAAI,WAAW,EAAE;YACtB,+DAA+D;YAC/D,MAAM,cAAc,GAAG,IAAA,4CAAc,EAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;YAClE,CAAC,KAAK,EAAE,SAAS,CAAC;gBAChB,cAAc,KAAK,SAAS;oBAC1B,CAAC,CAAC,0BAA0B,CAAC,IAAI,EAAE,kBAAkB,CAAC;oBACtD,CAAC,CAAC,cAAc,KAAK,SAAS;wBAC9B,CAAC,CAAC,qBAAqB,CAAC,IAAI,EAAE,kBAAkB,CAAC;wBACjD,CAAC,CAAC,cAAc,KAAK,KAAK;4BAC1B,CAAC,CAAC,sBAAsB,CAAC,IAAI,EAAE,kBAAkB,CAAC;4BAClD,CAAC,CAAC,cAAc,KAAK,KAAK;gCAC1B,CAAC,CAAC,iBAAiB,CAAC,IAAI,EAAE,kBAAkB,CAAC;gCAC7C,CAAC,CAAC,sBAAsB,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;SACxD;QACD,MAAM,MAAM,GAAG,YAAY,CACzB,KAAM,EACN,kBAAkB,EAClB,SAAU,EACV,gBAAgB,CACjB,CAAC;QACF,WAAW,CAAC,GAAG,CAAC,kBAAkB,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;QACzD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,IAAI,MAAM,GAAG,IAAI,CAAC;IAClB,MAAM,OAAO,GAAG,CAAC,OAAiB,EAAE,EAAE,CACpC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;IACxD,MAAM,OAAO,GAAG,CAAC,QAAgB,EAAE,EAAE;QACnC,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QACzB,MAAM,GAAG,GAAG,IAAA,cAAO,EAAC,QAAQ,CAAC,CAAC;QAC9B,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACrC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;SACtD;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IAEF,SAAS,mBAAmB,CAAC,MAAwB;QACnD,iBAAiB,CAAC,IAAI,CAAC;YACrB,GAAG,MAAM;YACT,iBAAiB,EAAE,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CACpD,IAAA,uBAAgB,EAAC,CAAC,CAAC,CACpB;SACF,CAAC,CAAC;IACL,CAAC;IAED,MAAM,kBAAkB,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,CAEjC,OAAO,CAAC,+CAA+C,CACxD,CAAC,aAAa,CAAC;QACd,UAAU;QACV,YAAY,EAAE,OAAO,CAAC,YAAY;QAClC,qCAAqC,EACnC,OAAO,CAAC,+BAA+B;KAC1C,CAAC,CACH,CAAC;IACF,MAAM,mBAAmB,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,CAElC,OAAO,CAAC,kDAAkD,CAC3D,CAAC,eAAe,CACf,OAAO,CAAC,+BAA+B,EACvC,kBAAkB,EAAE,CACrB,CACF,CAAC;IACF,MAAM,gBAAgB,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,CAE/B,OAAO,CAAC,8CAA8C,CACvD,CAAC,eAAe,CAAC;QAChB,UAAU;QACV,YAAY,EAAE,OAAO,CAAC,YAAY;QAClC,eAAe,EAAE,kBAAkB,EAAE;KACtC,CAAC,CACH,CAAC;IAEF,OAAO;QACL,CAAC,qBAAqB,CAAC,EAAE,IAAI;QAC7B,EAAE;QACF,YAAY,EAAE,QAAQ;QACtB,MAAM;QACN,OAAO;QACP,WAAW;QACX,OAAO;QACP,OAAO;QACP,OAAO;QACP,cAAc;QACd,oBAAoB;QACpB,eAAe;QACf,mBAAmB;QACnB,uBAAuB;QACvB,kCAAkC;QAClC,aAAa;QACb,yBAAyB;QACzB,kBAAkB;QAClB,mBAAmB;QACnB,gBAAgB;QAChB,UAAU;KACX,CAAC;AACJ,CAAC;AAl5BD,8DAk5BC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,aAAqB,EAAE,MAAgB;IAC3D,OAAO,CAAC,QAAgB,EAAE,EAAE;QAC1B,MAAM,OAAO,GAAG,IAAA,eAAQ,EAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAClD,MAAM,IAAI,GAAG,IAAA,uBAAgB,EAAC,OAAO,CAAC,CAAC;QAEvC,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CACzB,YAAwC,EACxC,UAAoB,EACpB,OAAgB,EAChB,iBAA2D;IAE3D,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;IACjC,0GAA0G;IAC1G,uEAAuE;IACvE,uEAAuE;IACvE,KAAK,MAAM,SAAS,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;QACxD,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,IAAA,qBAAc,EAAC,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE;YACzE,mEAAmE;YACnE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SACxB;KACF;IAED,2BAA2B;IAC3B,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;QACtB,iBAAiB,CAAC,GAAG,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;KACpD;IAED,IAAI,YAAY,EAAE;QAChB,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC;YAClC,GAAG,IAAI;YACP,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;SACnC,CAAC,CAAC;QAEH,2CAA2C;QAC3C,KAAK,MAAM,GAAG,IAAI,mBAAmB,EAAE;YACrC,MAAM,GAAG,GAAG,MAAM,CAAC,wBAAwB,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YACrE,OAAO,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC/B,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,EAAE,GAAI,CAAC,CAAC;SACtD;KACF;AACH,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CACxB,GAAW,EACX,OAAgB,EAChB,eAAyD;IAEzD,MAAM,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,eAAe,CAAC;IAEvD,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,UAAU,CAAM,EAAE,QAAQ;QAClD,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC;YAAE,OAAO,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;QAEvD,wBAAwB,CAAC,OAAO,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;QAE/C,MAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;QAE5B,CAAC,CAAC,QAAQ,GAAG,UAAU,IAAY,EAAE,QAAgB;YACnD,IAAA,aAAK,EAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;YAEnC,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC/C,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC/C,CAAC,CAAC;QAEF,OAAO,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;IAC1B,CAAC,CAAC;AACJ,CAAC;AAOD;;GAEG;AACH,SAAS,YAAY,CACnB,UAAkB,EAClB,QAAgB,EAChB,SAAiB,EACjB,gBAA8C;IAE9C,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAC3B,eAAe,CAAC,SAAS,EAAE,QAAQ,CAAC,EACpC,MAAM,CACP,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACrB,MAAM,gBAAgB,GAAG,mEAAmE,SAAS,EAAE,CAAC;IACxG,+HAA+H;IAC/H,oGAAoG;IACpG,MAAM,MAAM,GAAG,uBAAuB,CAAC;IACvC,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;IACnC,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAA,eAAQ,EAAC,QAAQ,CAAC,CAAC;IAChD,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAA,cAAO,EAAC,QAAQ,CAAC,CAAC;IAC3C,MAAM,SAAS,GAAG,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IACrD,MAAM,iBAAiB,GACrB,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,SAAS,GAAG,MAAM,CAAC;IAC1D,MAAM,qCAAqC,GACzC,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC;IAC1C;;;;;OAKG;IACH,IACE,UAAU,CAAC,MAAM,CAAC,CAAC,qCAAqC,EAAE,YAAY,CAAC;QACvE,MAAM,EACN;QACA,OAAO,CACL,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,qCAAqC,CAAC;YAC3D,gBAAgB,CACjB,CAAC;KACH;IACD,6JAA6J;IAC7J,MAAM,kCAAkC,GACtC,YAAY,GAAG,SAAS,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC;IACrD,IACE,UAAU,CAAC,MAAM,CAAC,CAAC,kCAAkC,EAAE,YAAY,CAAC;QACpE,MAAM,EACN;QACA,OAAO,CACL,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,kCAAkC,CAAC;YACxD,gBAAgB,CACjB,CAAC;KACH;IAED,OAAO,GAAG,UAAU,KAAK,gBAAgB,EAAE,CAAC;AAC9C,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,aAAqB,EAAE,QAAgB;IAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IAC5C,SAAS,CAAC,IAAI,GAAG,QAAQ,CAAC;IAC1B,SAAS,CAAC,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/B,OAAO,SAAS,CAAC,UAAU,CAAC;IAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACnC,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CACxB,WAAsC,EACtC,OAA2B;IAE3B,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAC9B,OAAO,CAAC,KAAK,CACX,CAAC,CAAC,EAAE,EAAE;;QACJ,OAAA,CAAC,CAAC,CAAC,CAAC,iBAAiB;YACnB,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAA,CAAC,CAAC,IAAI,0CAAE,QAAS,CAAC,KAAK,CAAC,CAAC,CAAC;YACxD,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;KAAA,CAC9C,CACF,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAS,kBAAkB,CACzB,EAAY,EACZ,UAA0B,EAC1B,QAAgB;IAEhB,IAAI,OAAO,GAAa,UAAU,CAAC;IAEnC,KAAK,EAAE,OAAO,IAAI,EAAE;QAClB,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE;YACnD,MAAM,KAAK,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;YACnC,IAAI,KAAK,GAAG,QAAQ;gBAAE,MAAM;YAE5B,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAC3B,IAAI,QAAQ,IAAI,GAAG,EAAE;gBACnB,OAAO,GAAG,KAAK,CAAC;gBAChB,SAAS,KAAK,CAAC;aAChB;SACF;QAED,OAAO,OAAO,CAAC;KAChB;AACH,CAAC;AAED;;;;;;;;;;;GAWG;AACI,MAAM,cAAc,GAA4B,CACrD,aAAsB,EACtB,EAAE,CAAE,OAAO,CAAC,OAAO,CAA4B,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;AAFnE,QAAA,cAAc,kBAEqD", "sourcesContent": ["import { relative, basename, extname, dirname, join } from 'path';\nimport { Module } from 'module';\nimport * as util from 'util';\nimport { fileURLToPath } from 'url';\n\nimport type * as _sourceMapSupport from '@cspotcode/source-map-support';\nimport { BaseError } from 'make-error';\nimport type * as _ts from 'typescript';\n\nimport type { Transpiler, TranspilerFactory } from './transpilers/types';\nimport {\n  cachedLookup,\n  createProjectLocalResolveHelper,\n  hasOwnProperty,\n  normalizeSlashes,\n  once,\n  parse,\n  ProjectLocalResolveHelper,\n  split,\n  versionGteLt,\n  yn,\n} from './util';\nimport { findAndReadConfig, loadCompiler } from './configuration';\nimport type { TSCommon, TSInternal } from './ts-compiler-types';\nimport {\n  createModuleTypeClassifier,\n  ModuleTypeClassifier,\n} from './module-type-classifier';\nimport { createResolverFunctions } from './resolver-functions';\nimport type { createEsmHooks as createEsmHooksFn } from './esm';\nimport {\n  installCommonjsResolveHooksIfNecessary,\n  ModuleConstructorWithInternals,\n} from './cjs-resolve-hooks';\nimport { classifyModule } from './node-module-type-classifier';\nimport type * as _nodeInternalModulesEsmResolve from '../dist-raw/node-internal-modules-esm-resolve';\nimport type * as _nodeInternalModulesEsmGetFormat from '../dist-raw/node-internal-modules-esm-get_format';\nimport type * as _nodeInternalModulesCjsLoader from '../dist-raw/node-internal-modules-cjs-loader';\nimport { Extensions, getExtensions } from './file-extensions';\nimport { createTsTranspileModule } from './ts-transpile-module';\n\nexport { TSCommon };\nexport {\n  createRepl,\n  CreateReplOptions,\n  ReplService,\n  EvalAwarePartialHost,\n} from './repl';\nexport type {\n  TranspilerModule,\n  TranspilerFactory,\n  CreateTranspilerOptions,\n  TranspileOutput,\n  TranspileOptions,\n  Transpiler,\n} from './transpilers/types';\nexport type {\n  NodeLoaderHooksAPI1,\n  NodeLoaderHooksAPI2,\n  NodeLoaderHooksFormat,\n} from './esm';\n\n/**\n * Does this version of node obey the package.json \"type\" field\n * and throw ERR_REQUIRE_ESM when attempting to require() an ESM modules.\n */\nconst engineSupportsPackageTypeField =\n  parseInt(process.versions.node.split('.')[0], 10) >= 12;\n\n/**\n * Assert that script can be loaded as CommonJS when we attempt to require it.\n * If it should be loaded as ESM, throw ERR_REQUIRE_ESM like node does.\n *\n * Loaded conditionally so we don't need to support older node versions\n */\nlet assertScriptCanLoadAsCJS: (\n  service: Service,\n  module: NodeJS.Module,\n  filename: string\n) => void = engineSupportsPackageTypeField\n  ? (\n      require('../dist-raw/node-internal-modules-cjs-loader') as typeof _nodeInternalModulesCjsLoader\n    ).assertScriptCanLoadAsCJSImpl\n  : () => {\n      /* noop */\n    };\n\n/**\n * Registered `ts-node` instance information.\n */\nexport const REGISTER_INSTANCE = Symbol.for('ts-node.register.instance');\n\n/**\n * Expose `REGISTER_INSTANCE` information on node.js `process`.\n */\ndeclare global {\n  namespace NodeJS {\n    interface Process {\n      [REGISTER_INSTANCE]?: Service;\n    }\n  }\n}\n\n/** @internal */\nexport const env = process.env as ProcessEnv;\n/**\n * Declare all env vars, to aid discoverability.\n * If an env var affects ts-node's behavior, it should not be buried somewhere in our codebase.\n * @internal\n */\nexport interface ProcessEnv {\n  TS_NODE_DEBUG?: string;\n  TS_NODE_CWD?: string;\n  /** @deprecated */\n  TS_NODE_DIR?: string;\n  TS_NODE_EMIT?: string;\n  TS_NODE_SCOPE?: string;\n  TS_NODE_SCOPE_DIR?: string;\n  TS_NODE_FILES?: string;\n  TS_NODE_PRETTY?: string;\n  TS_NODE_COMPILER?: string;\n  TS_NODE_COMPILER_OPTIONS?: string;\n  TS_NODE_IGNORE?: string;\n  TS_NODE_PROJECT?: string;\n  TS_NODE_SKIP_PROJECT?: string;\n  TS_NODE_SKIP_IGNORE?: string;\n  TS_NODE_PREFER_TS_EXTS?: string;\n  TS_NODE_IGNORE_DIAGNOSTICS?: string;\n  TS_NODE_TRANSPILE_ONLY?: string;\n  TS_NODE_TYPE_CHECK?: string;\n  TS_NODE_COMPILER_HOST?: string;\n  TS_NODE_LOG_ERROR?: string;\n  TS_NODE_HISTORY?: string;\n  TS_NODE_EXPERIMENTAL_REPL_AWAIT?: string;\n\n  NODE_NO_READLINE?: string;\n}\n\n/**\n * @internal\n */\nexport const INSPECT_CUSTOM = util.inspect.custom || 'inspect';\n\n/**\n * Debugging `ts-node`.\n */\nconst shouldDebug = yn(env.TS_NODE_DEBUG);\n/** @internal */\nexport const debug = shouldDebug\n  ? (...args: any) =>\n      console.log(`[ts-node ${new Date().toISOString()}]`, ...args)\n  : () => undefined;\nconst debugFn = shouldDebug\n  ? <T, U>(key: string, fn: (arg: T) => U) => {\n      let i = 0;\n      return (x: T) => {\n        debug(key, x, ++i);\n        return fn(x);\n      };\n    }\n  : <T, U>(_: string, fn: (arg: T) => U) => fn;\n\n/**\n * Export the current version.\n */\nexport const VERSION = require('../package.json').version;\n\n/**\n * Options for creating a new TypeScript compiler instance.\n\n * @category Basic\n */\nexport interface CreateOptions {\n  /**\n   * Behave as if invoked within this working directory.  Roughly equivalent to `cd $dir && ts-node ...`\n   *\n   * @default process.cwd()\n   */\n  cwd?: string;\n  /**\n   * Legacy alias for `cwd`\n   *\n   * @deprecated use `projectSearchDir` or `cwd`\n   */\n  dir?: string;\n  /**\n   * Emit output files into `.ts-node` directory.\n   *\n   * @default false\n   */\n  emit?: boolean;\n  /**\n   * Scope compiler to files within `scopeDir`.\n   *\n   * @default false\n   */\n  scope?: boolean;\n  /**\n   * @default First of: `tsconfig.json` \"rootDir\" if specified, directory containing `tsconfig.json`, or cwd if no `tsconfig.json` is loaded.\n   */\n  scopeDir?: string;\n  /**\n   * Use pretty diagnostic formatter.\n   *\n   * @default false\n   */\n  pretty?: boolean;\n  /**\n   * Use TypeScript's faster `transpileModule`.\n   *\n   * @default false\n   */\n  transpileOnly?: boolean;\n  /**\n   * **DEPRECATED** Specify type-check is enabled (e.g. `transpileOnly == false`).\n   *\n   * @default true\n   */\n  typeCheck?: boolean;\n  /**\n   * Use TypeScript's compiler host API instead of the language service API.\n   *\n   * @default false\n   */\n  compilerHost?: boolean;\n  /**\n   * Logs TypeScript errors to stderr instead of throwing exceptions.\n   *\n   * @default false\n   */\n  logError?: boolean;\n  /**\n   * Load \"files\" and \"include\" from `tsconfig.json` on startup.\n   *\n   * Default is to override `tsconfig.json` \"files\" and \"include\" to only include the entrypoint script.\n   *\n   * @default false\n   */\n  files?: boolean;\n  /**\n   * Specify a custom TypeScript compiler.\n   *\n   * @default \"typescript\"\n   */\n  compiler?: string;\n  /**\n   * Specify a custom transpiler for use with transpileOnly\n   */\n  transpiler?: string | [string, object];\n  /**\n   * Transpile with swc instead of the TypeScript compiler, and skip typechecking.\n   *\n   * Equivalent to setting both `transpileOnly: true` and `transpiler: 'ts-node/transpilers/swc'`\n   *\n   * For complete instructions: https://typestrong.org/ts-node/docs/transpilers\n   */\n  swc?: boolean;\n  /**\n   * Paths which should not be compiled.\n   *\n   * Each string in the array is converted to a regular expression via `new RegExp()` and tested against source paths prior to compilation.\n   *\n   * Source paths are normalized to posix-style separators, relative to the directory containing `tsconfig.json` or to cwd if no `tsconfig.json` is loaded.\n   *\n   * Default is to ignore all node_modules subdirectories.\n   *\n   * @default [\"(?:^|/)node_modules/\"]\n   */\n  ignore?: string[];\n  /**\n   * Path to TypeScript config file or directory containing a `tsconfig.json`.\n   * Similar to the `tsc --project` flag: https://www.typescriptlang.org/docs/handbook/compiler-options.html\n   */\n  project?: string;\n  /**\n   * Search for TypeScript config file (`tsconfig.json`) in this or parent directories.\n   */\n  projectSearchDir?: string;\n  /**\n   * Skip project config resolution and loading.\n   *\n   * @default false\n   */\n  skipProject?: boolean;\n  /**\n   * Skip ignore check, so that compilation will be attempted for all files with matching extensions.\n   *\n   * @default false\n   */\n  skipIgnore?: boolean;\n  /**\n   * JSON object to merge with TypeScript `compilerOptions`.\n   *\n   * @allOf [{\"$ref\": \"https://schemastore.azurewebsites.net/schemas/json/tsconfig.json#definitions/compilerOptionsDefinition/properties/compilerOptions\"}]\n   */\n  compilerOptions?: object;\n  /**\n   * Ignore TypeScript warnings by diagnostic code.\n   */\n  ignoreDiagnostics?: Array<number | string>;\n  /**\n   * Modules to require, like node's `--require` flag.\n   *\n   * If specified in `tsconfig.json`, the modules will be resolved relative to the `tsconfig.json` file.\n   *\n   * If specified programmatically, each input string should be pre-resolved to an absolute path for\n   * best results.\n   */\n  require?: Array<string>;\n  readFile?: (path: string) => string | undefined;\n  fileExists?: (path: string) => boolean;\n  transformers?:\n    | _ts.CustomTransformers\n    | ((p: _ts.Program) => _ts.CustomTransformers);\n  /**\n   * Allows the usage of top level await in REPL.\n   *\n   * Uses node's implementation which accomplishes this with an AST syntax transformation.\n   *\n   * Enabled by default when tsconfig target is es2018 or above. Set to false to disable.\n   *\n   * **Note**: setting to `true` when tsconfig target is too low will throw an Error.  Leave as `undefined`\n   * to get default, automatic behavior.\n   */\n  experimentalReplAwait?: boolean;\n  /**\n   * Override certain paths to be compiled and executed as CommonJS or ECMAScript modules.\n   * When overridden, the tsconfig \"module\" and package.json \"type\" fields are overridden, and\n   * the file extension is ignored.\n   * This is useful if you cannot use .mts, .cts, .mjs, or .cjs file extensions;\n   * it achieves the same effect.\n   *\n   * Each key is a glob pattern following the same rules as tsconfig's \"include\" array.\n   * When multiple patterns match the same file, the last pattern takes precedence.\n   *\n   * `cjs` overrides matches files to compile and execute as CommonJS.\n   * `esm` overrides matches files to compile and execute as native ECMAScript modules.\n   * `package` overrides either of the above to default behavior, which obeys package.json \"type\" and\n   * tsconfig.json \"module\" options.\n   */\n  moduleTypes?: ModuleTypes;\n  /**\n   * @internal\n   * Set by our configuration loader whenever a config file contains options that\n   * are relative to the config file they came from, *and* when other logic needs\n   * to know this.  Some options can be eagerly resolved to absolute paths by\n   * the configuration loader, so it is *not* necessary for their source to be set here.\n   */\n  optionBasePaths?: OptionBasePaths;\n  /**\n   * A function to collect trace messages from the TypeScript compiler, for example when `traceResolution` is enabled.\n   *\n   * @default console.log\n   */\n  tsTrace?: (str: string) => void;\n  /**\n   * Enable native ESM support.\n   *\n   * For details, see https://typestrong.org/ts-node/docs/imports#native-ecmascript-modules\n   */\n  esm?: boolean;\n  /**\n   * Re-order file extensions so that TypeScript imports are preferred.\n   *\n   * For example, when both `index.js` and `index.ts` exist, enabling this option causes `require('./index')` to resolve to `index.ts` instead of `index.js`\n   *\n   * @default false\n   */\n  preferTsExts?: boolean;\n  /**\n   * Like node's `--experimental-specifier-resolution`, , but can also be set in your `tsconfig.json` for convenience.\n   *\n   * For details, see https://nodejs.org/dist/latest-v18.x/docs/api/esm.html#customizing-esm-specifier-resolution-algorithm\n   */\n  experimentalSpecifierResolution?: 'node' | 'explicit';\n  /**\n   * Allow using voluntary `.ts` file extension in import specifiers.\n   *\n   * Typically, in ESM projects, import specifiers must have an emit extension, `.js`, `.cjs`, or `.mjs`,\n   * and we automatically map to the corresponding `.ts`, `.cts`, or `.mts` source file.  This is the\n   * recommended approach.\n   *\n   * However, if you really want to use `.ts` in import specifiers, and are aware that this may\n   * break tooling, you can enable this flag.\n   */\n  experimentalTsImportSpecifiers?: boolean;\n}\n\nexport type ModuleTypes = Record<string, ModuleTypeOverride>;\nexport type ModuleTypeOverride = 'cjs' | 'esm' | 'package';\n\n/** @internal */\nexport interface OptionBasePaths {\n  moduleTypes?: string;\n  transpiler?: string;\n  compiler?: string;\n  swc?: string;\n}\n\n/**\n * Options for registering a TypeScript compiler instance globally.\n\n * @category Basic\n */\nexport interface RegisterOptions extends CreateOptions {\n  /**\n   * Enable experimental features that re-map imports and require calls to support:\n   * `baseUrl`, `paths`, `rootDirs`, `.js` to `.ts` file extension mappings,\n   * `outDir` to `rootDir` mappings for composite projects and monorepos.\n   *\n   * For details, see https://github.com/TypeStrong/ts-node/issues/1514\n   */\n  experimentalResolver?: boolean;\n}\n\nexport type ExperimentalSpecifierResolution = 'node' | 'explicit';\n\n/**\n * Must be an interface to support `typescript-json-schema`.\n */\nexport interface TsConfigOptions\n  extends Omit<\n    RegisterOptions,\n    | 'transformers'\n    | 'readFile'\n    | 'fileExists'\n    | 'skipProject'\n    | 'project'\n    | 'dir'\n    | 'cwd'\n    | 'projectSearchDir'\n    | 'optionBasePaths'\n    | 'tsTrace'\n  > {}\n\n/**\n * Information retrieved from type info check.\n */\nexport interface TypeInfo {\n  name: string;\n  comment: string;\n}\n\n/**\n * Default register options, including values specified via environment\n * variables.\n * @internal\n */\nexport const DEFAULTS: RegisterOptions = {\n  cwd: env.TS_NODE_CWD ?? env.TS_NODE_DIR,\n  emit: yn(env.TS_NODE_EMIT),\n  scope: yn(env.TS_NODE_SCOPE),\n  scopeDir: env.TS_NODE_SCOPE_DIR,\n  files: yn(env.TS_NODE_FILES),\n  pretty: yn(env.TS_NODE_PRETTY),\n  compiler: env.TS_NODE_COMPILER,\n  compilerOptions: parse(env.TS_NODE_COMPILER_OPTIONS),\n  ignore: split(env.TS_NODE_IGNORE),\n  project: env.TS_NODE_PROJECT,\n  skipProject: yn(env.TS_NODE_SKIP_PROJECT),\n  skipIgnore: yn(env.TS_NODE_SKIP_IGNORE),\n  preferTsExts: yn(env.TS_NODE_PREFER_TS_EXTS),\n  ignoreDiagnostics: split(env.TS_NODE_IGNORE_DIAGNOSTICS),\n  transpileOnly: yn(env.TS_NODE_TRANSPILE_ONLY),\n  typeCheck: yn(env.TS_NODE_TYPE_CHECK),\n  compilerHost: yn(env.TS_NODE_COMPILER_HOST),\n  logError: yn(env.TS_NODE_LOG_ERROR),\n  experimentalReplAwait: yn(env.TS_NODE_EXPERIMENTAL_REPL_AWAIT) ?? undefined,\n  tsTrace: console.log.bind(console),\n};\n\n/**\n * TypeScript diagnostics error.\n */\nexport class TSError extends BaseError {\n  name = 'TSError';\n  diagnosticText!: string;\n  diagnostics!: ReadonlyArray<_ts.Diagnostic>;\n\n  constructor(\n    diagnosticText: string,\n    public diagnosticCodes: number[],\n    diagnostics: ReadonlyArray<_ts.Diagnostic> = []\n  ) {\n    super(`⨯ Unable to compile TypeScript:\\n${diagnosticText}`);\n    Object.defineProperty(this, 'diagnosticText', {\n      configurable: true,\n      writable: true,\n      value: diagnosticText,\n    });\n    Object.defineProperty(this, 'diagnostics', {\n      configurable: true,\n      writable: true,\n      value: diagnostics,\n    });\n  }\n\n  /**\n   * @internal\n   */\n  [INSPECT_CUSTOM]() {\n    return this.diagnosticText;\n  }\n}\n\nconst TS_NODE_SERVICE_BRAND = Symbol('TS_NODE_SERVICE_BRAND');\n\n/**\n * Primary ts-node service, which wraps the TypeScript API and can compile TypeScript to JavaScript\n */\nexport interface Service {\n  /** @internal */\n  [TS_NODE_SERVICE_BRAND]: true;\n  ts: TSCommon;\n  /** @internal */\n  compilerPath: string;\n  config: _ts.ParsedCommandLine;\n  options: RegisterOptions;\n  enabled(enabled?: boolean): boolean;\n  ignored(fileName: string): boolean;\n  compile(code: string, fileName: string, lineOffset?: number): string;\n  getTypeInfo(code: string, fileName: string, position: number): TypeInfo;\n  /** @internal */\n  configFilePath: string | undefined;\n  /** @internal */\n  moduleTypeClassifier: ModuleTypeClassifier;\n  /** @internal */\n  readonly shouldReplAwait: boolean;\n  /** @internal */\n  addDiagnosticFilter(filter: DiagnosticFilter): void;\n  /** @internal */\n  installSourceMapSupport(): void;\n  /** @internal */\n  enableExperimentalEsmLoaderInterop(): void;\n  /** @internal */\n  transpileOnly: boolean;\n  /** @internal */\n  projectLocalResolveHelper: ProjectLocalResolveHelper;\n  /** @internal */\n  getNodeEsmResolver: () => ReturnType<\n    typeof import('../dist-raw/node-internal-modules-esm-resolve').createResolve\n  >;\n  /** @internal */\n  getNodeEsmGetFormat: () => ReturnType<\n    typeof import('../dist-raw/node-internal-modules-esm-get_format').createGetFormat\n  >;\n  /** @internal */\n  getNodeCjsLoader: () => ReturnType<\n    typeof import('../dist-raw/node-internal-modules-cjs-loader').createCjsLoader\n  >;\n  /** @internal */\n  extensions: Extensions;\n}\n\n/**\n * Re-export of `Service` interface for backwards-compatibility\n * @deprecated use `Service` instead\n * @see {Service}\n */\nexport type Register = Service;\n\n/** @internal */\nexport interface DiagnosticFilter {\n  /** if true, filter applies to all files */\n  appliesToAllFiles: boolean;\n  /** Filter applies onto to these filenames.  Only used if appliesToAllFiles is false */\n  filenamesAbsolute: string[];\n  /** these diagnostic codes are ignored */\n  diagnosticsIgnored: number[];\n}\n\n/**\n * Create a new TypeScript compiler instance and register it onto node.js\n *\n * @category Basic\n */\nexport function register(opts?: RegisterOptions): Service;\n/**\n * Register TypeScript compiler instance onto node.js\n\n * @category Basic\n */\nexport function register(service: Service): Service;\nexport function register(\n  serviceOrOpts: Service | RegisterOptions | undefined\n): Service {\n  // Is this a Service or a RegisterOptions?\n  let service = serviceOrOpts as Service;\n  if (!(serviceOrOpts as Service)?.[TS_NODE_SERVICE_BRAND]) {\n    // Not a service; is options\n    service = create((serviceOrOpts ?? {}) as RegisterOptions);\n  }\n\n  const originalJsHandler = require.extensions['.js'];\n\n  // Expose registered instance globally.\n  process[REGISTER_INSTANCE] = service;\n\n  // Register the extensions.\n  registerExtensions(\n    service.options.preferTsExts,\n    service.extensions.compiled,\n    service,\n    originalJsHandler\n  );\n\n  installCommonjsResolveHooksIfNecessary(service);\n\n  // Require specified modules before start-up.\n  (Module as ModuleConstructorWithInternals)._preloadModules(\n    service.options.require\n  );\n\n  return service;\n}\n\n/**\n * Create TypeScript compiler instance.\n *\n * @category Basic\n */\nexport function create(rawOptions: CreateOptions = {}): Service {\n  const foundConfigResult = findAndReadConfig(rawOptions);\n  return createFromPreloadedConfig(foundConfigResult);\n}\n\n/** @internal */\nexport function createFromPreloadedConfig(\n  foundConfigResult: ReturnType<typeof findAndReadConfig>\n): Service {\n  const {\n    configFilePath,\n    cwd,\n    options,\n    config,\n    compiler,\n    projectLocalResolveDir,\n    optionBasePaths,\n  } = foundConfigResult;\n\n  const projectLocalResolveHelper = createProjectLocalResolveHelper(\n    projectLocalResolveDir\n  );\n\n  const ts = loadCompiler(compiler);\n\n  // Experimental REPL await is not compatible targets lower than ES2018\n  const targetSupportsTla = config.options.target! >= ts.ScriptTarget.ES2018;\n  if (options.experimentalReplAwait === true && !targetSupportsTla) {\n    throw new Error(\n      'Experimental REPL await is not compatible with targets lower than ES2018'\n    );\n  }\n  // Top-level await was added in TS 3.8\n  const tsVersionSupportsTla = versionGteLt(ts.version, '3.8.0');\n  if (options.experimentalReplAwait === true && !tsVersionSupportsTla) {\n    throw new Error(\n      'Experimental REPL await is not compatible with TypeScript versions older than 3.8'\n    );\n  }\n\n  const shouldReplAwait =\n    options.experimentalReplAwait !== false &&\n    tsVersionSupportsTla &&\n    targetSupportsTla;\n\n  // swc implies two other options\n  // typeCheck option was implemented specifically to allow overriding tsconfig transpileOnly from the command-line\n  // So we should allow using typeCheck to override swc\n  if (options.swc && !options.typeCheck) {\n    if (options.transpileOnly === false) {\n      throw new Error(\n        \"Cannot enable 'swc' option with 'transpileOnly: false'.  'swc' implies 'transpileOnly'.\"\n      );\n    }\n    if (options.transpiler) {\n      throw new Error(\n        \"Cannot specify both 'swc' and 'transpiler' options.  'swc' uses the built-in swc transpiler.\"\n      );\n    }\n  }\n\n  const readFile = options.readFile || ts.sys.readFile;\n  const fileExists = options.fileExists || ts.sys.fileExists;\n  // typeCheck can override transpileOnly, useful for CLI flag to override config file\n  const transpileOnly =\n    (options.transpileOnly === true || options.swc === true) &&\n    options.typeCheck !== true;\n  let transpiler: RegisterOptions['transpiler'] | undefined = undefined;\n  let transpilerBasePath: string | undefined = undefined;\n  if (options.transpiler) {\n    transpiler = options.transpiler;\n    transpilerBasePath = optionBasePaths.transpiler;\n  } else if (options.swc) {\n    transpiler = require.resolve('./transpilers/swc.js');\n    transpilerBasePath = optionBasePaths.swc;\n  }\n  const transformers = options.transformers || undefined;\n  const diagnosticFilters: Array<DiagnosticFilter> = [\n    {\n      appliesToAllFiles: true,\n      filenamesAbsolute: [],\n      diagnosticsIgnored: [\n        6059, // \"'rootDir' is expected to contain all source files.\"\n        18002, // \"The 'files' list in config file is empty.\"\n        18003, // \"No inputs were found in config file.\"\n        ...(options.experimentalTsImportSpecifiers\n          ? [\n              2691, // \"An import path cannot end with a '.ts' extension. Consider importing '<specifier without ext>' instead.\"\n            ]\n          : []),\n        ...(options.ignoreDiagnostics || []),\n      ].map(Number),\n    },\n  ];\n\n  const configDiagnosticList = filterDiagnostics(\n    config.errors,\n    diagnosticFilters\n  );\n  const outputCache = new Map<\n    string,\n    {\n      content: string;\n    }\n  >();\n\n  const configFileDirname = configFilePath ? dirname(configFilePath) : null;\n  const scopeDir =\n    options.scopeDir ?? config.options.rootDir ?? configFileDirname ?? cwd;\n  const ignoreBaseDir = configFileDirname ?? cwd;\n  const isScoped = options.scope\n    ? (fileName: string) => relative(scopeDir, fileName).charAt(0) !== '.'\n    : () => true;\n  const shouldIgnore = createIgnore(\n    ignoreBaseDir,\n    options.skipIgnore\n      ? []\n      : (options.ignore || ['(?:^|/)node_modules/']).map(\n          (str) => new RegExp(str)\n        )\n  );\n\n  const diagnosticHost: _ts.FormatDiagnosticsHost = {\n    getNewLine: () => ts.sys.newLine,\n    getCurrentDirectory: () => cwd,\n    // TODO switch to getCanonicalFileName we already create later in scope\n    getCanonicalFileName: ts.sys.useCaseSensitiveFileNames\n      ? (x) => x\n      : (x) => x.toLowerCase(),\n  };\n\n  if (options.transpileOnly && typeof transformers === 'function') {\n    throw new TypeError(\n      'Transformers function is unavailable in \"--transpile-only\"'\n    );\n  }\n  let createTranspiler = initializeTranspilerFactory();\n  function initializeTranspilerFactory() {\n    if (transpiler) {\n      if (!transpileOnly)\n        throw new Error(\n          'Custom transpiler can only be used when transpileOnly is enabled.'\n        );\n      const transpilerName =\n        typeof transpiler === 'string' ? transpiler : transpiler[0];\n      const transpilerOptions =\n        typeof transpiler === 'string' ? {} : transpiler[1] ?? {};\n      const transpilerConfigLocalResolveHelper = transpilerBasePath\n        ? createProjectLocalResolveHelper(transpilerBasePath)\n        : projectLocalResolveHelper;\n      const transpilerPath = transpilerConfigLocalResolveHelper(\n        transpilerName,\n        true\n      );\n      const transpilerFactory = require(transpilerPath)\n        .create as TranspilerFactory;\n      return createTranspiler;\n\n      function createTranspiler(\n        compilerOptions: TSCommon.CompilerOptions,\n        nodeModuleEmitKind?: NodeModuleEmitKind\n      ) {\n        return transpilerFactory?.({\n          service: {\n            options,\n            config: {\n              ...config,\n              options: compilerOptions,\n            },\n            projectLocalResolveHelper,\n          },\n          transpilerConfigLocalResolveHelper,\n          nodeModuleEmitKind,\n          ...transpilerOptions,\n        });\n      }\n    }\n  }\n\n  /**\n   * True if require() hooks should interop with experimental ESM loader.\n   * Enabled explicitly via a flag since it is a breaking change.\n   */\n  let experimentalEsmLoader = false;\n  function enableExperimentalEsmLoaderInterop() {\n    experimentalEsmLoader = true;\n  }\n\n  // Install source map support and read from memory cache.\n  installSourceMapSupport();\n  function installSourceMapSupport() {\n    const sourceMapSupport =\n      require('@cspotcode/source-map-support') as typeof _sourceMapSupport;\n    sourceMapSupport.install({\n      environment: 'node',\n      retrieveFile(pathOrUrl: string) {\n        let path = pathOrUrl;\n        // If it's a file URL, convert to local path\n        // Note: fileURLToPath does not exist on early node v10\n        // I could not find a way to handle non-URLs except to swallow an error\n        if (experimentalEsmLoader && path.startsWith('file://')) {\n          try {\n            path = fileURLToPath(path);\n          } catch (e) {\n            /* swallow error */\n          }\n        }\n        path = normalizeSlashes(path);\n        return outputCache.get(path)?.content || '';\n      },\n      redirectConflictingLibrary: true,\n      onConflictingLibraryRedirect(\n        request,\n        parent,\n        isMain,\n        options,\n        redirectedRequest\n      ) {\n        debug(\n          `Redirected an attempt to require source-map-support to instead receive @cspotcode/source-map-support.  \"${\n            (parent as NodeJS.Module).filename\n          }\" attempted to require or resolve \"${request}\" and was redirected to \"${redirectedRequest}\".`\n        );\n      },\n    });\n  }\n\n  const shouldHavePrettyErrors =\n    options.pretty === undefined ? process.stdout.isTTY : options.pretty;\n\n  const formatDiagnostics = shouldHavePrettyErrors\n    ? ts.formatDiagnosticsWithColorAndContext || ts.formatDiagnostics\n    : ts.formatDiagnostics;\n\n  function createTSError(diagnostics: ReadonlyArray<_ts.Diagnostic>) {\n    const diagnosticText = formatDiagnostics(diagnostics, diagnosticHost);\n    const diagnosticCodes = diagnostics.map((x) => x.code);\n    return new TSError(diagnosticText, diagnosticCodes, diagnostics);\n  }\n\n  function reportTSError(configDiagnosticList: _ts.Diagnostic[]) {\n    const error = createTSError(configDiagnosticList);\n    if (options.logError) {\n      // Print error in red color and continue execution.\n      console.error('\\x1b[31m%s\\x1b[0m', error);\n    } else {\n      // Throw error and exit the script.\n      throw error;\n    }\n  }\n\n  // Render the configuration errors.\n  if (configDiagnosticList.length) reportTSError(configDiagnosticList);\n\n  const jsxEmitPreserve = config.options.jsx === ts.JsxEmit.Preserve;\n  /**\n   * Get the extension for a transpiled file.\n   * [MUST_UPDATE_FOR_NEW_FILE_EXTENSIONS]\n   */\n  function getEmitExtension(path: string) {\n    const lastDotIndex = path.lastIndexOf('.');\n    if (lastDotIndex >= 0) {\n      const ext = path.slice(lastDotIndex);\n      switch (ext) {\n        case '.js':\n        case '.ts':\n          return '.js';\n        case '.jsx':\n        case '.tsx':\n          return jsxEmitPreserve ? '.jsx' : '.js';\n        case '.mjs':\n        case '.mts':\n          return '.mjs';\n        case '.cjs':\n        case '.cts':\n          return '.cjs';\n      }\n    }\n    return '.js';\n  }\n\n  type GetOutputFunction = (code: string, fileName: string) => SourceOutput;\n  /**\n   * Get output from TS compiler w/typechecking.  `undefined` in `transpileOnly`\n   * mode.\n   */\n  let getOutput: GetOutputFunction | undefined;\n  let getTypeInfo: (\n    _code: string,\n    _fileName: string,\n    _position: number\n  ) => TypeInfo;\n\n  const getCanonicalFileName = (\n    ts as unknown as TSInternal\n  ).createGetCanonicalFileName(ts.sys.useCaseSensitiveFileNames);\n\n  const moduleTypeClassifier = createModuleTypeClassifier({\n    basePath: options.optionBasePaths?.moduleTypes,\n    patterns: options.moduleTypes,\n  });\n\n  const extensions = getExtensions(config, options, ts.version);\n\n  // Use full language services when the fast option is disabled.\n  if (!transpileOnly) {\n    const fileContents = new Map<string, string>();\n    const rootFileNames = new Set(config.fileNames);\n    const cachedReadFile = cachedLookup(debugFn('readFile', readFile));\n\n    // Use language services by default\n    if (!options.compilerHost) {\n      let projectVersion = 1;\n      const fileVersions = new Map(\n        Array.from(rootFileNames).map((fileName) => [fileName, 0])\n      );\n\n      const getCustomTransformers = () => {\n        if (typeof transformers === 'function') {\n          const program = service.getProgram();\n          return program ? transformers(program) : undefined;\n        }\n\n        return transformers;\n      };\n\n      // Create the compiler host for type checking.\n      const serviceHost: _ts.LanguageServiceHost &\n        Required<Pick<_ts.LanguageServiceHost, 'fileExists' | 'readFile'>> = {\n        getProjectVersion: () => String(projectVersion),\n        getScriptFileNames: () => Array.from(rootFileNames),\n        getScriptVersion: (fileName: string) => {\n          const version = fileVersions.get(fileName);\n          return version ? version.toString() : '';\n        },\n        getScriptSnapshot(fileName: string) {\n          // TODO ordering of this with getScriptVersion?  Should they sync up?\n          let contents = fileContents.get(fileName);\n\n          // Read contents into TypeScript memory cache.\n          if (contents === undefined) {\n            contents = cachedReadFile(fileName);\n            if (contents === undefined) return;\n\n            fileVersions.set(fileName, 1);\n            fileContents.set(fileName, contents);\n            projectVersion++;\n          }\n\n          return ts.ScriptSnapshot.fromString(contents);\n        },\n        readFile: cachedReadFile,\n        readDirectory: ts.sys.readDirectory,\n        getDirectories: cachedLookup(\n          debugFn('getDirectories', ts.sys.getDirectories)\n        ),\n        fileExists: cachedLookup(debugFn('fileExists', fileExists)),\n        directoryExists: cachedLookup(\n          debugFn('directoryExists', ts.sys.directoryExists)\n        ),\n        realpath: ts.sys.realpath\n          ? cachedLookup(debugFn('realpath', ts.sys.realpath))\n          : undefined,\n        getNewLine: () => ts.sys.newLine,\n        useCaseSensitiveFileNames: () => ts.sys.useCaseSensitiveFileNames,\n        getCurrentDirectory: () => cwd,\n        getCompilationSettings: () => config.options,\n        getDefaultLibFileName: () => ts.getDefaultLibFilePath(config.options),\n        getCustomTransformers: getCustomTransformers,\n        trace: options.tsTrace,\n      };\n      const {\n        resolveModuleNames,\n        getResolvedModuleWithFailedLookupLocationsFromCache,\n        resolveTypeReferenceDirectives,\n        isFileKnownToBeInternal,\n        markBucketOfFilenameInternal,\n      } = createResolverFunctions({\n        host: serviceHost,\n        getCanonicalFileName,\n        ts,\n        cwd,\n        config,\n        projectLocalResolveHelper,\n        options,\n        extensions,\n      });\n      serviceHost.resolveModuleNames = resolveModuleNames;\n      serviceHost.getResolvedModuleWithFailedLookupLocationsFromCache =\n        getResolvedModuleWithFailedLookupLocationsFromCache;\n      serviceHost.resolveTypeReferenceDirectives =\n        resolveTypeReferenceDirectives;\n\n      const registry = ts.createDocumentRegistry(\n        ts.sys.useCaseSensitiveFileNames,\n        cwd\n      );\n      const service = ts.createLanguageService(serviceHost, registry);\n\n      const updateMemoryCache = (contents: string, fileName: string) => {\n        // Add to `rootFiles` as necessary, either to make TS include a file it has not seen,\n        // or to trigger a re-classification of files from external to internal.\n        if (\n          !rootFileNames.has(fileName) &&\n          !isFileKnownToBeInternal(fileName)\n        ) {\n          markBucketOfFilenameInternal(fileName);\n          rootFileNames.add(fileName);\n          // Increment project version for every change to rootFileNames.\n          projectVersion++;\n        }\n\n        const previousVersion = fileVersions.get(fileName) || 0;\n        const previousContents = fileContents.get(fileName);\n        // Avoid incrementing cache when nothing has changed.\n        if (contents !== previousContents) {\n          fileVersions.set(fileName, previousVersion + 1);\n          fileContents.set(fileName, contents);\n          // Increment project version for every file change.\n          projectVersion++;\n        }\n      };\n\n      let previousProgram: _ts.Program | undefined = undefined;\n\n      getOutput = (code: string, fileName: string) => {\n        updateMemoryCache(code, fileName);\n\n        const programBefore = service.getProgram();\n        if (programBefore !== previousProgram) {\n          debug(\n            `compiler rebuilt Program instance when getting output for ${fileName}`\n          );\n        }\n\n        const output = service.getEmitOutput(fileName);\n\n        // Get the relevant diagnostics - this is 3x faster than `getPreEmitDiagnostics`.\n        const diagnostics = service\n          .getSemanticDiagnostics(fileName)\n          .concat(service.getSyntacticDiagnostics(fileName));\n\n        const programAfter = service.getProgram();\n\n        debug(\n          'invariant: Is service.getProject() identical before and after getting emit output and diagnostics? (should always be true) ',\n          programBefore === programAfter\n        );\n\n        previousProgram = programAfter;\n\n        const diagnosticList = filterDiagnostics(\n          diagnostics,\n          diagnosticFilters\n        );\n        if (diagnosticList.length) reportTSError(diagnosticList);\n\n        if (output.emitSkipped) {\n          return [undefined, undefined, true];\n        }\n\n        // Throw an error when requiring `.d.ts` files.\n        if (output.outputFiles.length === 0) {\n          throw new TypeError(\n            `Unable to require file: ${relative(cwd, fileName)}\\n` +\n              'This is usually the result of a faulty configuration or import. ' +\n              'Make sure there is a `.js`, `.json` or other executable extension with ' +\n              'loader attached before `ts-node` available.'\n          );\n        }\n\n        return [output.outputFiles[1].text, output.outputFiles[0].text, false];\n      };\n\n      getTypeInfo = (code: string, fileName: string, position: number) => {\n        const normalizedFileName = normalizeSlashes(fileName);\n        updateMemoryCache(code, normalizedFileName);\n\n        const info = service.getQuickInfoAtPosition(\n          normalizedFileName,\n          position\n        );\n        const name = ts.displayPartsToString(info ? info.displayParts : []);\n        const comment = ts.displayPartsToString(info ? info.documentation : []);\n\n        return { name, comment };\n      };\n    } else {\n      const sys: _ts.System & _ts.FormatDiagnosticsHost = {\n        ...ts.sys,\n        ...diagnosticHost,\n        readFile: (fileName: string) => {\n          const cacheContents = fileContents.get(fileName);\n          if (cacheContents !== undefined) return cacheContents;\n          const contents = cachedReadFile(fileName);\n          if (contents) fileContents.set(fileName, contents);\n          return contents;\n        },\n        readDirectory: ts.sys.readDirectory,\n        getDirectories: cachedLookup(\n          debugFn('getDirectories', ts.sys.getDirectories)\n        ),\n        fileExists: cachedLookup(debugFn('fileExists', fileExists)),\n        directoryExists: cachedLookup(\n          debugFn('directoryExists', ts.sys.directoryExists)\n        ),\n        resolvePath: cachedLookup(debugFn('resolvePath', ts.sys.resolvePath)),\n        realpath: ts.sys.realpath\n          ? cachedLookup(debugFn('realpath', ts.sys.realpath))\n          : undefined,\n      };\n\n      const host: _ts.CompilerHost = ts.createIncrementalCompilerHost\n        ? ts.createIncrementalCompilerHost(config.options, sys)\n        : {\n            ...sys,\n            getSourceFile: (fileName, languageVersion) => {\n              const contents = sys.readFile(fileName);\n              if (contents === undefined) return;\n              return ts.createSourceFile(fileName, contents, languageVersion);\n            },\n            getDefaultLibLocation: () => normalizeSlashes(dirname(compiler)),\n            getDefaultLibFileName: () =>\n              normalizeSlashes(\n                join(\n                  dirname(compiler),\n                  ts.getDefaultLibFileName(config.options)\n                )\n              ),\n            useCaseSensitiveFileNames: () => sys.useCaseSensitiveFileNames,\n          };\n      host.trace = options.tsTrace;\n      const {\n        resolveModuleNames,\n        resolveTypeReferenceDirectives,\n        isFileKnownToBeInternal,\n        markBucketOfFilenameInternal,\n      } = createResolverFunctions({\n        host,\n        cwd,\n        config,\n        ts,\n        getCanonicalFileName,\n        projectLocalResolveHelper,\n        options,\n        extensions,\n      });\n      host.resolveModuleNames = resolveModuleNames;\n      host.resolveTypeReferenceDirectives = resolveTypeReferenceDirectives;\n\n      // Fallback for older TypeScript releases without incremental API.\n      let builderProgram = ts.createIncrementalProgram\n        ? ts.createIncrementalProgram({\n            rootNames: Array.from(rootFileNames),\n            options: config.options,\n            host,\n            configFileParsingDiagnostics: config.errors,\n            projectReferences: config.projectReferences,\n          })\n        : ts.createEmitAndSemanticDiagnosticsBuilderProgram(\n            Array.from(rootFileNames),\n            config.options,\n            host,\n            undefined,\n            config.errors,\n            config.projectReferences\n          );\n\n      // Read and cache custom transformers.\n      const customTransformers =\n        typeof transformers === 'function'\n          ? transformers(builderProgram.getProgram())\n          : transformers;\n\n      // Set the file contents into cache manually.\n      const updateMemoryCache = (contents: string, fileName: string) => {\n        const previousContents = fileContents.get(fileName);\n        const contentsChanged = previousContents !== contents;\n        if (contentsChanged) {\n          fileContents.set(fileName, contents);\n        }\n\n        // Add to `rootFiles` when discovered by compiler for the first time.\n        let addedToRootFileNames = false;\n        if (\n          !rootFileNames.has(fileName) &&\n          !isFileKnownToBeInternal(fileName)\n        ) {\n          markBucketOfFilenameInternal(fileName);\n          rootFileNames.add(fileName);\n          addedToRootFileNames = true;\n        }\n\n        // Update program when file changes.\n        if (addedToRootFileNames || contentsChanged) {\n          builderProgram = ts.createEmitAndSemanticDiagnosticsBuilderProgram(\n            Array.from(rootFileNames),\n            config.options,\n            host,\n            builderProgram,\n            config.errors,\n            config.projectReferences\n          );\n        }\n      };\n\n      getOutput = (code: string, fileName: string) => {\n        let outText = '';\n        let outMap = '';\n\n        updateMemoryCache(code, fileName);\n\n        const sourceFile = builderProgram.getSourceFile(fileName);\n        if (!sourceFile)\n          throw new TypeError(`Unable to read file: ${fileName}`);\n\n        const program = builderProgram.getProgram();\n        const diagnostics = ts.getPreEmitDiagnostics(program, sourceFile);\n        const diagnosticList = filterDiagnostics(\n          diagnostics,\n          diagnosticFilters\n        );\n        if (diagnosticList.length) reportTSError(diagnosticList);\n\n        const result = builderProgram.emit(\n          sourceFile,\n          (path, file, writeByteOrderMark) => {\n            if (path.endsWith('.map')) {\n              outMap = file;\n            } else {\n              outText = file;\n            }\n\n            if (options.emit) sys.writeFile(path, file, writeByteOrderMark);\n          },\n          undefined,\n          undefined,\n          customTransformers\n        );\n\n        if (result.emitSkipped) {\n          return [undefined, undefined, true];\n        }\n\n        // Throw an error when requiring files that cannot be compiled.\n        if (outText === '') {\n          if (program.isSourceFileFromExternalLibrary(sourceFile)) {\n            throw new TypeError(\n              `Unable to compile file from external library: ${relative(\n                cwd,\n                fileName\n              )}`\n            );\n          }\n\n          throw new TypeError(\n            `Unable to require file: ${relative(cwd, fileName)}\\n` +\n              'This is usually the result of a faulty configuration or import. ' +\n              'Make sure there is a `.js`, `.json` or other executable extension with ' +\n              'loader attached before `ts-node` available.'\n          );\n        }\n\n        return [outText, outMap, false];\n      };\n\n      getTypeInfo = (code: string, fileName: string, position: number) => {\n        const normalizedFileName = normalizeSlashes(fileName);\n        updateMemoryCache(code, normalizedFileName);\n\n        const sourceFile = builderProgram.getSourceFile(normalizedFileName);\n        if (!sourceFile)\n          throw new TypeError(`Unable to read file: ${fileName}`);\n\n        const node = getTokenAtPosition(ts, sourceFile, position);\n        const checker = builderProgram.getProgram().getTypeChecker();\n        const symbol = checker.getSymbolAtLocation(node);\n\n        if (!symbol) return { name: '', comment: '' };\n\n        const type = checker.getTypeOfSymbolAtLocation(symbol, node);\n        const signatures = [\n          ...type.getConstructSignatures(),\n          ...type.getCallSignatures(),\n        ];\n\n        return {\n          name: signatures.length\n            ? signatures.map((x) => checker.signatureToString(x)).join('\\n')\n            : checker.typeToString(type),\n          comment: ts.displayPartsToString(\n            symbol ? symbol.getDocumentationComment(checker) : []\n          ),\n        };\n      };\n\n      // Write `.tsbuildinfo` when `--build` is enabled.\n      if (options.emit && config.options.incremental) {\n        process.on('exit', () => {\n          // Emits `.tsbuildinfo` to filesystem.\n          (builderProgram.getProgram() as any).emitBuildInfo();\n        });\n      }\n    }\n  } else {\n    getTypeInfo = () => {\n      throw new TypeError(\n        'Type information is unavailable in \"--transpile-only\"'\n      );\n    };\n  }\n\n  function createTranspileOnlyGetOutputFunction(\n    overrideModuleType?: _ts.ModuleKind,\n    nodeModuleEmitKind?: NodeModuleEmitKind\n  ): GetOutputFunction {\n    const compilerOptions = { ...config.options };\n    if (overrideModuleType !== undefined)\n      compilerOptions.module = overrideModuleType;\n    let customTranspiler = createTranspiler?.(\n      compilerOptions,\n      nodeModuleEmitKind\n    );\n    let tsTranspileModule = versionGteLt(ts.version, '4.7.0')\n      ? createTsTranspileModule(ts, {\n          compilerOptions,\n          reportDiagnostics: true,\n          transformers: transformers as _ts.CustomTransformers | undefined,\n        })\n      : undefined;\n    return (code: string, fileName: string): SourceOutput => {\n      let result: _ts.TranspileOutput;\n      if (customTranspiler) {\n        result = customTranspiler.transpile(code, {\n          fileName,\n        });\n      } else if (tsTranspileModule) {\n        result = tsTranspileModule(\n          code,\n          {\n            fileName,\n          },\n          nodeModuleEmitKind === 'nodeesm' ? 'module' : 'commonjs'\n        );\n      } else {\n        result = ts.transpileModule(code, {\n          fileName,\n          compilerOptions,\n          reportDiagnostics: true,\n          transformers: transformers as _ts.CustomTransformers | undefined,\n        });\n      }\n\n      const diagnosticList = filterDiagnostics(\n        result.diagnostics || [],\n        diagnosticFilters\n      );\n      if (diagnosticList.length) reportTSError(diagnosticList);\n\n      return [result.outputText, result.sourceMapText as string, false];\n    };\n  }\n\n  // When true, these mean that a `moduleType` override will cause a different emit\n  // than the TypeScript compiler, so we *must* overwrite the emit.\n  const shouldOverwriteEmitWhenForcingCommonJS =\n    config.options.module !== ts.ModuleKind.CommonJS;\n  // [MUST_UPDATE_FOR_NEW_MODULEKIND]\n  const shouldOverwriteEmitWhenForcingEsm = !(\n    config.options.module === ts.ModuleKind.ES2015 ||\n    (ts.ModuleKind.ES2020 && config.options.module === ts.ModuleKind.ES2020) ||\n    (ts.ModuleKind.ES2022 && config.options.module === ts.ModuleKind.ES2022) ||\n    config.options.module === ts.ModuleKind.ESNext\n  );\n  /**\n   * node16 or nodenext\n   * [MUST_UPDATE_FOR_NEW_MODULEKIND]\n   */\n  const isNodeModuleType =\n    (ts.ModuleKind.Node16 && config.options.module === ts.ModuleKind.Node16) ||\n    (ts.ModuleKind.NodeNext &&\n      config.options.module === ts.ModuleKind.NodeNext);\n  const getOutputForceCommonJS = createTranspileOnlyGetOutputFunction(\n    ts.ModuleKind.CommonJS\n  );\n  const getOutputForceNodeCommonJS = createTranspileOnlyGetOutputFunction(\n    ts.ModuleKind.NodeNext,\n    'nodecjs'\n  );\n  const getOutputForceNodeESM = createTranspileOnlyGetOutputFunction(\n    ts.ModuleKind.NodeNext,\n    'nodeesm'\n  );\n  // [MUST_UPDATE_FOR_NEW_MODULEKIND]\n  const getOutputForceESM = createTranspileOnlyGetOutputFunction(\n    ts.ModuleKind.ES2022 || ts.ModuleKind.ES2020 || ts.ModuleKind.ES2015\n  );\n  const getOutputTranspileOnly = createTranspileOnlyGetOutputFunction();\n\n  // Create a simple TypeScript compiler proxy.\n  function compile(code: string, fileName: string, lineOffset = 0) {\n    const normalizedFileName = normalizeSlashes(fileName);\n    const classification =\n      moduleTypeClassifier.classifyModuleByModuleTypeOverrides(\n        normalizedFileName\n      );\n    let value: string | undefined = '';\n    let sourceMap: string | undefined = '';\n    let emitSkipped = true;\n    if (getOutput) {\n      // Must always call normal getOutput to throw typechecking errors\n      [value, sourceMap, emitSkipped] = getOutput(code, normalizedFileName);\n    }\n    // If module classification contradicts the above, call the relevant transpiler\n    if (\n      classification.moduleType === 'cjs' &&\n      (shouldOverwriteEmitWhenForcingCommonJS || emitSkipped)\n    ) {\n      [value, sourceMap] = getOutputForceCommonJS(code, normalizedFileName);\n    } else if (\n      classification.moduleType === 'esm' &&\n      (shouldOverwriteEmitWhenForcingEsm || emitSkipped)\n    ) {\n      [value, sourceMap] = getOutputForceESM(code, normalizedFileName);\n    } else if (emitSkipped) {\n      // Happens when ts compiler skips emit or in transpileOnly mode\n      const classification = classifyModule(fileName, isNodeModuleType);\n      [value, sourceMap] =\n        classification === 'nodecjs'\n          ? getOutputForceNodeCommonJS(code, normalizedFileName)\n          : classification === 'nodeesm'\n          ? getOutputForceNodeESM(code, normalizedFileName)\n          : classification === 'cjs'\n          ? getOutputForceCommonJS(code, normalizedFileName)\n          : classification === 'esm'\n          ? getOutputForceESM(code, normalizedFileName)\n          : getOutputTranspileOnly(code, normalizedFileName);\n    }\n    const output = updateOutput(\n      value!,\n      normalizedFileName,\n      sourceMap!,\n      getEmitExtension\n    );\n    outputCache.set(normalizedFileName, { content: output });\n    return output;\n  }\n\n  let active = true;\n  const enabled = (enabled?: boolean) =>\n    enabled === undefined ? active : (active = !!enabled);\n  const ignored = (fileName: string) => {\n    if (!active) return true;\n    const ext = extname(fileName);\n    if (extensions.compiled.includes(ext)) {\n      return !isScoped(fileName) || shouldIgnore(fileName);\n    }\n    return true;\n  };\n\n  function addDiagnosticFilter(filter: DiagnosticFilter) {\n    diagnosticFilters.push({\n      ...filter,\n      filenamesAbsolute: filter.filenamesAbsolute.map((f) =>\n        normalizeSlashes(f)\n      ),\n    });\n  }\n\n  const getNodeEsmResolver = once(() =>\n    (\n      require('../dist-raw/node-internal-modules-esm-resolve') as typeof _nodeInternalModulesEsmResolve\n    ).createResolve({\n      extensions,\n      preferTsExts: options.preferTsExts,\n      tsNodeExperimentalSpecifierResolution:\n        options.experimentalSpecifierResolution,\n    })\n  );\n  const getNodeEsmGetFormat = once(() =>\n    (\n      require('../dist-raw/node-internal-modules-esm-get_format') as typeof _nodeInternalModulesEsmGetFormat\n    ).createGetFormat(\n      options.experimentalSpecifierResolution,\n      getNodeEsmResolver()\n    )\n  );\n  const getNodeCjsLoader = once(() =>\n    (\n      require('../dist-raw/node-internal-modules-cjs-loader') as typeof _nodeInternalModulesCjsLoader\n    ).createCjsLoader({\n      extensions,\n      preferTsExts: options.preferTsExts,\n      nodeEsmResolver: getNodeEsmResolver(),\n    })\n  );\n\n  return {\n    [TS_NODE_SERVICE_BRAND]: true,\n    ts,\n    compilerPath: compiler,\n    config,\n    compile,\n    getTypeInfo,\n    ignored,\n    enabled,\n    options,\n    configFilePath,\n    moduleTypeClassifier,\n    shouldReplAwait,\n    addDiagnosticFilter,\n    installSourceMapSupport,\n    enableExperimentalEsmLoaderInterop,\n    transpileOnly,\n    projectLocalResolveHelper,\n    getNodeEsmResolver,\n    getNodeEsmGetFormat,\n    getNodeCjsLoader,\n    extensions,\n  };\n}\n\n/**\n * Check if the filename should be ignored.\n */\nfunction createIgnore(ignoreBaseDir: string, ignore: RegExp[]) {\n  return (fileName: string) => {\n    const relname = relative(ignoreBaseDir, fileName);\n    const path = normalizeSlashes(relname);\n\n    return ignore.some((x) => x.test(path));\n  };\n}\n\n/**\n * Register the extensions to support when importing files.\n */\nfunction registerExtensions(\n  preferTsExts: boolean | null | undefined,\n  extensions: string[],\n  service: Service,\n  originalJsHandler: (m: NodeModule, filename: string) => any\n) {\n  const exts = new Set(extensions);\n  // Can't add these extensions cuz would allow omitting file extension; node requires ext for .cjs and .mjs\n  // Unless they're already registered by something else (nyc does this):\n  // then we *must* hook them or else our transformer will not be called.\n  for (const cannotAdd of ['.mts', '.cts', '.mjs', '.cjs']) {\n    if (exts.has(cannotAdd) && !hasOwnProperty(require.extensions, cannotAdd)) {\n      // Unrecognized file exts can be transformed via the `.js` handler.\n      exts.add('.js');\n      exts.delete(cannotAdd);\n    }\n  }\n\n  // Register new extensions.\n  for (const ext of exts) {\n    registerExtension(ext, service, originalJsHandler);\n  }\n\n  if (preferTsExts) {\n    const preferredExtensions = new Set([\n      ...exts,\n      ...Object.keys(require.extensions),\n    ]);\n\n    // Re-sort iteration order of Object.keys()\n    for (const ext of preferredExtensions) {\n      const old = Object.getOwnPropertyDescriptor(require.extensions, ext);\n      delete require.extensions[ext];\n      Object.defineProperty(require.extensions, ext, old!);\n    }\n  }\n}\n\n/**\n * Register the extension for node.\n */\nfunction registerExtension(\n  ext: string,\n  service: Service,\n  originalHandler: (m: NodeModule, filename: string) => any\n) {\n  const old = require.extensions[ext] || originalHandler;\n\n  require.extensions[ext] = function (m: any, filename) {\n    if (service.ignored(filename)) return old(m, filename);\n\n    assertScriptCanLoadAsCJS(service, m, filename);\n\n    const _compile = m._compile;\n\n    m._compile = function (code: string, fileName: string) {\n      debug('module._compile', fileName);\n\n      const result = service.compile(code, fileName);\n      return _compile.call(this, result, fileName);\n    };\n\n    return old(m, filename);\n  };\n}\n\n/**\n * Internal source output.\n */\ntype SourceOutput = [string, string, false] | [undefined, undefined, true];\n\n/**\n * Update the output remapping the source map.\n */\nfunction updateOutput(\n  outputText: string,\n  fileName: string,\n  sourceMap: string,\n  getEmitExtension: (fileName: string) => string\n) {\n  const base64Map = Buffer.from(\n    updateSourceMap(sourceMap, fileName),\n    'utf8'\n  ).toString('base64');\n  const sourceMapContent = `//# sourceMappingURL=data:application/json;charset=utf-8;base64,${base64Map}`;\n  // Expected form: `//# sourceMappingURL=foo bar.js.map` or `//# sourceMappingURL=foo%20bar.js.map` for input file \"foo bar.tsx\"\n  // Percent-encoding behavior added in TS 4.1.1: https://github.com/microsoft/TypeScript/issues/40951\n  const prefix = '//# sourceMappingURL=';\n  const prefixLength = prefix.length;\n  const baseName = /*foo.tsx*/ basename(fileName);\n  const extName = /*.tsx*/ extname(fileName);\n  const extension = /*.js*/ getEmitExtension(fileName);\n  const sourcemapFilename =\n    baseName.slice(0, -extName.length) + extension + '.map';\n  const sourceMapLengthWithoutPercentEncoding =\n    prefixLength + sourcemapFilename.length;\n  /*\n   * Only rewrite if existing directive exists at the location we expect, to support:\n   *   a) compilers that do not append a sourcemap directive\n   *   b) situations where we did the math wrong\n   *     Not ideal, but appending our sourcemap *after* a pre-existing sourcemap still overrides, so the end-user is happy.\n   */\n  if (\n    outputText.substr(-sourceMapLengthWithoutPercentEncoding, prefixLength) ===\n    prefix\n  ) {\n    return (\n      outputText.slice(0, -sourceMapLengthWithoutPercentEncoding) +\n      sourceMapContent\n    );\n  }\n  // If anyone asks why we're not using URL, the URL equivalent is: `u = new URL('http://d'); u.pathname = \"/\" + sourcemapFilename; return u.pathname.slice(1);\n  const sourceMapLengthWithPercentEncoding =\n    prefixLength + encodeURI(sourcemapFilename).length;\n  if (\n    outputText.substr(-sourceMapLengthWithPercentEncoding, prefixLength) ===\n    prefix\n  ) {\n    return (\n      outputText.slice(0, -sourceMapLengthWithPercentEncoding) +\n      sourceMapContent\n    );\n  }\n\n  return `${outputText}\\n${sourceMapContent}`;\n}\n\n/**\n * Update the source map contents for improved output.\n */\nfunction updateSourceMap(sourceMapText: string, fileName: string) {\n  const sourceMap = JSON.parse(sourceMapText);\n  sourceMap.file = fileName;\n  sourceMap.sources = [fileName];\n  delete sourceMap.sourceRoot;\n  return JSON.stringify(sourceMap);\n}\n\n/**\n * Filter diagnostics.\n */\nfunction filterDiagnostics(\n  diagnostics: readonly _ts.Diagnostic[],\n  filters: DiagnosticFilter[]\n) {\n  return diagnostics.filter((d) =>\n    filters.every(\n      (f) =>\n        (!f.appliesToAllFiles &&\n          f.filenamesAbsolute.indexOf(d.file?.fileName!) === -1) ||\n        f.diagnosticsIgnored.indexOf(d.code) === -1\n    )\n  );\n}\n\n/**\n * Get token at file position.\n *\n * Reference: https://github.com/microsoft/TypeScript/blob/fcd9334f57d85b73dd66ad2d21c02e84822f4841/src/services/utilities.ts#L705-L731\n */\nfunction getTokenAtPosition(\n  ts: TSCommon,\n  sourceFile: _ts.SourceFile,\n  position: number\n): _ts.Node {\n  let current: _ts.Node = sourceFile;\n\n  outer: while (true) {\n    for (const child of current.getChildren(sourceFile)) {\n      const start = child.getFullStart();\n      if (start > position) break;\n\n      const end = child.getEnd();\n      if (position <= end) {\n        current = child;\n        continue outer;\n      }\n    }\n\n    return current;\n  }\n}\n\n/**\n * Create an implementation of node's ESM loader hooks.\n *\n * This may be useful if you\n * want to wrap or compose the loader hooks to add additional functionality or\n * combine with another loader.\n *\n * Node changed the hooks API, so there are two possible APIs.  This function\n * detects your node version and returns the appropriate API.\n *\n * @category ESM Loader\n */\nexport const createEsmHooks: typeof createEsmHooksFn = (\n  tsNodeService: Service\n) => (require('./esm') as typeof import('./esm')).createEsmHooks(tsNodeService);\n\n/**\n * When using `module: nodenext` or `module: node12`, there are two possible styles of emit depending in file extension or package.json \"type\":\n *\n * - CommonJS with dynamic imports preserved (not transformed into `require()` calls)\n * - ECMAScript modules with `import foo = require()` transformed into `require = createRequire(); const foo = require()`\n */\nexport type NodeModuleEmitKind = 'nodeesm' | 'nodecjs';\n"]}