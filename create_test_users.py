#!/usr/bin/env python3
"""
Script to create test users in the database for authentication testing.
"""
import os
import sys
from pathlib import Path

# Add model-service src to path
model_service_path = Path(__file__).parent / "model-service" / "src"
sys.path.insert(0, str(model_service_path))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models import User, UserRole, Base
from app.routers import get_password_hash

# Database configuration
DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "sqlite:///./fraud_detection.db"  # Temporary SQLite for testing
)

def create_test_users():
    """Create test users in the database"""
    print("Creating test users...")

    # Create engine and session
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    # Create tables if they don't exist
    Base.metadata.create_all(bind=engine)

    db = SessionLocal()

    try:
        # Check if users already exist
        existing_analyst = db.query(User).filter(User.username == "analyst").first()
        existing_admin = db.query(User).filter(User.username == "admin").first()

        if not existing_analyst:
            # Create analyst user
            analyst_user = User(
                username="analyst",
                password_hash=get_password_hash("password"),
                role=UserRole.ANALYST
            )
            db.add(analyst_user)
            print("✅ Created analyst user (username: analyst, password: password)")
        else:
            print("ℹ️  Analyst user already exists")

        if not existing_admin:
            # Create admin user
            admin_user = User(
                username="admin",
                password_hash=get_password_hash("password"),
                role=UserRole.ADMIN
            )
            db.add(admin_user)
            print("✅ Created admin user (username: admin, password: password)")
        else:
            print("ℹ️  Admin user already exists")

        # Commit changes
        db.commit()
        print("✅ Test users created successfully!")

    except Exception as e:
        print(f"❌ Error creating test users: {e}")
        db.rollback()
        return False
    finally:
        db.close()

    return True

if __name__ == "__main__":
    success = create_test_users()
    if success:
        print("\n🎉 Database setup complete!")
        print("You can now test authentication with:")
        print("  - Username: analyst, Password: password")
        print("  - Username: admin, Password: password")
    else:
        print("\n❌ Database setup failed!")
        sys.exit(1)
