{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\services\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { loginUser } from './api';\n\n// Create auth context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(null);\n\n// Auth provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing auth on mount\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const token = localStorage.getItem('token');\n    if (storedUser && token) {\n      setUser(JSON.parse(storedUser));\n    }\n    setLoading(false);\n  }, []);\n\n  // Login function\n  const login = async (username, password) => {\n    try {\n      const response = await loginUser(username, password);\n      const {\n        access_token,\n        token_type\n      } = response;\n\n      // For demo purposes, create a mock user object\n      // In production, you would decode the JWT or make a separate request\n      const mockUser = {\n        username,\n        role: username.includes('admin') ? 'admin' : 'analyst'\n      };\n\n      // Store auth data\n      localStorage.setItem('token', access_token);\n      localStorage.setItem('user', JSON.stringify(mockUser));\n      setUser(mockUser);\n      return true;\n    } catch (error) {\n      console.error('Login failed:', error);\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n  };\n\n  // Context value\n  const value = {\n    user,\n    login,\n    logout,\n    isAuthenticated: !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: !loading && children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook for using auth context\n_s(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "loginUser", "jsxDEV", "_jsxDEV", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "loading", "setLoading", "storedUser", "localStorage", "getItem", "token", "JSON", "parse", "login", "username", "password", "response", "access_token", "token_type", "mockUser", "role", "includes", "setItem", "stringify", "error", "console", "logout", "removeItem", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/services/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { loginUser } from './api';\n\n// Create auth context\nconst AuthContext = createContext(null);\n\n// Auth provider component\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing auth on mount\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const token = localStorage.getItem('token');\n    \n    if (storedUser && token) {\n      setUser(JSON.parse(storedUser));\n    }\n    \n    setLoading(false);\n  }, []);\n\n  // Login function\n  const login = async (username, password) => {\n    try {\n      const response = await loginUser(username, password);\n      const { access_token, token_type } = response;\n      \n      // For demo purposes, create a mock user object\n      // In production, you would decode the JWT or make a separate request\n      const mockUser = {\n        username,\n        role: username.includes('admin') ? 'admin' : 'analyst'\n      };\n      \n      // Store auth data\n      localStorage.setItem('token', access_token);\n      localStorage.setItem('user', JSON.stringify(mockUser));\n      \n      setUser(mockUser);\n      return true;\n    } catch (error) {\n      console.error('Login failed:', error);\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n  };\n\n  // Context value\n  const value = {\n    user,\n    login,\n    logout,\n    isAuthenticated: !!user\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {!loading && children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook for using auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,SAAS,QAAQ,OAAO;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,gBAAGP,aAAa,CAAC,IAAI,CAAC;;AAEvC;AACA,OAAO,MAAMQ,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMY,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,MAAMC,KAAK,GAAGF,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAIF,UAAU,IAAIG,KAAK,EAAE;MACvBN,OAAO,CAACO,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,CAAC;IACjC;IAEAD,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,KAAK,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMpB,SAAS,CAACkB,QAAQ,EAAEC,QAAQ,CAAC;MACpD,MAAM;QAAEE,YAAY;QAAEC;MAAW,CAAC,GAAGF,QAAQ;;MAE7C;MACA;MACA,MAAMG,QAAQ,GAAG;QACfL,QAAQ;QACRM,IAAI,EAAEN,QAAQ,CAACO,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG;MAC/C,CAAC;;MAED;MACAb,YAAY,CAACc,OAAO,CAAC,OAAO,EAAEL,YAAY,CAAC;MAC3CT,YAAY,CAACc,OAAO,CAAC,MAAM,EAAEX,IAAI,CAACY,SAAS,CAACJ,QAAQ,CAAC,CAAC;MAEtDf,OAAO,CAACe,QAAQ,CAAC;MACjB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAME,MAAM,GAAGA,CAAA,KAAM;IACnBlB,YAAY,CAACmB,UAAU,CAAC,OAAO,CAAC;IAChCnB,YAAY,CAACmB,UAAU,CAAC,MAAM,CAAC;IAC/BvB,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;;EAED;EACA,MAAMwB,KAAK,GAAG;IACZzB,IAAI;IACJU,KAAK;IACLa,MAAM;IACNG,eAAe,EAAE,CAAC,CAAC1B;EACrB,CAAC;EAED,oBACEL,OAAA,CAACC,WAAW,CAAC+B,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAA3B,QAAA,EAChC,CAACI,OAAO,IAAIJ;EAAQ;IAAA8B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAE3B,CAAC;;AAED;AAAAhC,EAAA,CA/DaF,YAAY;AAAAmC,EAAA,GAAZnC,YAAY;AAgEzB,OAAO,MAAMoC,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAG7C,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACuC,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}