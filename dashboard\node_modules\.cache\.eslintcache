[{"C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\services\\WebSocketContext.tsx": "3", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\services\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\pages\\Dashboard.tsx": "5", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\pages\\Login.tsx": "6", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\ProtectedRoute.tsx": "7", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\pages\\CaseManagement.tsx": "8", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\pages\\Analytics.tsx": "9", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\Navbar.tsx": "10", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\services\\api.ts": "11", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\TransactionTable.tsx": "12", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\CaseTable.tsx": "13", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\RiskHeatmap.tsx": "14", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\CaseForm.tsx": "15", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\CaseDrawer.tsx": "16", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\ErrorBoundary.tsx": "17", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\pages\\Unauthorized.tsx": "18", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\types\\index.ts": "19", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\Loading.tsx": "20", "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\utils\\validation.ts": "21"}, {"size": 388, "mtime": 1748175305134, "results": "22", "hashOfConfig": "23"}, {"size": 2493, "mtime": 1748176248768, "results": "24", "hashOfConfig": "23"}, {"size": 4250, "mtime": 1748175774337, "results": "25", "hashOfConfig": "23"}, {"size": 2509, "mtime": 1748175725292, "results": "26", "hashOfConfig": "23"}, {"size": 4611, "mtime": 1748175876135, "results": "27", "hashOfConfig": "23"}, {"size": 4682, "mtime": 1748176130273, "results": "28", "hashOfConfig": "23"}, {"size": 1332, "mtime": 1748176156921, "results": "29", "hashOfConfig": "23"}, {"size": 3408, "mtime": 1748071406064, "results": "30", "hashOfConfig": "23"}, {"size": 4955, "mtime": 1748071406059, "results": "31", "hashOfConfig": "23"}, {"size": 1440, "mtime": 1748071405968, "results": "32", "hashOfConfig": "23"}, {"size": 6277, "mtime": 1748175705128, "results": "33", "hashOfConfig": "23"}, {"size": 5979, "mtime": 1748175821708, "results": "34", "hashOfConfig": "23"}, {"size": 4089, "mtime": 1748071406001, "results": "35", "hashOfConfig": "23"}, {"size": 5475, "mtime": 1748071405985, "results": "36", "hashOfConfig": "23"}, {"size": 2952, "mtime": 1748071406008, "results": "37", "hashOfConfig": "23"}, {"size": 7505, "mtime": 1748071405982, "results": "38", "hashOfConfig": "23"}, {"size": 7532, "mtime": 1748176029449, "results": "39", "hashOfConfig": "23"}, {"size": 4235, "mtime": 1748176186607, "results": "40", "hashOfConfig": "23"}, {"size": 5365, "mtime": 1748175674568, "results": "41", "hashOfConfig": "23"}, {"size": 7484, "mtime": 1748176062711, "results": "42", "hashOfConfig": "23"}, {"size": 7760, "mtime": 1748175997575, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ug76rv", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\services\\WebSocketContext.tsx", ["107"], ["108"], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\services\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\pages\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\pages\\CaseManagement.tsx", ["109"], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\pages\\Analytics.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\services\\api.ts", ["110", "111"], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\TransactionTable.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\CaseTable.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\RiskHeatmap.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\CaseForm.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\CaseDrawer.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\ErrorBoundary.tsx", ["112"], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\pages\\Unauthorized.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\components\\Loading.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\fraud-platform\\dashboard\\src\\utils\\validation.ts", ["113", "114", "115"], [], {"ruleId": "116", "severity": 1, "message": "117", "line": 46, "column": 17, "nodeType": "118", "messageId": "119", "endLine": 46, "endColumn": 21}, {"ruleId": "120", "severity": 1, "message": "121", "line": 116, "column": 6, "nodeType": "122", "endLine": 116, "endColumn": 8, "suggestions": "123", "suppressions": "124"}, {"ruleId": "120", "severity": 1, "message": "125", "line": 16, "column": 6, "nodeType": "122", "endLine": 16, "endColumn": 14, "suggestions": "126"}, {"ruleId": "127", "severity": 2, "message": "128", "line": 82, "column": 1, "nodeType": "129", "endLine": 95, "endColumn": 19, "fix": "130"}, {"ruleId": "116", "severity": 1, "message": "131", "line": 94, "column": 3, "nodeType": "118", "messageId": "119", "endLine": 94, "endColumn": 20}, {"ruleId": "127", "severity": 2, "message": "128", "line": 104, "column": 1, "nodeType": "129", "endLine": 104, "endColumn": 45, "fix": "132"}, {"ruleId": "133", "severity": 1, "message": "134", "line": 96, "column": 22, "nodeType": "135", "messageId": "136", "endLine": 96, "endColumn": 23, "suggestions": "137"}, {"ruleId": "133", "severity": 1, "message": "138", "line": 96, "column": 24, "nodeType": "135", "messageId": "136", "endLine": 96, "endColumn": 25, "suggestions": "139"}, {"ruleId": "127", "severity": 2, "message": "128", "line": 296, "column": 1, "nodeType": "129", "endLine": 296, "endColumn": 47, "fix": "140"}, "@typescript-eslint/no-unused-vars", "'data' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'connectWebSocket' and 'socket'. Either include them or remove the dependency array.", "ArrayExpression", ["141"], ["142"], "React Hook useEffect has a missing dependency: 'loadCases'. Either include it or remove the dependency array.", ["143"], "import/first", "Import in body of module; reorder to top.", "ImportDeclaration", {"range": "144", "text": "145"}, "'PaginatedResponse' is defined but never used.", {"range": "146", "text": "147"}, "no-useless-escape", "Unnecessary escape character: \\(.", "Literal", "unnecessaryEscape", ["148", "149"], "Unnecessary escape character: \\).", ["150", "151"], {"range": "152", "text": "153"}, {"desc": "154", "fix": "155"}, {"kind": "156", "justification": "157"}, {"desc": "158", "fix": "159"}, [0, 2105], "import axios, { AxiosResponse, AxiosError } from 'axios';\n\n// Import types\nimport {\n  Transaction,\n  Case,\n  CaseCreate,\n  CaseUpdate,\n  CaseFilter,\n  CaseStats,\n  FeatureImportance,\n  FraudTrendData,\n  ModelInfo,\n  HealthStatus,\n  ServiceStats,\n  PaginatedResponse\n} from '../types';\n\n// Type definitions\nexport interface LoginRequest {\n  username: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  access_token: string;\n  token_type: string;\n}\n\nexport interface ApiError {\n  message: string;\n  status: number;\n  details?: any;\n}\n\n// API base URL\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 10000\n});\n\n// Add request interceptor for authentication\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers['Authorization'] = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error: AxiosError) => {\n    return Promise.reject(error);\n  }\n);\n\n// Add response interceptor for error handling\napi.interceptors.response.use(\n  (response: AxiosResponse) => response,\n  (error: AxiosError) => {\n    const apiError: ApiError = {\n      message: error.message || 'An error occurred',\n      status: error.response?.status || 500,\n      details: error.response?.data\n    };\n\n    // Handle authentication errors\n    if (error.response?.status === 401) {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n\n    return Promise.reject(apiError);\n  }\n);\n\n// Authentication API\nexport const loginUser = async (username: string, password: string): Promise<LoginResponse> => {\n  const formData = new FormData();\n  formData.append('username', username);\n  formData.append('password', password);\n\n  const response = await api.post<LoginResponse>('/auth/login', formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  });\n  return response.data;\n};", [0, 2832], "import React, { Component, ErrorInfo, ReactNode } from 'react';\n\n/**\n * Hook-based error boundary for functional components\n */\nimport { useState, useEffect } from 'react';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n  errorInfo?: ErrorInfo;\n}\n\n/**\n * Error Boundary component to catch and handle React errors\n */\nexport class ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    // Log error details\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    \n    this.setState({\n      error,\n      errorInfo\n    });\n\n    // Call custom error handler if provided\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n\n    // In production, you might want to send this to an error reporting service\n    if (process.env.NODE_ENV === 'production') {\n      // Example: Send to error reporting service\n      // errorReportingService.captureException(error, { extra: errorInfo });\n    }\n  }\n\n  handleRetry = () => {\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // Custom fallback UI\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      // Default fallback UI\n      return (\n        <div className=\"error-boundary\">\n          <div className=\"error-boundary-content\">\n            <h2>Something went wrong</h2>\n            <p>We're sorry, but something unexpected happened.</p>\n            \n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <details className=\"error-details\">\n                <summary>Error Details (Development Only)</summary>\n                <pre className=\"error-stack\">\n                  {this.state.error.toString()}\n                  {this.state.errorInfo?.componentStack}\n                </pre>\n              </details>\n            )}\n            \n            <div className=\"error-actions\">\n              <button \n                onClick={this.handleRetry}\n                className=\"retry-button\"\n              >\n                Try Again\n              </button>\n              <button \n                onClick={() => window.location.reload()}\n                className=\"reload-button\"\n              >\n                Reload Page\n              </button>\n            </div>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}", {"messageId": "160", "fix": "161", "desc": "162"}, {"messageId": "163", "fix": "164", "desc": "165"}, {"messageId": "160", "fix": "166", "desc": "162"}, {"messageId": "163", "fix": "167", "desc": "165"}, [0, 6699], "/**\n * Validation utilities for forms and data\n */\n\n/**\n * Form validation hook\n */\nimport { useState, useCallback } from 'react';\n\nexport interface ValidationRule {\n  required?: boolean;\n  minLength?: number;\n  maxLength?: number;\n  min?: number;\n  max?: number;\n  pattern?: RegExp;\n  custom?: (value: any) => string | null;\n}\n\nexport interface ValidationSchema {\n  [field: string]: ValidationRule;\n}\n\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: Record<string, string>;\n}\n\n/**\n * Validate a single field against a rule\n */\nexport const validateField = (value: any, rule: ValidationRule, fieldName: string): string | null => {\n  // Required validation\n  if (rule.required && (value === null || value === undefined || value === '')) {\n    return `${fieldName} is required`;\n  }\n  \n  // Skip other validations if value is empty and not required\n  if (!rule.required && (value === null || value === undefined || value === '')) {\n    return null;\n  }\n  \n  // String validations\n  if (typeof value === 'string') {\n    if (rule.minLength && value.length < rule.minLength) {\n      return `${fieldName} must be at least ${rule.minLength} characters`;\n    }\n    \n    if (rule.maxLength && value.length > rule.maxLength) {\n      return `${fieldName} must be no more than ${rule.maxLength} characters`;\n    }\n    \n    if (rule.pattern && !rule.pattern.test(value)) {\n      return `${fieldName} format is invalid`;\n    }\n  }\n  \n  // Number validations\n  if (typeof value === 'number') {\n    if (rule.min !== undefined && value < rule.min) {\n      return `${fieldName} must be at least ${rule.min}`;\n    }\n    \n    if (rule.max !== undefined && value > rule.max) {\n      return `${fieldName} must be no more than ${rule.max}`;\n    }\n  }\n  \n  // Custom validation\n  if (rule.custom) {\n    return rule.custom(value);\n  }\n  \n  return null;\n};\n\n/**\n * Validate an object against a schema\n */\nexport const validateSchema = (data: Record<string, any>, schema: ValidationSchema): ValidationResult => {\n  const errors: Record<string, string> = {};\n  \n  for (const [field, rule] of Object.entries(schema)) {\n    const error = validateField(data[field], rule, field);\n    if (error) {\n      errors[field] = error;\n    }\n  }\n  \n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors\n  };\n};\n\n/**\n * Common validation patterns\n */\nexport const patterns = {\n  email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  phone: /^\\+?[\\d\\s\\-\\(\\)]+$/,\n  url: /^https?:\\/\\/.+/,\n  alphanumeric: /^[a-zA-Z0-9]+$/,\n  numeric: /^\\d+$/,\n  decimal: /^\\d+(\\.\\d+)?$/,\n  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/,\n  transactionId: /^[A-Za-z0-9_-]+$/,\n  accountNumber: /^[A-Za-z0-9]+$/\n};\n\n/**\n * Pre-defined validation rules\n */\nexport const rules = {\n  required: { required: true },\n  email: { \n    required: true, \n    pattern: patterns.email \n  },\n  password: { \n    required: true, \n    minLength: 8, \n    pattern: patterns.password \n  },\n  phone: { \n    pattern: patterns.phone \n  },\n  url: { \n    pattern: patterns.url \n  },\n  transactionId: {\n    required: true,\n    pattern: patterns.transactionId,\n    minLength: 1,\n    maxLength: 50\n  },\n  amount: {\n    required: true,\n    min: 0,\n    custom: (value: any) => {\n      if (typeof value !== 'number' || isNaN(value)) {\n        return 'Amount must be a valid number';\n      }\n      return null;\n    }\n  },\n  riskScore: {\n    min: 0,\n    max: 1,\n    custom: (value: any) => {\n      if (value !== undefined && (typeof value !== 'number' || isNaN(value))) {\n        return 'Risk score must be a valid number';\n      }\n      return null;\n    }\n  }\n};\n\n/**\n * Validation schemas for common forms\n */\nexport const schemas = {\n  login: {\n    username: rules.required,\n    password: rules.required\n  },\n  \n  case: {\n    transaction_id: rules.transactionId,\n    tag: rules.required,\n    comment: { maxLength: 500 }\n  },\n  \n  transaction: {\n    transaction_id: rules.transactionId,\n    type: rules.required,\n    amount: rules.amount,\n    nameOrig: rules.required,\n    nameDest: rules.required\n  },\n  \n  user: {\n    username: {\n      required: true,\n      minLength: 3,\n      maxLength: 50,\n      pattern: patterns.alphanumeric\n    },\n    password: rules.password,\n    role: rules.required\n  }\n};\n\n/**\n * Sanitize input data\n */\nexport const sanitize = {\n  /**\n   * Remove HTML tags and dangerous characters\n   */\n  html: (input: string): string => {\n    if (!input) return '';\n    return input\n      .replace(/<[^>]*>/g, '') // Remove HTML tags\n      .replace(/[<>'\"&]/g, '') // Remove dangerous characters\n      .trim();\n  },\n  \n  /**\n   * Sanitize for SQL-like queries (basic protection)\n   */\n  sql: (input: string): string => {\n    if (!input) return '';\n    return input\n      .replace(/[';--]/g, '') // Remove SQL injection patterns\n      .trim();\n  },\n  \n  /**\n   * Sanitize numeric input\n   */\n  number: (input: any): number | null => {\n    if (input === null || input === undefined || input === '') {\n      return null;\n    }\n    \n    const num = Number(input);\n    return isNaN(num) ? null : num;\n  },\n  \n  /**\n   * Sanitize boolean input\n   */\n  boolean: (input: any): boolean => {\n    if (typeof input === 'boolean') return input;\n    if (typeof input === 'string') {\n      return input.toLowerCase() === 'true';\n    }\n    return Boolean(input);\n  },\n  \n  /**\n   * Sanitize array input\n   */\n  array: (input: any): any[] => {\n    if (Array.isArray(input)) return input;\n    if (input === null || input === undefined) return [];\n    return [input];\n  }\n};\n\n/**\n * Data type validators\n */\nexport const isValid = {\n  email: (email: string): boolean => patterns.email.test(email),\n  phone: (phone: string): boolean => patterns.phone.test(phone),\n  url: (url: string): boolean => patterns.url.test(url),\n  date: (date: string): boolean => !isNaN(Date.parse(date)),\n  json: (json: string): boolean => {\n    try {\n      JSON.parse(json);\n      return true;\n    } catch {\n      return false;\n    }\n  },\n  uuid: (uuid: string): boolean => {\n    const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n    return uuidPattern.test(uuid);\n  },\n  creditCard: (cardNumber: string): boolean => {\n    // Luhn algorithm for credit card validation\n    const cleaned = cardNumber.replace(/\\D/g, '');\n    if (cleaned.length < 13 || cleaned.length > 19) return false;\n    \n    let sum = 0;\n    let isEven = false;\n    \n    for (let i = cleaned.length - 1; i >= 0; i--) {\n      let digit = parseInt(cleaned[i]);\n      \n      if (isEven) {\n        digit *= 2;\n        if (digit > 9) {\n          digit -= 9;\n        }\n      }\n      \n      sum += digit;\n      isEven = !isEven;\n    }\n    \n    return sum % 10 === 0;\n  }\n};", "Update the dependencies array to be: [connectWebSocket, socket]", {"range": "168", "text": "169"}, "directive", "", "Update the dependencies array to be: [filter, loadCases]", {"range": "170", "text": "171"}, "removeEscape", {"range": "172", "text": "157"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "173", "text": "174"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "175", "text": "157"}, {"range": "176", "text": "174"}, [3669, 3671], "[connectWebSocket, socket]", [598, 606], "[filter, loadCases]", [2348, 2349], [2348, 2348], "\\", [2350, 2351], [2350, 2350]]