{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { Component } from 'react';\n/**\n * Error Boundary component to catch and handle React errors\n */\nexport class ErrorBoundary extends Component {\n  constructor(props) {\n    super(props);\n    this.handleRetry = () => {\n      this.setState({\n        hasError: false,\n        error: undefined,\n        errorInfo: undefined\n      });\n    };\n    this.state = {\n      hasError: false\n    };\n  }\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    // Log error details\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    this.setState({\n      error,\n      errorInfo\n    });\n\n    // Call custom error handler if provided\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n\n    // In production, you might want to send this to an error reporting service\n    if (process.env.NODE_ENV === 'production') {\n      // Example: Send to error reporting service\n      // errorReportingService.captureException(error, { extra: errorInfo });\n    }\n  }\n  render() {\n    if (this.state.hasError) {\n      var _this$state$errorInfo;\n      // Custom fallback UI\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      // Default fallback UI\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-boundary\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-boundary-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Something went wrong\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"We're sorry, but something unexpected happened.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), process.env.NODE_ENV === 'development' && this.state.error && /*#__PURE__*/_jsxDEV(\"details\", {\n            className: \"error-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n              children: \"Error Details (Development Only)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              className: \"error-stack\",\n              children: [this.state.error.toString(), (_this$state$errorInfo = this.state.errorInfo) === null || _this$state$errorInfo === void 0 ? void 0 : _this$state$errorInfo.componentStack]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: this.handleRetry,\n              className: \"retry-button\",\n              children: \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.reload(),\n              className: \"reload-button\",\n              children: \"Reload Page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\n\n/**\n * Hook-based error boundary for functional components\n */\nimport { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function useErrorHandler() {\n  _s();\n  const [error, setError] = useState(null);\n  const resetError = () => {\n    setError(null);\n  };\n  const captureError = error => {\n    setError(error);\n    console.error('Error captured:', error);\n  };\n\n  // Reset error when component unmounts\n  useEffect(() => {\n    return () => {\n      setError(null);\n    };\n  }, []);\n  return {\n    error,\n    resetError,\n    captureError\n  };\n}\n\n/**\n * Higher-order component to wrap components with error boundary\n */\n_s(useErrorHandler, \"JfhGochNIqPkY17zyDsXnSE7zLQ=\");\nexport function withErrorBoundary(Component, fallback, onError) {\n  const WrappedComponent = props => /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    fallback: fallback,\n    onError: onError,\n    children: /*#__PURE__*/_jsxDEV(Component, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this);\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n  return WrappedComponent;\n}\n\n/**\n * Async error boundary for handling promise rejections\n */\nexport function useAsyncError() {\n  _s2();\n  const [, setError] = useState();\n  return error => {\n    setError(() => {\n      throw error;\n    });\n  };\n}\n\n/**\n * Error fallback components\n */\n_s2(useAsyncError, \"lY5cRlK41sQS2+0BUhmGlzBHBNc=\");\nexport const ErrorFallback = ({\n  error,\n  resetError,\n  message = \"Something went wrong\"\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"error-fallback\",\n  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n    children: message\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n    className: \"error-message\",\n    children: error.message\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 7\n  }, this), resetError && /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: resetError,\n    className: \"retry-button\",\n    children: \"Try Again\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 7\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 174,\n  columnNumber: 3\n}, this);\n_c = ErrorFallback;\nexport const LoadingErrorFallback = ({\n  error,\n  retry\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"loading-error-fallback\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error-icon\",\n    children: \"\\u26A0\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 192,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n    children: \"Failed to Load\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n    children: (error === null || error === void 0 ? void 0 : error.message) || \"Unable to load the requested data\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 194,\n    columnNumber: 5\n  }, this), retry && /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: retry,\n    className: \"retry-button\",\n    children: \"Retry\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 196,\n    columnNumber: 7\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 191,\n  columnNumber: 3\n}, this);\n_c2 = LoadingErrorFallback;\nexport const NetworkErrorFallback = ({\n  retry\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"network-error-fallback\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error-icon\",\n    children: \"\\uD83C\\uDF10\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 207,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n    children: \"Network Error\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 208,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Please check your internet connection and try again.\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 209,\n    columnNumber: 5\n  }, this), retry && /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: retry,\n    className: \"retry-button\",\n    children: \"Retry\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 7\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 206,\n  columnNumber: 3\n}, this);\n\n/**\n * Global error handler for unhandled promise rejections\n */\n_c3 = NetworkErrorFallback;\nexport const setupGlobalErrorHandlers = () => {\n  // Handle unhandled promise rejections\n  window.addEventListener('unhandledrejection', event => {\n    console.error('Unhandled promise rejection:', event.reason);\n\n    // Prevent the default browser behavior\n    event.preventDefault();\n\n    // You can send this to an error reporting service\n    // errorReportingService.captureException(event.reason);\n  });\n\n  // Handle general JavaScript errors\n  window.addEventListener('error', event => {\n    console.error('Global error:', event.error);\n\n    // You can send this to an error reporting service\n    // errorReportingService.captureException(event.error);\n  });\n};\n\n/**\n * Error reporting utilities\n */\nexport const errorReporting = {\n  captureException: (error, context) => {\n    console.error('Error captured:', error, context);\n\n    // In production, send to error reporting service\n    if (process.env.NODE_ENV === 'production') {\n      // Example implementations:\n      // Sentry.captureException(error, { extra: context });\n      // LogRocket.captureException(error);\n      // Bugsnag.notify(error, context);\n    }\n  },\n  captureMessage: (message, level = 'info') => {\n    console[level]('Message captured:', message);\n\n    // In production, send to error reporting service\n    if (process.env.NODE_ENV === 'production') {\n      // Example: Sentry.captureMessage(message, level);\n    }\n  },\n  setUserContext: user => {\n    // Set user context for error reporting\n    if (process.env.NODE_ENV === 'production') {\n      // Example: Sentry.setUser(user);\n    }\n  },\n  addBreadcrumb: (message, category, data) => {\n    // Add breadcrumb for debugging\n    if (process.env.NODE_ENV === 'production') {\n      // Example: Sentry.addBreadcrumb({ message, category, data });\n    }\n  }\n};\nexport default ErrorBoundary;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ErrorFallback\");\n$RefreshReg$(_c2, \"LoadingErrorFallback\");\n$RefreshReg$(_c3, \"NetworkErrorFallback\");", "map": {"version": 3, "names": ["React", "Component", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "props", "handleRetry", "setState", "<PERSON><PERSON><PERSON><PERSON>", "error", "undefined", "errorInfo", "state", "getDerivedStateFromError", "componentDidCatch", "console", "onError", "process", "env", "NODE_ENV", "render", "_this$state$errorInfo", "fallback", "_jsxDEV", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toString", "componentStack", "onClick", "window", "location", "reload", "useState", "useEffect", "jsxDEV", "useErrorHandler", "_s", "setError", "resetError", "captureError", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WrappedComponent", "displayName", "name", "useAsyncError", "_s2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "message", "_c", "LoadingErrorFallback", "retry", "_c2", "NetworkErrorFallback", "_c3", "setupGlobalErrorHandlers", "addEventListener", "event", "reason", "preventDefault", "errorReporting", "captureException", "context", "captureMessage", "level", "setUserContext", "user", "addBreadcrumb", "category", "data", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/components/ErrorBoundary.tsx"], "sourcesContent": ["import React, { Component, ErrorInfo, ReactNode } from 'react';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n  errorInfo?: ErrorInfo;\n}\n\n/**\n * Error Boundary component to catch and handle React errors\n */\nexport class ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    // Log error details\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    \n    this.setState({\n      error,\n      errorInfo\n    });\n\n    // Call custom error handler if provided\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n\n    // In production, you might want to send this to an error reporting service\n    if (process.env.NODE_ENV === 'production') {\n      // Example: Send to error reporting service\n      // errorReportingService.captureException(error, { extra: errorInfo });\n    }\n  }\n\n  handleRetry = () => {\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // Custom fallback UI\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      // Default fallback UI\n      return (\n        <div className=\"error-boundary\">\n          <div className=\"error-boundary-content\">\n            <h2>Something went wrong</h2>\n            <p>We're sorry, but something unexpected happened.</p>\n            \n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <details className=\"error-details\">\n                <summary>Error Details (Development Only)</summary>\n                <pre className=\"error-stack\">\n                  {this.state.error.toString()}\n                  {this.state.errorInfo?.componentStack}\n                </pre>\n              </details>\n            )}\n            \n            <div className=\"error-actions\">\n              <button \n                onClick={this.handleRetry}\n                className=\"retry-button\"\n              >\n                Try Again\n              </button>\n              <button \n                onClick={() => window.location.reload()}\n                className=\"reload-button\"\n              >\n                Reload Page\n              </button>\n            </div>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n/**\n * Hook-based error boundary for functional components\n */\nimport { useState, useEffect } from 'react';\n\ninterface UseErrorHandlerReturn {\n  error: Error | null;\n  resetError: () => void;\n  captureError: (error: Error) => void;\n}\n\nexport function useErrorHandler(): UseErrorHandlerReturn {\n  const [error, setError] = useState<Error | null>(null);\n\n  const resetError = () => {\n    setError(null);\n  };\n\n  const captureError = (error: Error) => {\n    setError(error);\n    console.error('Error captured:', error);\n  };\n\n  // Reset error when component unmounts\n  useEffect(() => {\n    return () => {\n      setError(null);\n    };\n  }, []);\n\n  return { error, resetError, captureError };\n}\n\n/**\n * Higher-order component to wrap components with error boundary\n */\nexport function withErrorBoundary<P extends object>(\n  Component: React.ComponentType<P>,\n  fallback?: ReactNode,\n  onError?: (error: Error, errorInfo: ErrorInfo) => void\n) {\n  const WrappedComponent = (props: P) => (\n    <ErrorBoundary fallback={fallback} onError={onError}>\n      <Component {...props} />\n    </ErrorBoundary>\n  );\n\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n  \n  return WrappedComponent;\n}\n\n/**\n * Async error boundary for handling promise rejections\n */\nexport function useAsyncError() {\n  const [, setError] = useState();\n  \n  return (error: Error) => {\n    setError(() => {\n      throw error;\n    });\n  };\n}\n\n/**\n * Error fallback components\n */\nexport const ErrorFallback: React.FC<{\n  error?: Error;\n  resetError?: () => void;\n  message?: string;\n}> = ({ error, resetError, message = \"Something went wrong\" }) => (\n  <div className=\"error-fallback\">\n    <h3>{message}</h3>\n    {error && (\n      <p className=\"error-message\">{error.message}</p>\n    )}\n    {resetError && (\n      <button onClick={resetError} className=\"retry-button\">\n        Try Again\n      </button>\n    )}\n  </div>\n);\n\nexport const LoadingErrorFallback: React.FC<{\n  error?: Error;\n  retry?: () => void;\n}> = ({ error, retry }) => (\n  <div className=\"loading-error-fallback\">\n    <div className=\"error-icon\">⚠️</div>\n    <h3>Failed to Load</h3>\n    <p>{error?.message || \"Unable to load the requested data\"}</p>\n    {retry && (\n      <button onClick={retry} className=\"retry-button\">\n        Retry\n      </button>\n    )}\n  </div>\n);\n\nexport const NetworkErrorFallback: React.FC<{\n  retry?: () => void;\n}> = ({ retry }) => (\n  <div className=\"network-error-fallback\">\n    <div className=\"error-icon\">🌐</div>\n    <h3>Network Error</h3>\n    <p>Please check your internet connection and try again.</p>\n    {retry && (\n      <button onClick={retry} className=\"retry-button\">\n        Retry\n      </button>\n    )}\n  </div>\n);\n\n/**\n * Global error handler for unhandled promise rejections\n */\nexport const setupGlobalErrorHandlers = () => {\n  // Handle unhandled promise rejections\n  window.addEventListener('unhandledrejection', (event) => {\n    console.error('Unhandled promise rejection:', event.reason);\n    \n    // Prevent the default browser behavior\n    event.preventDefault();\n    \n    // You can send this to an error reporting service\n    // errorReportingService.captureException(event.reason);\n  });\n\n  // Handle general JavaScript errors\n  window.addEventListener('error', (event) => {\n    console.error('Global error:', event.error);\n    \n    // You can send this to an error reporting service\n    // errorReportingService.captureException(event.error);\n  });\n};\n\n/**\n * Error reporting utilities\n */\nexport const errorReporting = {\n  captureException: (error: Error, context?: Record<string, any>) => {\n    console.error('Error captured:', error, context);\n    \n    // In production, send to error reporting service\n    if (process.env.NODE_ENV === 'production') {\n      // Example implementations:\n      // Sentry.captureException(error, { extra: context });\n      // LogRocket.captureException(error);\n      // Bugsnag.notify(error, context);\n    }\n  },\n\n  captureMessage: (message: string, level: 'info' | 'warning' | 'error' = 'info') => {\n    console[level]('Message captured:', message);\n    \n    // In production, send to error reporting service\n    if (process.env.NODE_ENV === 'production') {\n      // Example: Sentry.captureMessage(message, level);\n    }\n  },\n\n  setUserContext: (user: { id: string; username: string; email?: string }) => {\n    // Set user context for error reporting\n    if (process.env.NODE_ENV === 'production') {\n      // Example: Sentry.setUser(user);\n    }\n  },\n\n  addBreadcrumb: (message: string, category?: string, data?: Record<string, any>) => {\n    // Add breadcrumb for debugging\n    if (process.env.NODE_ENV === 'production') {\n      // Example: Sentry.addBreadcrumb({ message, category, data });\n    }\n  }\n};\n\nexport default ErrorBoundary;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAA8B,OAAO;AAc9D;AACA;AACA;AACA,OAAO,MAAMC,aAAa,SAASD,SAAS,CAAe;EACzDE,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IAAC,KA8BfC,WAAW,GAAG,MAAM;MAClB,IAAI,CAACC,QAAQ,CAAC;QAAEC,QAAQ,EAAE,KAAK;QAAEC,KAAK,EAAEC,SAAS;QAAEC,SAAS,EAAED;MAAU,CAAC,CAAC;IAC5E,CAAC;IA/BC,IAAI,CAACE,KAAK,GAAG;MAAEJ,QAAQ,EAAE;IAAM,CAAC;EAClC;EAEA,OAAOK,wBAAwBA,CAACJ,KAAY,EAAS;IACnD;IACA,OAAO;MAAED,QAAQ,EAAE,IAAI;MAAEC;IAAM,CAAC;EAClC;EAEAK,iBAAiBA,CAACL,KAAY,EAAEE,SAAoB,EAAE;IACpD;IACAI,OAAO,CAACN,KAAK,CAAC,gCAAgC,EAAEA,KAAK,EAAEE,SAAS,CAAC;IAEjE,IAAI,CAACJ,QAAQ,CAAC;MACZE,KAAK;MACLE;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,IAAI,CAACN,KAAK,CAACW,OAAO,EAAE;MACtB,IAAI,CAACX,KAAK,CAACW,OAAO,CAACP,KAAK,EAAEE,SAAS,CAAC;IACtC;;IAEA;IACA,IAAIM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;MACA;IAAA;EAEJ;EAMAC,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACR,KAAK,CAACJ,QAAQ,EAAE;MAAA,IAAAa,qBAAA;MACvB;MACA,IAAI,IAAI,CAAChB,KAAK,CAACiB,QAAQ,EAAE;QACvB,OAAO,IAAI,CAACjB,KAAK,CAACiB,QAAQ;MAC5B;;MAEA;MACA,oBACEC,OAAA;QAAKC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BF,OAAA;UAAKC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCF,OAAA;YAAAE,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BN,OAAA;YAAAE,QAAA,EAAG;UAA+C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,EAErDZ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI,IAAI,CAACP,KAAK,CAACH,KAAK,iBACzDc,OAAA;YAASC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAChCF,OAAA;cAAAE,QAAA,EAAS;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACnDN,OAAA;cAAKC,SAAS,EAAC,aAAa;cAAAC,QAAA,GACzB,IAAI,CAACb,KAAK,CAACH,KAAK,CAACqB,QAAQ,CAAC,CAAC,GAAAT,qBAAA,GAC3B,IAAI,CAACT,KAAK,CAACD,SAAS,cAAAU,qBAAA,uBAApBA,qBAAA,CAAsBU,cAAc;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACV,eAEDN,OAAA;YAAKC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BF,OAAA;cACES,OAAO,EAAE,IAAI,CAAC1B,WAAY;cAC1BkB,SAAS,EAAC,cAAc;cAAAC,QAAA,EACzB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTN,OAAA;cACES,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;cACxCX,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC1B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,OAAO,IAAI,CAACxB,KAAK,CAACoB,QAAQ;EAC5B;AACF;;AAEA;AACA;AACA;AACA,SAASW,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAf,OAAA;AAQ5C,OAAO,SAASgB,eAAeA,CAAA,EAA0B;EAAAC,EAAA;EACvD,MAAM,CAAC/B,KAAK,EAAEgC,QAAQ,CAAC,GAAGL,QAAQ,CAAe,IAAI,CAAC;EAEtD,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvBD,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAME,YAAY,GAAIlC,KAAY,IAAK;IACrCgC,QAAQ,CAAChC,KAAK,CAAC;IACfM,OAAO,CAACN,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;EACzC,CAAC;;EAED;EACA4B,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXI,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IAAEhC,KAAK;IAAEiC,UAAU;IAAEC;EAAa,CAAC;AAC5C;;AAEA;AACA;AACA;AAFAH,EAAA,CAtBgBD,eAAe;AAyB/B,OAAO,SAASK,iBAAiBA,CAC/B1C,SAAiC,EACjCoB,QAAoB,EACpBN,OAAsD,EACtD;EACA,MAAM6B,gBAAgB,GAAIxC,KAAQ,iBAChCkB,OAAA,CAACpB,aAAa;IAACmB,QAAQ,EAAEA,QAAS;IAACN,OAAO,EAAEA,OAAQ;IAAAS,QAAA,eAClDF,OAAA,CAACrB,SAAS;MAAA,GAAKG;IAAK;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAChB;EAEDgB,gBAAgB,CAACC,WAAW,GAAG,qBAAqB5C,SAAS,CAAC4C,WAAW,IAAI5C,SAAS,CAAC6C,IAAI,GAAG;EAE9F,OAAOF,gBAAgB;AACzB;;AAEA;AACA;AACA;AACA,OAAO,SAASG,aAAaA,CAAA,EAAG;EAAAC,GAAA;EAC9B,MAAM,GAAGR,QAAQ,CAAC,GAAGL,QAAQ,CAAC,CAAC;EAE/B,OAAQ3B,KAAY,IAAK;IACvBgC,QAAQ,CAAC,MAAM;MACb,MAAMhC,KAAK;IACb,CAAC,CAAC;EACJ,CAAC;AACH;;AAEA;AACA;AACA;AAFAwC,GAAA,CAVgBD,aAAa;AAa7B,OAAO,MAAME,aAIX,GAAGA,CAAC;EAAEzC,KAAK;EAAEiC,UAAU;EAAES,OAAO,GAAG;AAAuB,CAAC,kBAC3D5B,OAAA;EAAKC,SAAS,EAAC,gBAAgB;EAAAC,QAAA,gBAC7BF,OAAA;IAAAE,QAAA,EAAK0B;EAAO;IAAAzB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC,EACjBpB,KAAK,iBACJc,OAAA;IAAGC,SAAS,EAAC,eAAe;IAAAC,QAAA,EAAEhB,KAAK,CAAC0C;EAAO;IAAAzB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAChD,EACAa,UAAU,iBACTnB,OAAA;IAAQS,OAAO,EAAEU,UAAW;IAAClB,SAAS,EAAC,cAAc;IAAAC,QAAA,EAAC;EAEtD;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAQ,CACT;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACE,CACN;AAACuB,EAAA,GAhBWF,aAIX;AAcF,OAAO,MAAMG,oBAGX,GAAGA,CAAC;EAAE5C,KAAK;EAAE6C;AAAM,CAAC,kBACpB/B,OAAA;EAAKC,SAAS,EAAC,wBAAwB;EAAAC,QAAA,gBACrCF,OAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC,eACpCN,OAAA;IAAAE,QAAA,EAAI;EAAc;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eACvBN,OAAA;IAAAE,QAAA,EAAI,CAAAhB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE0C,OAAO,KAAI;EAAmC;IAAAzB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,EAC7DyB,KAAK,iBACJ/B,OAAA;IAAQS,OAAO,EAAEsB,KAAM;IAAC9B,SAAS,EAAC,cAAc;IAAAC,QAAA,EAAC;EAEjD;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAQ,CACT;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACE,CACN;AAAC0B,GAAA,GAdWF,oBAGX;AAaF,OAAO,MAAMG,oBAEX,GAAGA,CAAC;EAAEF;AAAM,CAAC,kBACb/B,OAAA;EAAKC,SAAS,EAAC,wBAAwB;EAAAC,QAAA,gBACrCF,OAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC,eACpCN,OAAA;IAAAE,QAAA,EAAI;EAAa;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eACtBN,OAAA;IAAAE,QAAA,EAAG;EAAoD;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC,EAC1DyB,KAAK,iBACJ/B,OAAA;IAAQS,OAAO,EAAEsB,KAAM;IAAC9B,SAAS,EAAC,cAAc;IAAAC,QAAA,EAAC;EAEjD;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAQ,CACT;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACE,CACN;;AAED;AACA;AACA;AAFA4B,GAAA,GAfaD,oBAEX;AAgBF,OAAO,MAAME,wBAAwB,GAAGA,CAAA,KAAM;EAC5C;EACAzB,MAAM,CAAC0B,gBAAgB,CAAC,oBAAoB,EAAGC,KAAK,IAAK;IACvD7C,OAAO,CAACN,KAAK,CAAC,8BAA8B,EAAEmD,KAAK,CAACC,MAAM,CAAC;;IAE3D;IACAD,KAAK,CAACE,cAAc,CAAC,CAAC;;IAEtB;IACA;EACF,CAAC,CAAC;;EAEF;EACA7B,MAAM,CAAC0B,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;IAC1C7C,OAAO,CAACN,KAAK,CAAC,eAAe,EAAEmD,KAAK,CAACnD,KAAK,CAAC;;IAE3C;IACA;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMsD,cAAc,GAAG;EAC5BC,gBAAgB,EAAEA,CAACvD,KAAY,EAAEwD,OAA6B,KAAK;IACjElD,OAAO,CAACN,KAAK,CAAC,iBAAiB,EAAEA,KAAK,EAAEwD,OAAO,CAAC;;IAEhD;IACA,IAAIhD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;MACA;MACA;MACA;IAAA;EAEJ,CAAC;EAED+C,cAAc,EAAEA,CAACf,OAAe,EAAEgB,KAAmC,GAAG,MAAM,KAAK;IACjFpD,OAAO,CAACoD,KAAK,CAAC,CAAC,mBAAmB,EAAEhB,OAAO,CAAC;;IAE5C;IACA,IAAIlC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;IAAA;EAEJ,CAAC;EAEDiD,cAAc,EAAGC,IAAsD,IAAK;IAC1E;IACA,IAAIpD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;IAAA;EAEJ,CAAC;EAEDmD,aAAa,EAAEA,CAACnB,OAAe,EAAEoB,QAAiB,EAAEC,IAA0B,KAAK;IACjF;IACA,IAAIvD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;IAAA;EAEJ;AACF,CAAC;AAED,eAAehB,aAAa;AAAC,IAAAiD,EAAA,EAAAG,GAAA,EAAAE,GAAA;AAAAgB,YAAA,CAAArB,EAAA;AAAAqB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAhB,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}