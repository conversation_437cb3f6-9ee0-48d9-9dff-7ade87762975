{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\services\\\\WebSocketContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Create WebSocket context\nconst WebSocketContext = /*#__PURE__*/createContext(null);\n// WebSocket provider component\nexport const WebSocketProvider = ({\n  children\n}) => {\n  _s();\n  const [socket, setSocket] = useState(null);\n  const [connected, setConnected] = useState(false);\n  const [lastMessage, setLastMessage] = useState(null);\n  const [error, setError] = useState(null);\n  const reconnectTimeoutRef = useRef(null);\n  const reconnectAttemptsRef = useRef(0);\n  const maxReconnectAttempts = 5;\n\n  // Function to connect to WebSocket\n  const connectWebSocket = () => {\n    const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:9000/ws/txns';\n    try {\n      const ws = new WebSocket(wsUrl);\n      ws.onopen = () => {\n        console.log('WebSocket connected');\n        setConnected(true);\n        setError(null);\n        reconnectAttemptsRef.current = 0;\n      };\n      ws.onmessage = event => {\n        try {\n          // Validate message format\n          const data = JSON.parse(event.data);\n          setLastMessage(event.data);\n        } catch (parseError) {\n          console.error('Error parsing WebSocket message:', parseError);\n          setLastMessage(event.data); // Store raw data if parsing fails\n        }\n      };\n      ws.onerror = event => {\n        console.error('WebSocket error:', event);\n        setError('WebSocket connection error');\n      };\n      ws.onclose = event => {\n        console.log('WebSocket disconnected:', event.code, event.reason);\n        setConnected(false);\n\n        // Attempt to reconnect with exponential backoff\n        if (reconnectAttemptsRef.current < maxReconnectAttempts) {\n          const delay = Math.pow(2, reconnectAttemptsRef.current) * 1000; // Exponential backoff\n          reconnectAttemptsRef.current += 1;\n          reconnectTimeoutRef.current = setTimeout(() => {\n            console.log(`Attempting to reconnect (${reconnectAttemptsRef.current}/${maxReconnectAttempts})...`);\n            connectWebSocket();\n          }, delay);\n        } else {\n          setError('Failed to reconnect after maximum attempts');\n        }\n      };\n      setSocket(ws);\n    } catch (connectionError) {\n      console.error('Failed to create WebSocket connection:', connectionError);\n      setError('Failed to create WebSocket connection');\n    }\n  };\n\n  // Send message function\n  const sendMessage = message => {\n    if (socket && socket.readyState === WebSocket.OPEN) {\n      socket.send(message);\n    } else {\n      console.warn('WebSocket is not connected');\n    }\n  };\n\n  // Manual reconnect function\n  const reconnect = () => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n    }\n    reconnectAttemptsRef.current = 0;\n    setError(null);\n    connectWebSocket();\n  };\n\n  // Connect to WebSocket on mount\n  useEffect(() => {\n    connectWebSocket();\n\n    // Clean up on unmount\n    return () => {\n      if (reconnectTimeoutRef.current) {\n        clearTimeout(reconnectTimeoutRef.current);\n      }\n      if (socket) {\n        socket.close();\n      }\n    };\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Context value\n  const value = {\n    connected,\n    lastMessage,\n    error,\n    sendMessage,\n    reconnect\n  };\n  return /*#__PURE__*/_jsxDEV(WebSocketContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook for using WebSocket context\n_s(WebSocketProvider, \"ZEsyzQXWEagOcS2m7Nr8E4I3SIo=\");\n_c = WebSocketProvider;\nexport const useWebSocket = () => {\n  _s2();\n  const context = useContext(WebSocketContext);\n  if (!context) {\n    throw new Error('useWebSocket must be used within a WebSocketProvider');\n  }\n  return context;\n};\n_s2(useWebSocket, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"WebSocketProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "WebSocketContext", "WebSocketProvider", "children", "_s", "socket", "setSocket", "connected", "setConnected", "lastMessage", "setLastMessage", "error", "setError", "reconnectTimeoutRef", "reconnectAttemptsRef", "maxReconnectAttempts", "connectWebSocket", "wsUrl", "process", "env", "REACT_APP_WS_URL", "ws", "WebSocket", "onopen", "console", "log", "current", "onmessage", "event", "data", "JSON", "parse", "parseError", "onerror", "onclose", "code", "reason", "delay", "Math", "pow", "setTimeout", "connectionError", "sendMessage", "message", "readyState", "OPEN", "send", "warn", "reconnect", "clearTimeout", "close", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useWebSocket", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/services/WebSocketContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react';\nimport { WebSocketMessage } from '../types';\n\ninterface WebSocketContextType {\n  connected: boolean;\n  lastMessage: string | null;\n  error: string | null;\n  sendMessage: (message: string) => void;\n  reconnect: () => void;\n}\n\n// Create WebSocket context\nconst WebSocketContext = createContext<WebSocketContextType | null>(null);\n\ninterface WebSocketProviderProps {\n  children: ReactNode;\n}\n\n// WebSocket provider component\nexport const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {\n  const [socket, setSocket] = useState<WebSocket | null>(null);\n  const [connected, setConnected] = useState<boolean>(false);\n  const [lastMessage, setLastMessage] = useState<string | null>(null);\n  const [error, setError] = useState<string | null>(null);\n  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  const reconnectAttemptsRef = useRef<number>(0);\n  const maxReconnectAttempts = 5;\n\n  // Function to connect to WebSocket\n  const connectWebSocket = (): void => {\n    const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:9000/ws/txns';\n\n    try {\n      const ws = new WebSocket(wsUrl);\n\n      ws.onopen = () => {\n        console.log('WebSocket connected');\n        setConnected(true);\n        setError(null);\n        reconnectAttemptsRef.current = 0;\n      };\n\n      ws.onmessage = (event: MessageEvent) => {\n        try {\n          // Validate message format\n          const data = JSON.parse(event.data) as WebSocketMessage;\n          setLastMessage(event.data);\n        } catch (parseError) {\n          console.error('Error parsing WebSocket message:', parseError);\n          setLastMessage(event.data); // Store raw data if parsing fails\n        }\n      };\n\n      ws.onerror = (event: Event) => {\n        console.error('WebSocket error:', event);\n        setError('WebSocket connection error');\n      };\n\n      ws.onclose = (event: CloseEvent) => {\n        console.log('WebSocket disconnected:', event.code, event.reason);\n        setConnected(false);\n\n        // Attempt to reconnect with exponential backoff\n        if (reconnectAttemptsRef.current < maxReconnectAttempts) {\n          const delay = Math.pow(2, reconnectAttemptsRef.current) * 1000; // Exponential backoff\n          reconnectAttemptsRef.current += 1;\n\n          reconnectTimeoutRef.current = setTimeout(() => {\n            console.log(`Attempting to reconnect (${reconnectAttemptsRef.current}/${maxReconnectAttempts})...`);\n            connectWebSocket();\n          }, delay);\n        } else {\n          setError('Failed to reconnect after maximum attempts');\n        }\n      };\n\n      setSocket(ws);\n    } catch (connectionError) {\n      console.error('Failed to create WebSocket connection:', connectionError);\n      setError('Failed to create WebSocket connection');\n    }\n  };\n\n  // Send message function\n  const sendMessage = (message: string): void => {\n    if (socket && socket.readyState === WebSocket.OPEN) {\n      socket.send(message);\n    } else {\n      console.warn('WebSocket is not connected');\n    }\n  };\n\n  // Manual reconnect function\n  const reconnect = (): void => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n    }\n    reconnectAttemptsRef.current = 0;\n    setError(null);\n    connectWebSocket();\n  };\n\n  // Connect to WebSocket on mount\n  useEffect(() => {\n    connectWebSocket();\n\n    // Clean up on unmount\n    return () => {\n      if (reconnectTimeoutRef.current) {\n        clearTimeout(reconnectTimeoutRef.current);\n      }\n      if (socket) {\n        socket.close();\n      }\n    };\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Context value\n  const value: WebSocketContextType = {\n    connected,\n    lastMessage,\n    error,\n    sendMessage,\n    reconnect\n  };\n\n  return (\n    <WebSocketContext.Provider value={value}>\n      {children}\n    </WebSocketContext.Provider>\n  );\n};\n\n// Custom hook for using WebSocket context\nexport const useWebSocket = (): WebSocketContextType => {\n  const context = useContext(WebSocketContext);\n  if (!context) {\n    throw new Error('useWebSocket must be used within a WebSocketProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAaC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWjG;AACA,MAAMC,gBAAgB,gBAAGP,aAAa,CAA8B,IAAI,CAAC;AAMzE;AACA,OAAO,MAAMQ,iBAAmD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACnF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGV,QAAQ,CAAmB,IAAI,CAAC;EAC5D,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAU,KAAK,CAAC;EAC1D,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAMiB,mBAAmB,GAAGf,MAAM,CAAwB,IAAI,CAAC;EAC/D,MAAMgB,oBAAoB,GAAGhB,MAAM,CAAS,CAAC,CAAC;EAC9C,MAAMiB,oBAAoB,GAAG,CAAC;;EAE9B;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAY;IACnC,MAAMC,KAAK,GAAGC,OAAO,CAACC,GAAG,CAACC,gBAAgB,IAAI,6BAA6B;IAE3E,IAAI;MACF,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACL,KAAK,CAAC;MAE/BI,EAAE,CAACE,MAAM,GAAG,MAAM;QAChBC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAClCjB,YAAY,CAAC,IAAI,CAAC;QAClBI,QAAQ,CAAC,IAAI,CAAC;QACdE,oBAAoB,CAACY,OAAO,GAAG,CAAC;MAClC,CAAC;MAEDL,EAAE,CAACM,SAAS,GAAIC,KAAmB,IAAK;QACtC,IAAI;UACF;UACA,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAqB;UACvDnB,cAAc,CAACkB,KAAK,CAACC,IAAI,CAAC;QAC5B,CAAC,CAAC,OAAOG,UAAU,EAAE;UACnBR,OAAO,CAACb,KAAK,CAAC,kCAAkC,EAAEqB,UAAU,CAAC;UAC7DtB,cAAc,CAACkB,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;QAC9B;MACF,CAAC;MAEDR,EAAE,CAACY,OAAO,GAAIL,KAAY,IAAK;QAC7BJ,OAAO,CAACb,KAAK,CAAC,kBAAkB,EAAEiB,KAAK,CAAC;QACxChB,QAAQ,CAAC,4BAA4B,CAAC;MACxC,CAAC;MAEDS,EAAE,CAACa,OAAO,GAAIN,KAAiB,IAAK;QAClCJ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEG,KAAK,CAACO,IAAI,EAAEP,KAAK,CAACQ,MAAM,CAAC;QAChE5B,YAAY,CAAC,KAAK,CAAC;;QAEnB;QACA,IAAIM,oBAAoB,CAACY,OAAO,GAAGX,oBAAoB,EAAE;UACvD,MAAMsB,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEzB,oBAAoB,CAACY,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;UAChEZ,oBAAoB,CAACY,OAAO,IAAI,CAAC;UAEjCb,mBAAmB,CAACa,OAAO,GAAGc,UAAU,CAAC,MAAM;YAC7ChB,OAAO,CAACC,GAAG,CAAC,4BAA4BX,oBAAoB,CAACY,OAAO,IAAIX,oBAAoB,MAAM,CAAC;YACnGC,gBAAgB,CAAC,CAAC;UACpB,CAAC,EAAEqB,KAAK,CAAC;QACX,CAAC,MAAM;UACLzB,QAAQ,CAAC,4CAA4C,CAAC;QACxD;MACF,CAAC;MAEDN,SAAS,CAACe,EAAE,CAAC;IACf,CAAC,CAAC,OAAOoB,eAAe,EAAE;MACxBjB,OAAO,CAACb,KAAK,CAAC,wCAAwC,EAAE8B,eAAe,CAAC;MACxE7B,QAAQ,CAAC,uCAAuC,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAM8B,WAAW,GAAIC,OAAe,IAAW;IAC7C,IAAItC,MAAM,IAAIA,MAAM,CAACuC,UAAU,KAAKtB,SAAS,CAACuB,IAAI,EAAE;MAClDxC,MAAM,CAACyC,IAAI,CAACH,OAAO,CAAC;IACtB,CAAC,MAAM;MACLnB,OAAO,CAACuB,IAAI,CAAC,4BAA4B,CAAC;IAC5C;EACF,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGA,CAAA,KAAY;IAC5B,IAAInC,mBAAmB,CAACa,OAAO,EAAE;MAC/BuB,YAAY,CAACpC,mBAAmB,CAACa,OAAO,CAAC;IAC3C;IACAZ,oBAAoB,CAACY,OAAO,GAAG,CAAC;IAChCd,QAAQ,CAAC,IAAI,CAAC;IACdI,gBAAgB,CAAC,CAAC;EACpB,CAAC;;EAED;EACAnB,SAAS,CAAC,MAAM;IACdmB,gBAAgB,CAAC,CAAC;;IAElB;IACA,OAAO,MAAM;MACX,IAAIH,mBAAmB,CAACa,OAAO,EAAE;QAC/BuB,YAAY,CAACpC,mBAAmB,CAACa,OAAO,CAAC;MAC3C;MACA,IAAIrB,MAAM,EAAE;QACVA,MAAM,CAAC6C,KAAK,CAAC,CAAC;MAChB;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,MAAMC,KAA2B,GAAG;IAClC5C,SAAS;IACTE,WAAW;IACXE,KAAK;IACL+B,WAAW;IACXM;EACF,CAAC;EAED,oBACEhD,OAAA,CAACC,gBAAgB,CAACmD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAhD,QAAA,EACrCA;EAAQ;IAAAkD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAEhC,CAAC;;AAED;AAAApD,EAAA,CAlHaF,iBAAmD;AAAAuD,EAAA,GAAnDvD,iBAAmD;AAmHhE,OAAO,MAAMwD,YAAY,GAAGA,CAAA,KAA4B;EAAAC,GAAA;EACtD,MAAMC,OAAO,GAAGjE,UAAU,CAACM,gBAAgB,CAAC;EAC5C,IAAI,CAAC2D,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,sDAAsD,CAAC;EACzE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,YAAY;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}