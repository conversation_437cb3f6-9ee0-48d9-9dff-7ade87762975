"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDevServerConfigProviderProxy = exports.createWebpackProdConfig = exports.createWebpackDevConfig = exports.createJestConfig = exports.gitHubIssueUrl = exports.throwUnexpectedConfigError = exports.whenTest = exports.whenProd = exports.whenDev = exports.when = exports.removePlugins = exports.addPlugins = exports.pluginByName = exports.getPlugin = exports.assetModuleByName = exports.addAfterAssetModules = exports.addAfterAssetModule = exports.addBeforeAssetModules = exports.addBeforeAssetModule = exports.removeAssetModules = exports.getAssetModules = exports.getAssetModule = exports.loaderByName = exports.addAfterLoaders = exports.addAfterLoader = exports.addBeforeLoaders = exports.addBeforeLoader = exports.removeLoaders = exports.getLoaders = exports.getLoader = void 0;
var asset_modules_1 = require("./lib/asset-modules");
Object.defineProperty(exports, "addAfterAssetModule", { enumerable: true, get: function () { return asset_modules_1.addAfterAssetModule; } });
Object.defineProperty(exports, "addAfterAssetModules", { enumerable: true, get: function () { return asset_modules_1.addAfterAssetModules; } });
Object.defineProperty(exports, "addBeforeAssetModule", { enumerable: true, get: function () { return asset_modules_1.addBeforeAssetModule; } });
Object.defineProperty(exports, "addBeforeAssetModules", { enumerable: true, get: function () { return asset_modules_1.addBeforeAssetModules; } });
Object.defineProperty(exports, "assetModuleByName", { enumerable: true, get: function () { return asset_modules_1.assetModuleByName; } });
Object.defineProperty(exports, "getAssetModule", { enumerable: true, get: function () { return asset_modules_1.getAssetModule; } });
Object.defineProperty(exports, "getAssetModules", { enumerable: true, get: function () { return asset_modules_1.getAssetModules; } });
Object.defineProperty(exports, "removeAssetModules", { enumerable: true, get: function () { return asset_modules_1.removeAssetModules; } });
var api_1 = require("./lib/features/dev-server/api");
Object.defineProperty(exports, "createDevServerConfigProviderProxy", { enumerable: true, get: function () { return api_1.createDevServerConfigProviderProxy; } });
var api_2 = require("./lib/features/jest/api");
Object.defineProperty(exports, "createJestConfig", { enumerable: true, get: function () { return api_2.createJestConfig; } });
var api_3 = require("./lib/features/webpack/api");
Object.defineProperty(exports, "createWebpackDevConfig", { enumerable: true, get: function () { return api_3.createWebpackDevConfig; } });
Object.defineProperty(exports, "createWebpackProdConfig", { enumerable: true, get: function () { return api_3.createWebpackProdConfig; } });
var loaders_1 = require("./lib/loaders");
Object.defineProperty(exports, "addAfterLoader", { enumerable: true, get: function () { return loaders_1.addAfterLoader; } });
Object.defineProperty(exports, "addAfterLoaders", { enumerable: true, get: function () { return loaders_1.addAfterLoaders; } });
Object.defineProperty(exports, "addBeforeLoader", { enumerable: true, get: function () { return loaders_1.addBeforeLoader; } });
Object.defineProperty(exports, "addBeforeLoaders", { enumerable: true, get: function () { return loaders_1.addBeforeLoaders; } });
Object.defineProperty(exports, "getLoader", { enumerable: true, get: function () { return loaders_1.getLoader; } });
Object.defineProperty(exports, "getLoaders", { enumerable: true, get: function () { return loaders_1.getLoaders; } });
Object.defineProperty(exports, "loaderByName", { enumerable: true, get: function () { return loaders_1.loaderByName; } });
Object.defineProperty(exports, "removeLoaders", { enumerable: true, get: function () { return loaders_1.removeLoaders; } });
var plugin_utils_1 = require("./lib/plugin-utils");
Object.defineProperty(exports, "gitHubIssueUrl", { enumerable: true, get: function () { return plugin_utils_1.gitHubIssueUrl; } });
Object.defineProperty(exports, "throwUnexpectedConfigError", { enumerable: true, get: function () { return plugin_utils_1.throwUnexpectedConfigError; } });
var user_config_utils_1 = require("./lib/user-config-utils");
Object.defineProperty(exports, "when", { enumerable: true, get: function () { return user_config_utils_1.when; } });
Object.defineProperty(exports, "whenDev", { enumerable: true, get: function () { return user_config_utils_1.whenDev; } });
Object.defineProperty(exports, "whenProd", { enumerable: true, get: function () { return user_config_utils_1.whenProd; } });
Object.defineProperty(exports, "whenTest", { enumerable: true, get: function () { return user_config_utils_1.whenTest; } });
var webpack_plugins_1 = require("./lib/webpack-plugins");
Object.defineProperty(exports, "addPlugins", { enumerable: true, get: function () { return webpack_plugins_1.addPlugins; } });
Object.defineProperty(exports, "getPlugin", { enumerable: true, get: function () { return webpack_plugins_1.getPlugin; } });
Object.defineProperty(exports, "pluginByName", { enumerable: true, get: function () { return webpack_plugins_1.pluginByName; } });
Object.defineProperty(exports, "removePlugins", { enumerable: true, get: function () { return webpack_plugins_1.removePlugins; } });
