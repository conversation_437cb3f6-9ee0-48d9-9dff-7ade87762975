{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\pages\\\\Unauthorized.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../services/AuthContext';\nimport { UserRole } from '../types';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Unauthorized = () => {\n  _s();\n  var _state$from;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const state = location.state;\n  const requiredRole = state === null || state === void 0 ? void 0 : state.requiredRole;\n  const fromPath = (state === null || state === void 0 ? void 0 : (_state$from = state.from) === null || _state$from === void 0 ? void 0 : _state$from.pathname) || '/';\n  const handleGoBack = () => {\n    navigate(-1);\n  };\n  const handleGoHome = () => {\n    navigate('/');\n  };\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n  const getRoleDisplayName = role => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return 'Administrator';\n      case UserRole.ANALYST:\n        return 'Analyst';\n      case UserRole.AUDITOR:\n        return 'Auditor';\n      default:\n        return role;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto h-24 w-24 text-red-500\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-full w-full\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mt-6 text-3xl font-extrabold text-gray-900\",\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-sm text-gray-600\",\n          children: \"You don't have permission to access this page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), requiredRole && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-yellow-800\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Required Role:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 17\n            }, this), \" \", getRoleDisplayName(requiredRole)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this), user && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-yellow-800 mt-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Your Role:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 19\n            }, this), \" \", getRoleDisplayName(user.role)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleGoBack,\n            className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: \"Go Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleGoHome,\n            className: \"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: \"Go to Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleLogout,\n            className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-red-600 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n            children: \"Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 text-xs text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"If you believe this is an error, please contact your administrator.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), fromPath && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1\",\n            children: [\"Attempted to access: \", /*#__PURE__*/_jsxDEV(\"code\", {\n              className: \"bg-gray-100 px-1 rounded\",\n              children: fromPath\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(Unauthorized, \"5H37BwHXpxPtdZBjRNkyB+P8QZs=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = Unauthorized;\nexport default Unauthorized;\nvar _c;\n$RefreshReg$(_c, \"Unauthorized\");", "map": {"version": 3, "names": ["React", "useNavigate", "useLocation", "useAuth", "UserRole", "jsxDEV", "_jsxDEV", "Unauthorized", "_s", "_state$from", "navigate", "location", "user", "logout", "state", "requiredRole", "fromPath", "from", "pathname", "handleGoBack", "handleGoHome", "handleLogout", "getRoleDisplayName", "role", "ADMIN", "ANALYST", "AUDITOR", "className", "children", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/pages/Unauthorized.tsx"], "sourcesContent": ["import React from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../services/AuthContext';\nimport { UserRole } from '../types';\n\ninterface LocationState {\n  from?: {\n    pathname: string;\n  };\n  requiredRole?: UserRole;\n}\n\nconst Unauthorized: React.FC = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, logout } = useAuth();\n  \n  const state = location.state as LocationState;\n  const requiredRole = state?.requiredRole;\n  const fromPath = state?.from?.pathname || '/';\n\n  const handleGoBack = () => {\n    navigate(-1);\n  };\n\n  const handleGoHome = () => {\n    navigate('/');\n  };\n\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n\n  const getRoleDisplayName = (role: UserRole): string => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return 'Administrator';\n      case UserRole.ANALYST:\n        return 'Analyst';\n      case UserRole.AUDITOR:\n        return 'Auditor';\n      default:\n        return role;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"text-center\">\n          <div className=\"mx-auto h-24 w-24 text-red-500\">\n            <svg\n              className=\"h-full w-full\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"\n              />\n            </svg>\n          </div>\n          \n          <h2 className=\"mt-6 text-3xl font-extrabold text-gray-900\">\n            Access Denied\n          </h2>\n          \n          <p className=\"mt-2 text-sm text-gray-600\">\n            You don't have permission to access this page.\n          </p>\n          \n          {requiredRole && (\n            <div className=\"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md\">\n              <p className=\"text-sm text-yellow-800\">\n                <strong>Required Role:</strong> {getRoleDisplayName(requiredRole)}\n              </p>\n              {user && (\n                <p className=\"text-sm text-yellow-800 mt-1\">\n                  <strong>Your Role:</strong> {getRoleDisplayName(user.role)}\n                </p>\n              )}\n            </div>\n          )}\n          \n          <div className=\"mt-6 space-y-3\">\n            <button\n              onClick={handleGoBack}\n              className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              Go Back\n            </button>\n            \n            <button\n              onClick={handleGoHome}\n              className=\"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              Go to Dashboard\n            </button>\n            \n            <button\n              onClick={handleLogout}\n              className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-red-600 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\n            >\n              Logout\n            </button>\n          </div>\n          \n          <div className=\"mt-6 text-xs text-gray-500\">\n            <p>\n              If you believe this is an error, please contact your administrator.\n            </p>\n            {fromPath && (\n              <p className=\"mt-1\">\n                Attempted to access: <code className=\"bg-gray-100 px-1 rounded\">{fromPath}</code>\n              </p>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Unauthorized;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASpC,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,WAAA;EACnC,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU,IAAI;IAAEC;EAAO,CAAC,GAAGV,OAAO,CAAC,CAAC;EAElC,MAAMW,KAAK,GAAGH,QAAQ,CAACG,KAAsB;EAC7C,MAAMC,YAAY,GAAGD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,YAAY;EACxC,MAAMC,QAAQ,GAAG,CAAAF,KAAK,aAALA,KAAK,wBAAAL,WAAA,GAALK,KAAK,CAAEG,IAAI,cAAAR,WAAA,uBAAXA,WAAA,CAAaS,QAAQ,KAAI,GAAG;EAE7C,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBT,QAAQ,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;EAED,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzBV,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,MAAMW,YAAY,GAAGA,CAAA,KAAM;IACzBR,MAAM,CAAC,CAAC;IACRH,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMY,kBAAkB,GAAIC,IAAc,IAAa;IACrD,QAAQA,IAAI;MACV,KAAKnB,QAAQ,CAACoB,KAAK;QACjB,OAAO,eAAe;MACxB,KAAKpB,QAAQ,CAACqB,OAAO;QACnB,OAAO,SAAS;MAClB,KAAKrB,QAAQ,CAACsB,OAAO;QACnB,OAAO,SAAS;MAClB;QACE,OAAOH,IAAI;IACf;EACF,CAAC;EAED,oBACEjB,OAAA;IAAKqB,SAAS,EAAC,qFAAqF;IAAAC,QAAA,eAClGtB,OAAA;MAAKqB,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxCtB,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtB,OAAA;UAAKqB,SAAS,EAAC,gCAAgC;UAAAC,QAAA,eAC7CtB,OAAA;YACEqB,SAAS,EAAC,eAAe;YACzBE,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnBC,MAAM,EAAC,cAAc;YAAAH,QAAA,eAErBtB,OAAA;cACE0B,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAC;YAA2I;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9I;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjC,OAAA;UAAIqB,SAAS,EAAC,4CAA4C;UAAAC,QAAA,EAAC;QAE3D;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELjC,OAAA;UAAGqB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAEHxB,YAAY,iBACXT,OAAA;UAAKqB,SAAS,EAAC,2DAA2D;UAAAC,QAAA,gBACxEtB,OAAA;YAAGqB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACpCtB,OAAA;cAAAsB,QAAA,EAAQ;YAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjB,kBAAkB,CAACP,YAAY,CAAC;UAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,EACH3B,IAAI,iBACHN,OAAA;YAAGqB,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBACzCtB,OAAA;cAAAsB,QAAA,EAAQ;YAAU;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjB,kBAAkB,CAACV,IAAI,CAACW,IAAI,CAAC;UAAA;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAEDjC,OAAA;UAAKqB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BtB,OAAA;YACEkC,OAAO,EAAErB,YAAa;YACtBQ,SAAS,EAAC,0NAA0N;YAAAC,QAAA,EACrO;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETjC,OAAA;YACEkC,OAAO,EAAEpB,YAAa;YACtBO,SAAS,EAAC,sNAAsN;YAAAC,QAAA,EACjO;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETjC,OAAA;YACEkC,OAAO,EAAEnB,YAAa;YACtBM,SAAS,EAAC,wNAAwN;YAAAC,QAAA,EACnO;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENjC,OAAA;UAAKqB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzCtB,OAAA;YAAAsB,QAAA,EAAG;UAEH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,EACHvB,QAAQ,iBACPV,OAAA;YAAGqB,SAAS,EAAC,MAAM;YAAAC,QAAA,GAAC,uBACG,eAAAtB,OAAA;cAAMqB,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAEZ;YAAQ;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAjHID,YAAsB;EAAA,QACTN,WAAW,EACXC,WAAW,EACHC,OAAO;AAAA;AAAAsC,EAAA,GAH5BlC,YAAsB;AAmH5B,eAAeA,YAAY;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}