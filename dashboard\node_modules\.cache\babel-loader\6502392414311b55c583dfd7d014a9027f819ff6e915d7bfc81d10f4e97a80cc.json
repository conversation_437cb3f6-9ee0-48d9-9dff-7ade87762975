{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\components\\\\TransactionTable.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport '../styles/TransactionTable.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TransactionTable = ({\n  transactions,\n  onSelectTransaction,\n  isLoading\n}) => {\n  _s();\n  const [sortConfig, setSortConfig] = useState({\n    key: 'timestamp',\n    direction: 'desc'\n  });\n  const [sortedTransactions, setSortedTransactions] = useState([]);\n  const parentRef = React.useRef();\n\n  // Apply sorting\n  useEffect(() => {\n    const sorted = [...transactions].sort((a, b) => {\n      if (a[sortConfig.key] < b[sortConfig.key]) {\n        return sortConfig.direction === 'asc' ? -1 : 1;\n      }\n      if (a[sortConfig.key] > b[sortConfig.key]) {\n        return sortConfig.direction === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n    setSortedTransactions(sorted);\n  }, [transactions, sortConfig]);\n\n  // Set up virtualization\n  const rowVirtualizer = useVirtual({\n    size: sortedTransactions.length,\n    parentRef,\n    estimateSize: React.useCallback(() => 60, []),\n    overscan: 10\n  });\n  const handleSort = key => {\n    setSortConfig(prevConfig => ({\n      key,\n      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'\n    }));\n  };\n  const getRiskClass = riskScore => {\n    if (riskScore >= 0.8) return 'high-risk';\n    if (riskScore >= 0.5) return 'medium-risk';\n    return 'low-risk';\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  const formatDate = dateString => {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"transaction-table-wrapper\",\n    children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-indicator\",\n      children: \"Loading transactions...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transaction-table-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-cell\",\n          onClick: () => handleSort('transaction_id'),\n          children: [\"ID \", sortConfig.key === 'transaction_id' && (sortConfig.direction === 'asc' ? '↑' : '↓')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-cell\",\n          onClick: () => handleSort('type'),\n          children: [\"Type \", sortConfig.key === 'type' && (sortConfig.direction === 'asc' ? '↑' : '↓')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-cell\",\n          onClick: () => handleSort('amount'),\n          children: [\"Amount \", sortConfig.key === 'amount' && (sortConfig.direction === 'asc' ? '↑' : '↓')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-cell\",\n          onClick: () => handleSort('risk_score'),\n          children: [\"Risk Score \", sortConfig.key === 'risk_score' && (sortConfig.direction === 'asc' ? '↑' : '↓')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-cell\",\n          onClick: () => handleSort('timestamp'),\n          children: [\"Timestamp \", sortConfig.key === 'timestamp' && (sortConfig.direction === 'asc' ? '↑' : '↓')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-cell\",\n          children: \"Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: parentRef,\n        className: \"transaction-table-body\",\n        style: {\n          height: '500px',\n          overflow: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: `${rowVirtualizer.totalSize}px`,\n            width: '100%',\n            position: 'relative'\n          },\n          children: rowVirtualizer.virtualItems.map(virtualRow => {\n            const transaction = sortedTransactions[virtualRow.index];\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"transaction-row\",\n              style: {\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                width: '100%',\n                height: `${virtualRow.size}px`,\n                transform: `translateY(${virtualRow.start}px)`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cell\",\n                children: transaction.transaction_id || transaction.nameOrig\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cell\",\n                children: transaction.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cell\",\n                children: formatCurrency(transaction.amount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cell\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `risk-badge ${getRiskClass(transaction.risk_score)}`,\n                  children: [(transaction.risk_score * 100).toFixed(1), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cell\",\n                children: formatDate(transaction.timestamp)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cell\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"view-button\",\n                  onClick: () => onSelectTransaction(transaction),\n                  children: \"View\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 21\n              }, this)]\n            }, virtualRow.index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this), sortedTransactions.length === 0 && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-data\",\n        children: \"No transactions found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_s(TransactionTable, \"vXFX9/Tx+4ovNENRHQLUzT0hVZQ=\", true);\n_c = TransactionTable;\nexport default TransactionTable;\nvar _c;\n$RefreshReg$(_c, \"TransactionTable\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TransactionTable", "transactions", "onSelectTransaction", "isLoading", "_s", "sortConfig", "setSortConfig", "key", "direction", "sortedTransactions", "setSortedTransactions", "parentRef", "useRef", "sorted", "sort", "a", "b", "rowVirtualizer", "useVirtual", "size", "length", "estimateSize", "useCallback", "overscan", "handleSort", "prevConfig", "getRiskClass", "riskScore", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "formatDate", "dateString", "date", "Date", "toLocaleString", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "ref", "height", "overflow", "totalSize", "width", "position", "virtualItems", "map", "virtualRow", "transaction", "index", "top", "left", "transform", "start", "transaction_id", "name<PERSON><PERSON>", "type", "risk_score", "toFixed", "timestamp", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/components/TransactionTable.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useVirtualizer } from '@tanstack/react-virtual';\nimport '../styles/TransactionTable.css';\n\nconst TransactionTable = ({ transactions, onSelectTransaction, isLoading }) => {\n  const [sortConfig, setSortConfig] = useState({ key: 'timestamp', direction: 'desc' });\n  const [sortedTransactions, setSortedTransactions] = useState([]);\n\n  const parentRef = React.useRef();\n\n  // Apply sorting\n  useEffect(() => {\n    const sorted = [...transactions].sort((a, b) => {\n      if (a[sortConfig.key] < b[sortConfig.key]) {\n        return sortConfig.direction === 'asc' ? -1 : 1;\n      }\n      if (a[sortConfig.key] > b[sortConfig.key]) {\n        return sortConfig.direction === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n\n    setSortedTransactions(sorted);\n  }, [transactions, sortConfig]);\n\n  // Set up virtualization\n  const rowVirtualizer = useVirtual({\n    size: sortedTransactions.length,\n    parentRef,\n    estimateSize: React.useCallback(() => 60, []),\n    overscan: 10\n  });\n\n  const handleSort = (key) => {\n    setSortConfig(prevConfig => ({\n      key,\n      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'\n    }));\n  };\n\n  const getRiskClass = (riskScore) => {\n    if (riskScore >= 0.8) return 'high-risk';\n    if (riskScore >= 0.5) return 'medium-risk';\n    return 'low-risk';\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    return date.toLocaleString();\n  };\n\n  return (\n    <div className=\"transaction-table-wrapper\">\n      {isLoading ? (\n        <div className=\"loading-indicator\">Loading transactions...</div>\n      ) : (\n        <>\n          <div className=\"transaction-table-header\">\n            <div className=\"header-cell\" onClick={() => handleSort('transaction_id')}>\n              ID {sortConfig.key === 'transaction_id' && (sortConfig.direction === 'asc' ? '↑' : '↓')}\n            </div>\n            <div className=\"header-cell\" onClick={() => handleSort('type')}>\n              Type {sortConfig.key === 'type' && (sortConfig.direction === 'asc' ? '↑' : '↓')}\n            </div>\n            <div className=\"header-cell\" onClick={() => handleSort('amount')}>\n              Amount {sortConfig.key === 'amount' && (sortConfig.direction === 'asc' ? '↑' : '↓')}\n            </div>\n            <div className=\"header-cell\" onClick={() => handleSort('risk_score')}>\n              Risk Score {sortConfig.key === 'risk_score' && (sortConfig.direction === 'asc' ? '↑' : '↓')}\n            </div>\n            <div className=\"header-cell\" onClick={() => handleSort('timestamp')}>\n              Timestamp {sortConfig.key === 'timestamp' && (sortConfig.direction === 'asc' ? '↑' : '↓')}\n            </div>\n            <div className=\"header-cell\">Actions</div>\n          </div>\n\n          <div\n            ref={parentRef}\n            className=\"transaction-table-body\"\n            style={{ height: '500px', overflow: 'auto' }}\n          >\n            <div\n              style={{\n                height: `${rowVirtualizer.totalSize}px`,\n                width: '100%',\n                position: 'relative'\n              }}\n            >\n              {rowVirtualizer.virtualItems.map(virtualRow => {\n                const transaction = sortedTransactions[virtualRow.index];\n                return (\n                  <div\n                    key={virtualRow.index}\n                    className=\"transaction-row\"\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      width: '100%',\n                      height: `${virtualRow.size}px`,\n                      transform: `translateY(${virtualRow.start}px)`\n                    }}\n                  >\n                    <div className=\"cell\">{transaction.transaction_id || transaction.nameOrig}</div>\n                    <div className=\"cell\">{transaction.type}</div>\n                    <div className=\"cell\">{formatCurrency(transaction.amount)}</div>\n                    <div className=\"cell\">\n                      <span className={`risk-badge ${getRiskClass(transaction.risk_score)}`}>\n                        {(transaction.risk_score * 100).toFixed(1)}%\n                      </span>\n                    </div>\n                    <div className=\"cell\">{formatDate(transaction.timestamp)}</div>\n                    <div className=\"cell\">\n                      <button\n                        className=\"view-button\"\n                        onClick={() => onSelectTransaction(transaction)}\n                      >\n                        View\n                      </button>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {sortedTransactions.length === 0 && !isLoading && (\n            <div className=\"no-data\">No transactions found</div>\n          )}\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default TransactionTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAgB,OAAO;AAE1D,OAAO,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,YAAY;EAAEC,mBAAmB;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC7E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC;IAAEa,GAAG,EAAE,WAAW;IAAEC,SAAS,EAAE;EAAO,CAAC,CAAC;EACrF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAMiB,SAAS,GAAGlB,KAAK,CAACmB,MAAM,CAAC,CAAC;;EAEhC;EACAjB,SAAS,CAAC,MAAM;IACd,MAAMkB,MAAM,GAAG,CAAC,GAAGZ,YAAY,CAAC,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC9C,IAAID,CAAC,CAACV,UAAU,CAACE,GAAG,CAAC,GAAGS,CAAC,CAACX,UAAU,CAACE,GAAG,CAAC,EAAE;QACzC,OAAOF,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAChD;MACA,IAAIO,CAAC,CAACV,UAAU,CAACE,GAAG,CAAC,GAAGS,CAAC,CAACX,UAAU,CAACE,GAAG,CAAC,EAAE;QACzC,OAAOF,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAChD;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IAEFE,qBAAqB,CAACG,MAAM,CAAC;EAC/B,CAAC,EAAE,CAACZ,YAAY,EAAEI,UAAU,CAAC,CAAC;;EAE9B;EACA,MAAMY,cAAc,GAAGC,UAAU,CAAC;IAChCC,IAAI,EAAEV,kBAAkB,CAACW,MAAM;IAC/BT,SAAS;IACTU,YAAY,EAAE5B,KAAK,CAAC6B,WAAW,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC;IAC7CC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMC,UAAU,GAAIjB,GAAG,IAAK;IAC1BD,aAAa,CAACmB,UAAU,KAAK;MAC3BlB,GAAG;MACHC,SAAS,EAAEiB,UAAU,CAAClB,GAAG,KAAKA,GAAG,IAAIkB,UAAU,CAACjB,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG;IACjF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMkB,YAAY,GAAIC,SAAS,IAAK;IAClC,IAAIA,SAAS,IAAI,GAAG,EAAE,OAAO,WAAW;IACxC,IAAIA,SAAS,IAAI,GAAG,EAAE,OAAO,aAAa;IAC1C,OAAO,UAAU;EACnB,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAC1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,cAAc,CAAC,CAAC;EAC9B,CAAC;EAED,oBACE1C,OAAA;IAAK2C,SAAS,EAAC,2BAA2B;IAAAC,QAAA,EACvCtC,SAAS,gBACRN,OAAA;MAAK2C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAC;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,gBAEhEhD,OAAA,CAAAE,SAAA;MAAA0C,QAAA,gBACE5C,OAAA;QAAK2C,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvC5C,OAAA;UAAK2C,SAAS,EAAC,aAAa;UAACM,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,gBAAgB,CAAE;UAAAiB,QAAA,GAAC,KACrE,EAACpC,UAAU,CAACE,GAAG,KAAK,gBAAgB,KAAKF,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;QAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eACNhD,OAAA;UAAK2C,SAAS,EAAC,aAAa;UAACM,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,MAAM,CAAE;UAAAiB,QAAA,GAAC,OACzD,EAACpC,UAAU,CAACE,GAAG,KAAK,MAAM,KAAKF,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;QAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eACNhD,OAAA;UAAK2C,SAAS,EAAC,aAAa;UAACM,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,QAAQ,CAAE;UAAAiB,QAAA,GAAC,SACzD,EAACpC,UAAU,CAACE,GAAG,KAAK,QAAQ,KAAKF,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;QAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eACNhD,OAAA;UAAK2C,SAAS,EAAC,aAAa;UAACM,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,YAAY,CAAE;UAAAiB,QAAA,GAAC,aACzD,EAACpC,UAAU,CAACE,GAAG,KAAK,YAAY,KAAKF,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;QAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,eACNhD,OAAA;UAAK2C,SAAS,EAAC,aAAa;UAACM,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAAC,WAAW,CAAE;UAAAiB,QAAA,GAAC,YACzD,EAACpC,UAAU,CAACE,GAAG,KAAK,WAAW,KAAKF,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;QAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtF,CAAC,eACNhD,OAAA;UAAK2C,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAENhD,OAAA;QACEkD,GAAG,EAAEpC,SAAU;QACf6B,SAAS,EAAC,wBAAwB;QAClCR,KAAK,EAAE;UAAEgB,MAAM,EAAE,OAAO;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAR,QAAA,eAE7C5C,OAAA;UACEmC,KAAK,EAAE;YACLgB,MAAM,EAAE,GAAG/B,cAAc,CAACiC,SAAS,IAAI;YACvCC,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE;UACZ,CAAE;UAAAX,QAAA,EAEDxB,cAAc,CAACoC,YAAY,CAACC,GAAG,CAACC,UAAU,IAAI;YAC7C,MAAMC,WAAW,GAAG/C,kBAAkB,CAAC8C,UAAU,CAACE,KAAK,CAAC;YACxD,oBACE5D,OAAA;cAEE2C,SAAS,EAAC,iBAAiB;cAC3BR,KAAK,EAAE;gBACLoB,QAAQ,EAAE,UAAU;gBACpBM,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPR,KAAK,EAAE,MAAM;gBACbH,MAAM,EAAE,GAAGO,UAAU,CAACpC,IAAI,IAAI;gBAC9ByC,SAAS,EAAE,cAAcL,UAAU,CAACM,KAAK;cAC3C,CAAE;cAAApB,QAAA,gBAEF5C,OAAA;gBAAK2C,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEe,WAAW,CAACM,cAAc,IAAIN,WAAW,CAACO;cAAQ;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChFhD,OAAA;gBAAK2C,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEe,WAAW,CAACQ;cAAI;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9ChD,OAAA;gBAAK2C,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEb,cAAc,CAAC4B,WAAW,CAAC3B,MAAM;cAAC;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChEhD,OAAA;gBAAK2C,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnB5C,OAAA;kBAAM2C,SAAS,EAAE,cAAcd,YAAY,CAAC8B,WAAW,CAACS,UAAU,CAAC,EAAG;kBAAAxB,QAAA,GACnE,CAACe,WAAW,CAACS,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAC7C;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNhD,OAAA;gBAAK2C,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEN,UAAU,CAACqB,WAAW,CAACW,SAAS;cAAC;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/DhD,OAAA;gBAAK2C,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnB5C,OAAA;kBACE2C,SAAS,EAAC,aAAa;kBACvBM,OAAO,EAAEA,CAAA,KAAM5C,mBAAmB,CAACsD,WAAW,CAAE;kBAAAf,QAAA,EACjD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GA3BDU,UAAU,CAACE,KAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4BlB,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELpC,kBAAkB,CAACW,MAAM,KAAK,CAAC,IAAI,CAACjB,SAAS,iBAC5CN,OAAA;QAAK2C,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACpD;IAAA,eACD;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzC,EAAA,CAzIIJ,gBAAgB;AAAAoE,EAAA,GAAhBpE,gBAAgB;AA2ItB,eAAeA,gBAAgB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}