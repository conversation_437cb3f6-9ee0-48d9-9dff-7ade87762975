{"ast": null, "code": "import React, { forwardRef, useRef, useEffect } from 'react';\nimport { Chart as Chart$1, <PERSON><PERSON><PERSON><PERSON><PERSON>, Bar<PERSON><PERSON><PERSON><PERSON>, RadarController, DoughnutController, PolarAreaController, B<PERSON>bleController, Pie<PERSON><PERSON>roller, ScatterController } from 'chart.js';\nconst defaultDatasetIdKey = 'label';\nfunction reforwardRef(ref, value) {\n  if (typeof ref === 'function') {\n    ref(value);\n  } else if (ref) {\n    ref.current = value;\n  }\n}\nfunction setOptions(chart, nextOptions) {\n  const options = chart.options;\n  if (options && nextOptions) {\n    Object.assign(options, nextOptions);\n  }\n}\nfunction setLabels(currentData, nextLabels) {\n  currentData.labels = nextLabels;\n}\nfunction setDatasets(currentData, nextDatasets) {\n  let datasetIdKey = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : defaultDatasetIdKey;\n  const addedDatasets = [];\n  currentData.datasets = nextDatasets.map(nextDataset => {\n    // given the new set, find it's current match\n    const currentDataset = currentData.datasets.find(dataset => dataset[datasetIdKey] === nextDataset[datasetIdKey]);\n    // There is no original to update, so simply add new one\n    if (!currentDataset || !nextDataset.data || addedDatasets.includes(currentDataset)) {\n      return {\n        ...nextDataset\n      };\n    }\n    addedDatasets.push(currentDataset);\n    Object.assign(currentDataset, nextDataset);\n    return currentDataset;\n  });\n}\nfunction cloneData(data) {\n  let datasetIdKey = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : defaultDatasetIdKey;\n  const nextData = {\n    labels: [],\n    datasets: []\n  };\n  setLabels(nextData, data.labels);\n  setDatasets(nextData, data.datasets, datasetIdKey);\n  return nextData;\n}\n/**\n * Get dataset from mouse click event\n * @param chart - Chart.js instance\n * @param event - Mouse click event\n * @returns Dataset\n */\nfunction getDatasetAtEvent(chart, event) {\n  return chart.getElementsAtEventForMode(event.nativeEvent, 'dataset', {\n    intersect: true\n  }, false);\n}\n/**\n * Get single dataset element from mouse click event\n * @param chart - Chart.js instance\n * @param event - Mouse click event\n * @returns Dataset\n */\nfunction getElementAtEvent(chart, event) {\n  return chart.getElementsAtEventForMode(event.nativeEvent, 'nearest', {\n    intersect: true\n  }, false);\n}\n/**\n * Get all dataset elements from mouse click event\n * @param chart - Chart.js instance\n * @param event - Mouse click event\n * @returns Dataset\n */\nfunction getElementsAtEvent(chart, event) {\n  return chart.getElementsAtEventForMode(event.nativeEvent, 'index', {\n    intersect: true\n  }, false);\n}\nfunction ChartComponent(props, ref) {\n  const {\n    height = 150,\n    width = 300,\n    redraw = false,\n    datasetIdKey,\n    type,\n    data,\n    options,\n    plugins = [],\n    fallbackContent,\n    updateMode,\n    ...canvasProps\n  } = props;\n  const canvasRef = useRef(null);\n  const chartRef = useRef(null);\n  const renderChart = () => {\n    if (!canvasRef.current) return;\n    chartRef.current = new Chart$1(canvasRef.current, {\n      type,\n      data: cloneData(data, datasetIdKey),\n      options: options && {\n        ...options\n      },\n      plugins\n    });\n    reforwardRef(ref, chartRef.current);\n  };\n  const destroyChart = () => {\n    reforwardRef(ref, null);\n    if (chartRef.current) {\n      chartRef.current.destroy();\n      chartRef.current = null;\n    }\n  };\n  useEffect(() => {\n    if (!redraw && chartRef.current && options) {\n      setOptions(chartRef.current, options);\n    }\n  }, [redraw, options]);\n  useEffect(() => {\n    if (!redraw && chartRef.current) {\n      setLabels(chartRef.current.config.data, data.labels);\n    }\n  }, [redraw, data.labels]);\n  useEffect(() => {\n    if (!redraw && chartRef.current && data.datasets) {\n      setDatasets(chartRef.current.config.data, data.datasets, datasetIdKey);\n    }\n  }, [redraw, data.datasets]);\n  useEffect(() => {\n    if (!chartRef.current) return;\n    if (redraw) {\n      destroyChart();\n      setTimeout(renderChart);\n    } else {\n      chartRef.current.update(updateMode);\n    }\n  }, [redraw, options, data.labels, data.datasets, updateMode]);\n  useEffect(() => {\n    if (!chartRef.current) return;\n    destroyChart();\n    setTimeout(renderChart);\n  }, [type]);\n  useEffect(() => {\n    renderChart();\n    return () => destroyChart();\n  }, []);\n  return /*#__PURE__*/React.createElement(\"canvas\", {\n    ref: canvasRef,\n    role: \"img\",\n    height: height,\n    width: width,\n    ...canvasProps\n  }, fallbackContent);\n}\nconst Chart = /*#__PURE__*/forwardRef(ChartComponent);\nfunction createTypedChart(type, registerables) {\n  Chart$1.register(registerables);\n  return /*#__PURE__*/forwardRef((props, ref) => /*#__PURE__*/React.createElement(Chart, {\n    ...props,\n    ref: ref,\n    type: type\n  }));\n}\nconst Line = /* #__PURE__ */createTypedChart('line', LineController);\nconst Bar = /* #__PURE__ */createTypedChart('bar', BarController);\nconst Radar = /* #__PURE__ */createTypedChart('radar', RadarController);\nconst Doughnut = /* #__PURE__ */createTypedChart('doughnut', DoughnutController);\nconst PolarArea = /* #__PURE__ */createTypedChart('polarArea', PolarAreaController);\nconst Bubble = /* #__PURE__ */createTypedChart('bubble', BubbleController);\nconst Pie = /* #__PURE__ */createTypedChart('pie', PieController);\nconst Scatter = /* #__PURE__ */createTypedChart('scatter', ScatterController);\nexport { Bar, Bubble, Chart, Doughnut, Line, Pie, PolarArea, Radar, Scatter, getDatasetAtEvent, getElementAtEvent, getElementsAtEvent };\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}