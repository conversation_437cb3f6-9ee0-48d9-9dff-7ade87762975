{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\services\\\\WebSocketContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\n// Create WebSocket context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WebSocketContext = /*#__PURE__*/createContext(null);\n\n// WebSocket provider component\nexport const WebSocketProvider = ({\n  children\n}) => {\n  _s();\n  const [socket, setSocket] = useState(null);\n  const [connected, setConnected] = useState(false);\n  const [lastMessage, setLastMessage] = useState(null);\n  const [error, setError] = useState(null);\n\n  // Connect to WebSocket on mount\n  useEffect(() => {\n    const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:9000/ws/txns';\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket connected');\n      setConnected(true);\n      setError(null);\n    };\n    ws.onmessage = event => {\n      setLastMessage(event.data);\n    };\n    ws.onerror = event => {\n      console.error('WebSocket error:', event);\n      setError('WebSocket connection error');\n    };\n    ws.onclose = () => {\n      console.log('WebSocket disconnected');\n      setConnected(false);\n\n      // Attempt to reconnect after a delay\n      setTimeout(() => {\n        setSocket(null);\n      }, 5000);\n    };\n    setSocket(ws);\n\n    // Clean up on unmount\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Context value\n  const value = {\n    connected,\n    lastMessage,\n    error\n  };\n  return /*#__PURE__*/_jsxDEV(WebSocketContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook for using WebSocket context\n_s(WebSocketProvider, \"pZRZUxpVqfJ4KodsCC1vopC2Bpc=\");\n_c = WebSocketProvider;\nexport const useWebSocket = () => {\n  _s2();\n  const context = useContext(WebSocketContext);\n  if (!context) {\n    throw new Error('useWebSocket must be used within a WebSocketProvider');\n  }\n  return context;\n};\n_s2(useWebSocket, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"WebSocketProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "WebSocketContext", "WebSocketProvider", "children", "_s", "socket", "setSocket", "connected", "setConnected", "lastMessage", "setLastMessage", "error", "setError", "wsUrl", "process", "env", "REACT_APP_WS_URL", "ws", "WebSocket", "onopen", "console", "log", "onmessage", "event", "data", "onerror", "onclose", "setTimeout", "close", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useWebSocket", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/services/WebSocketContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\n// Create WebSocket context\nconst WebSocketContext = createContext(null);\n\n// WebSocket provider component\nexport const WebSocketProvider = ({ children }) => {\n  const [socket, setSocket] = useState(null);\n  const [connected, setConnected] = useState(false);\n  const [lastMessage, setLastMessage] = useState(null);\n  const [error, setError] = useState(null);\n\n  // Connect to WebSocket on mount\n  useEffect(() => {\n    const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:9000/ws/txns';\n    const ws = new WebSocket(wsUrl);\n\n    ws.onopen = () => {\n      console.log('WebSocket connected');\n      setConnected(true);\n      setError(null);\n    };\n\n    ws.onmessage = (event) => {\n      setLastMessage(event.data);\n    };\n\n    ws.onerror = (event) => {\n      console.error('WebSocket error:', event);\n      setError('WebSocket connection error');\n    };\n\n    ws.onclose = () => {\n      console.log('WebSocket disconnected');\n      setConnected(false);\n      \n      // Attempt to reconnect after a delay\n      setTimeout(() => {\n        setSocket(null);\n      }, 5000);\n    };\n\n    setSocket(ws);\n\n    // Clean up on unmount\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Context value\n  const value = {\n    connected,\n    lastMessage,\n    error\n  };\n\n  return (\n    <WebSocketContext.Provider value={value}>\n      {children}\n    </WebSocketContext.Provider>\n  );\n};\n\n// Custom hook for using WebSocket context\nexport const useWebSocket = () => {\n  const context = useContext(WebSocketContext);\n  if (!context) {\n    throw new Error('useWebSocket must be used within a WebSocketProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;;AAE7E;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,gBAAgB,gBAAGN,aAAa,CAAC,IAAI,CAAC;;AAE5C;AACA,OAAO,MAAMO,iBAAiB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACjD,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMe,KAAK,GAAGC,OAAO,CAACC,GAAG,CAACC,gBAAgB,IAAI,6BAA6B;IAC3E,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACL,KAAK,CAAC;IAE/BI,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClCb,YAAY,CAAC,IAAI,CAAC;MAClBI,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC;IAEDK,EAAE,CAACK,SAAS,GAAIC,KAAK,IAAK;MACxBb,cAAc,CAACa,KAAK,CAACC,IAAI,CAAC;IAC5B,CAAC;IAEDP,EAAE,CAACQ,OAAO,GAAIF,KAAK,IAAK;MACtBH,OAAO,CAACT,KAAK,CAAC,kBAAkB,EAAEY,KAAK,CAAC;MACxCX,QAAQ,CAAC,4BAA4B,CAAC;IACxC,CAAC;IAEDK,EAAE,CAACS,OAAO,GAAG,MAAM;MACjBN,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrCb,YAAY,CAAC,KAAK,CAAC;;MAEnB;MACAmB,UAAU,CAAC,MAAM;QACfrB,SAAS,CAAC,IAAI,CAAC;MACjB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IAEDA,SAAS,CAACW,EAAE,CAAC;;IAEb;IACA,OAAO,MAAM;MACXA,EAAE,CAACW,KAAK,CAAC,CAAC;IACZ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,KAAK,GAAG;IACZtB,SAAS;IACTE,WAAW;IACXE;EACF,CAAC;EAED,oBACEX,OAAA,CAACC,gBAAgB,CAAC6B,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA1B,QAAA,EACrCA;EAAQ;IAAA4B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAEhC,CAAC;;AAED;AAAA9B,EAAA,CA1DaF,iBAAiB;AAAAiC,EAAA,GAAjBjC,iBAAiB;AA2D9B,OAAO,MAAMkC,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAChC,MAAMC,OAAO,GAAG1C,UAAU,CAACK,gBAAgB,CAAC;EAC5C,IAAI,CAACqC,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,sDAAsD,CAAC;EACzE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,YAAY;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}