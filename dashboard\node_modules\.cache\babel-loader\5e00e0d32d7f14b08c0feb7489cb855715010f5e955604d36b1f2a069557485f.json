{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './services/AuthContext';\nimport { WebSocketProvider } from './services/WebSocketContext';\nimport ErrorBoundary, { setupGlobalErrorHandlers } from './components/ErrorBoundary';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Navbar from './components/Navbar';\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport CaseManagement from './pages/CaseManagement';\nimport Analytics from './pages/Analytics';\nimport Unauthorized from './pages/Unauthorized';\nimport { UserRole } from './types';\nimport './App.css';\n\n// Setup global error handlers\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nsetupGlobalErrorHandlers();\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(WebSocketProvider, {\n        children: /*#__PURE__*/_jsxDEV(Router, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"app\",\n            children: /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/login\",\n                element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 27,\n                  columnNumber: 47\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/unauthorized\",\n                element: /*#__PURE__*/_jsxDEV(Unauthorized, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 28,\n                  columnNumber: 54\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"app-container\",\n                    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 33,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"content\",\n                      children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 35,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 34,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 32,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/cases\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"app-container\",\n                    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 44,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"content\",\n                      children: /*#__PURE__*/_jsxDEV(CaseManagement, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 46,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 45,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 43,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 42,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/analytics\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requiredRole: UserRole.ANALYST,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"app-container\",\n                    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 55,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"content\",\n                      children: /*#__PURE__*/_jsxDEV(Analytics, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 57,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 56,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 54,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"*\",\n                element: /*#__PURE__*/_jsxDEV(Navigate, {\n                  to: \"/\",\n                  replace: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "WebSocketProvider", "Error<PERSON>ou<PERSON><PERSON>", "setupGlobalErrorHandlers", "ProtectedRoute", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Dashboard", "CaseManagement", "Analytics", "Unauthorized", "UserRole", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "requiredRole", "ANALYST", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './services/AuthContext';\nimport { WebSocketProvider } from './services/WebSocketContext';\nimport ErrorBoundary, { setupGlobalErrorHandlers } from './components/ErrorBoundary';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Navbar from './components/Navbar';\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport CaseManagement from './pages/CaseManagement';\nimport Analytics from './pages/Analytics';\nimport Unauthorized from './pages/Unauthorized';\nimport { UserRole } from './types';\nimport './App.css';\n\n// Setup global error handlers\nsetupGlobalErrorHandlers();\n\nconst App: React.FC = () => {\n  return (\n    <ErrorBoundary>\n      <AuthProvider>\n        <WebSocketProvider>\n          <Router>\n            <div className=\"app\">\n              <Routes>\n                <Route path=\"/login\" element={<Login />} />\n                <Route path=\"/unauthorized\" element={<Unauthorized />} />\n\n                <Route path=\"/\" element={\n                  <ProtectedRoute>\n                    <div className=\"app-container\">\n                      <Navbar />\n                      <div className=\"content\">\n                        <Dashboard />\n                      </div>\n                    </div>\n                  </ProtectedRoute>\n                } />\n\n                <Route path=\"/cases\" element={\n                  <ProtectedRoute>\n                    <div className=\"app-container\">\n                      <Navbar />\n                      <div className=\"content\">\n                        <CaseManagement />\n                      </div>\n                    </div>\n                  </ProtectedRoute>\n                } />\n\n                <Route path=\"/analytics\" element={\n                  <ProtectedRoute requiredRole={UserRole.ANALYST}>\n                    <div className=\"app-container\">\n                      <Navbar />\n                      <div className=\"content\">\n                        <Analytics />\n                      </div>\n                    </div>\n                  </ProtectedRoute>\n                } />\n\n                {/* Catch all route */}\n                <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n              </Routes>\n            </div>\n          </Router>\n        </WebSocketProvider>\n      </AuthProvider>\n    </ErrorBoundary>\n  );\n};\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,OAAOC,aAAa,IAAIC,wBAAwB,QAAQ,4BAA4B;AACpF,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,SAASC,QAAQ,QAAQ,SAAS;AAClC,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAV,wBAAwB,CAAC,CAAC;AAE1B,MAAMW,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACED,OAAA,CAACX,aAAa;IAAAa,QAAA,eACZF,OAAA,CAACb,YAAY;MAAAe,QAAA,eACXF,OAAA,CAACZ,iBAAiB;QAAAc,QAAA,eAChBF,OAAA,CAACjB,MAAM;UAAAmB,QAAA,eACLF,OAAA;YAAKG,SAAS,EAAC,KAAK;YAAAD,QAAA,eAClBF,OAAA,CAAChB,MAAM;cAAAkB,QAAA,gBACLF,OAAA,CAACf,KAAK;gBAACmB,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAEL,OAAA,CAACP,KAAK;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3CT,OAAA,CAACf,KAAK;gBAACmB,IAAI,EAAC,eAAe;gBAACC,OAAO,eAAEL,OAAA,CAACH,YAAY;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEzDT,OAAA,CAACf,KAAK;gBAACmB,IAAI,EAAC,GAAG;gBAACC,OAAO,eACrBL,OAAA,CAACT,cAAc;kBAAAW,QAAA,eACbF,OAAA;oBAAKG,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5BF,OAAA,CAACR,MAAM;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACVT,OAAA;sBAAKG,SAAS,EAAC,SAAS;sBAAAD,QAAA,eACtBF,OAAA,CAACN,SAAS;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEJT,OAAA,CAACf,KAAK;gBAACmB,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAC1BL,OAAA,CAACT,cAAc;kBAAAW,QAAA,eACbF,OAAA;oBAAKG,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5BF,OAAA,CAACR,MAAM;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACVT,OAAA;sBAAKG,SAAS,EAAC,SAAS;sBAAAD,QAAA,eACtBF,OAAA,CAACL,cAAc;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEJT,OAAA,CAACf,KAAK;gBAACmB,IAAI,EAAC,YAAY;gBAACC,OAAO,eAC9BL,OAAA,CAACT,cAAc;kBAACmB,YAAY,EAAEZ,QAAQ,CAACa,OAAQ;kBAAAT,QAAA,eAC7CF,OAAA;oBAAKG,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5BF,OAAA,CAACR,MAAM;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACVT,OAAA;sBAAKG,SAAS,EAAC,SAAS;sBAAAD,QAAA,eACtBF,OAAA,CAACJ,SAAS;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAGJT,OAAA,CAACf,KAAK;gBAACmB,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAEL,OAAA,CAACd,QAAQ;kBAAC0B,EAAE,EAAC,GAAG;kBAACC,OAAO;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB,CAAC;AAACK,EAAA,GArDIb,GAAa;AAuDnB,eAAeA,GAAG;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}