/**
 * Utility functions for formatting data display
 */

/**
 * Format currency values
 */
export const formatCurrency = (
  amount: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

/**
 * Format large numbers with abbreviations (K, M, B)
 */
export const formatNumber = (num: number, decimals: number = 1): string => {
  if (num === 0) return '0';
  
  const k = 1000;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['', 'K', 'M', 'B', 'T'];
  
  const i = Math.floor(Math.log(Math.abs(num)) / Math.log(k));
  
  if (i === 0) {
    return num.toString();
  }
  
  return parseFloat((num / Math.pow(k, i)).toFixed(dm)) + sizes[i];
};

/**
 * Format percentage values
 */
export const formatPercentage = (
  value: number,
  decimals: number = 1,
  includeSign: boolean = true
): string => {
  const formatted = (value * 100).toFixed(decimals);
  return includeSign ? `${formatted}%` : formatted;
};

/**
 * Format date and time values
 */
export const formatDate = (
  date: string | Date,
  options: Intl.DateTimeFormatOptions = {}
): string => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date';
  }
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  };
  
  return dateObj.toLocaleDateString('en-US', { ...defaultOptions, ...options });
};

/**
 * Format relative time (e.g., "2 hours ago")
 */
export const formatRelativeTime = (date: string | Date): string => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return 'Just now';
  }
  
  const intervals = [
    { label: 'year', seconds: 31536000 },
    { label: 'month', seconds: 2592000 },
    { label: 'day', seconds: 86400 },
    { label: 'hour', seconds: 3600 },
    { label: 'minute', seconds: 60 }
  ];
  
  for (const interval of intervals) {
    const count = Math.floor(diffInSeconds / interval.seconds);
    if (count > 0) {
      return `${count} ${interval.label}${count > 1 ? 's' : ''} ago`;
    }
  }
  
  return 'Just now';
};

/**
 * Format file sizes
 */
export const formatFileSize = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Format transaction types for display
 */
export const formatTransactionType = (type: string): string => {
  const typeMap: Record<string, string> = {
    'PAYMENT': 'Payment',
    'TRANSFER': 'Transfer',
    'CASH_OUT': 'Cash Out',
    'DEBIT': 'Debit',
    'CASH_IN': 'Cash In'
  };
  
  return typeMap[type] || type;
};

/**
 * Format risk scores with color coding
 */
export const formatRiskScore = (score: number | undefined): {
  formatted: string;
  level: 'low' | 'medium' | 'high';
  color: string;
} => {
  if (score === undefined || score === null) {
    return {
      formatted: 'N/A',
      level: 'low',
      color: '#6b7280'
    };
  }
  
  const percentage = (score * 100).toFixed(1);
  
  if (score >= 0.8) {
    return {
      formatted: `${percentage}%`,
      level: 'high',
      color: '#dc2626'
    };
  } else if (score >= 0.5) {
    return {
      formatted: `${percentage}%`,
      level: 'medium',
      color: '#d97706'
    };
  } else {
    return {
      formatted: `${percentage}%`,
      level: 'low',
      color: '#059669'
    };
  }
};

/**
 * Format case status for display
 */
export const formatCaseStatus = (status: string): {
  formatted: string;
  color: string;
  bgColor: string;
} => {
  const statusMap: Record<string, { formatted: string; color: string; bgColor: string }> = {
    'open': {
      formatted: 'Open',
      color: '#dc2626',
      bgColor: '#fef2f2'
    },
    'closed': {
      formatted: 'Closed',
      color: '#059669',
      bgColor: '#f0fdf4'
    },
    'pending': {
      formatted: 'Pending',
      color: '#d97706',
      bgColor: '#fffbeb'
    }
  };
  
  return statusMap[status.toLowerCase()] || {
    formatted: status,
    color: '#6b7280',
    bgColor: '#f9fafb'
  };
};

/**
 * Format case tags for display
 */
export const formatCaseTag = (tag: string): {
  formatted: string;
  color: string;
  bgColor: string;
} => {
  const tagMap: Record<string, { formatted: string; color: string; bgColor: string }> = {
    'FP': {
      formatted: 'False Positive',
      color: '#059669',
      bgColor: '#f0fdf4'
    },
    'CONFIRMED': {
      formatted: 'Confirmed Fraud',
      color: '#dc2626',
      bgColor: '#fef2f2'
    },
    'SUSPICIOUS': {
      formatted: 'Suspicious',
      color: '#d97706',
      bgColor: '#fffbeb'
    },
    'NEEDS_REVIEW': {
      formatted: 'Needs Review',
      color: '#7c3aed',
      bgColor: '#faf5ff'
    }
  };
  
  return tagMap[tag] || {
    formatted: tag,
    color: '#6b7280',
    bgColor: '#f9fafb'
  };
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (!text || text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength - 3) + '...';
};

/**
 * Format phone numbers
 */
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return '';
  
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Format as (XXX) XXX-XXXX for US numbers
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  }
  
  // Return original if not a standard format
  return phone;
};

/**
 * Format account numbers (mask sensitive information)
 */
export const formatAccountNumber = (accountNumber: string, showLast: number = 4): string => {
  if (!accountNumber) return '';
  
  if (accountNumber.length <= showLast) {
    return accountNumber;
  }
  
  const masked = '*'.repeat(accountNumber.length - showLast);
  const visible = accountNumber.slice(-showLast);
  
  return masked + visible;
};

/**
 * Format duration in milliseconds to human readable format
 */
export const formatDuration = (milliseconds: number): string => {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  }
  
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};

/**
 * Format API response time
 */
export const formatResponseTime = (milliseconds: number): string => {
  if (milliseconds < 1000) {
    return `${milliseconds.toFixed(0)}ms`;
  } else {
    return `${(milliseconds / 1000).toFixed(2)}s`;
  }
};
