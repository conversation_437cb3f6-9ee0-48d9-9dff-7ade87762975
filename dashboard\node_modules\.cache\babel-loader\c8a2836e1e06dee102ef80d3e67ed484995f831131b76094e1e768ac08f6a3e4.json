{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\pages\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport TransactionTable from '../components/TransactionTable';\nimport RiskHeatmap from '../components/RiskHeatmap';\nimport CaseDrawer from '../components/CaseDrawer';\nimport { useWebSocket } from '../services/WebSocketContext';\nimport { fetchLatestTransactions } from '../services/api';\nimport '../styles/Dashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [transactions, setTransactions] = useState([]);\n  const [selectedTransaction, setSelectedTransaction] = useState(null);\n  const [isDrawerOpen, setIsDrawerOpen] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const {\n    lastMessage\n  } = useWebSocket();\n  const transactionsRef = useRef([]);\n  useEffect(() => {\n    // Load initial transactions\n    const loadTransactions = async () => {\n      try {\n        setIsLoading(true);\n        const data = await fetchLatestTransactions(100);\n        setTransactions(data);\n        transactionsRef.current = data;\n      } catch (error) {\n        console.error('Error fetching transactions:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    loadTransactions();\n  }, []);\n  useEffect(() => {\n    // Handle new transactions from WebSocket\n    if (lastMessage) {\n      try {\n        const messageData = JSON.parse(lastMessage);\n        if (messageData.transaction && messageData.risk_score !== undefined) {\n          const newTransaction = {\n            ...messageData.transaction,\n            risk_score: messageData.risk_score,\n            timestamp: messageData.timestamp\n          };\n\n          // Add to beginning of array and limit size\n          const updatedTransactions = [newTransaction, ...transactionsRef.current].slice(0, 1000);\n          setTransactions(updatedTransactions);\n          transactionsRef.current = updatedTransactions;\n        }\n      } catch (error) {\n        console.error('Error processing WebSocket message:', error);\n      }\n    }\n  }, [lastMessage]);\n  const handleTransactionSelect = transaction => {\n    setSelectedTransaction(transaction);\n    setIsDrawerOpen(true);\n  };\n  const handleDrawerClose = () => {\n    setIsDrawerOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Fraud Detection Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Total Transactions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: transactions.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"High Risk\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: transactions.filter(t => t.risk_score >= 0.8).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Medium Risk\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: transactions.filter(t => t.risk_score >= 0.5 && t.risk_score < 0.8).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-main\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card transaction-table-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Live Transaction Feed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TransactionTable, {\n            transactions: transactions,\n            onSelectTransaction: handleTransactionSelect,\n            isLoading: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-sidebar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Risk Heatmap\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(RiskHeatmap, {\n            transactions: transactions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CaseDrawer, {\n      isOpen: isDrawerOpen,\n      onClose: handleDrawerClose,\n      transaction: selectedTransaction\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"cSEcGETdQr/sDS0/tjveUMqZcoM=\", false, function () {\n  return [useWebSocket];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "TransactionTable", "RiskHeatmap", "CaseDrawer", "useWebSocket", "fetchLatestTransactions", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "transactions", "setTransactions", "selectedTransaction", "setSelectedTransaction", "isDrawerOpen", "setIsDrawerOpen", "isLoading", "setIsLoading", "lastMessage", "transactionsRef", "loadTransactions", "data", "current", "error", "console", "messageData", "JSON", "parse", "transaction", "risk_score", "undefined", "newTransaction", "timestamp", "updatedTransactions", "slice", "handleTransactionSelect", "handleDrawerClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "filter", "t", "onSelectTransaction", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport TransactionTable from '../components/TransactionTable';\nimport RiskHeatmap from '../components/RiskHeatmap';\nimport CaseDrawer from '../components/CaseDrawer';\nimport { useWebSocket } from '../services/WebSocketContext';\nimport { fetchLatestTransactions } from '../services/api';\nimport '../styles/Dashboard.css';\n\nconst Dashboard = () => {\n  const [transactions, setTransactions] = useState([]);\n  const [selectedTransaction, setSelectedTransaction] = useState(null);\n  const [isDrawerOpen, setIsDrawerOpen] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const { lastMessage } = useWebSocket();\n  const transactionsRef = useRef([]);\n\n  useEffect(() => {\n    // Load initial transactions\n    const loadTransactions = async () => {\n      try {\n        setIsLoading(true);\n        const data = await fetchLatestTransactions(100);\n        setTransactions(data);\n        transactionsRef.current = data;\n      } catch (error) {\n        console.error('Error fetching transactions:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadTransactions();\n  }, []);\n\n  useEffect(() => {\n    // Handle new transactions from WebSocket\n    if (lastMessage) {\n      try {\n        const messageData = JSON.parse(lastMessage);\n        if (messageData.transaction && messageData.risk_score !== undefined) {\n          const newTransaction = {\n            ...messageData.transaction,\n            risk_score: messageData.risk_score,\n            timestamp: messageData.timestamp\n          };\n          \n          // Add to beginning of array and limit size\n          const updatedTransactions = [newTransaction, ...transactionsRef.current].slice(0, 1000);\n          setTransactions(updatedTransactions);\n          transactionsRef.current = updatedTransactions;\n        }\n      } catch (error) {\n        console.error('Error processing WebSocket message:', error);\n      }\n    }\n  }, [lastMessage]);\n\n  const handleTransactionSelect = (transaction) => {\n    setSelectedTransaction(transaction);\n    setIsDrawerOpen(true);\n  };\n\n  const handleDrawerClose = () => {\n    setIsDrawerOpen(false);\n  };\n\n  return (\n    <div className=\"dashboard-container\">\n      <div className=\"dashboard-header\">\n        <h1>Fraud Detection Dashboard</h1>\n        <div className=\"dashboard-stats\">\n          <div className=\"stat-card\">\n            <h3>Total Transactions</h3>\n            <p>{transactions.length}</p>\n          </div>\n          <div className=\"stat-card\">\n            <h3>High Risk</h3>\n            <p>{transactions.filter(t => t.risk_score >= 0.8).length}</p>\n          </div>\n          <div className=\"stat-card\">\n            <h3>Medium Risk</h3>\n            <p>{transactions.filter(t => t.risk_score >= 0.5 && t.risk_score < 0.8).length}</p>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"dashboard-content\">\n        <div className=\"dashboard-main\">\n          <div className=\"card transaction-table-card\">\n            <h2>Live Transaction Feed</h2>\n            <TransactionTable \n              transactions={transactions} \n              onSelectTransaction={handleTransactionSelect}\n              isLoading={isLoading}\n            />\n          </div>\n        </div>\n        \n        <div className=\"dashboard-sidebar\">\n          <div className=\"card\">\n            <h2>Risk Heatmap</h2>\n            <RiskHeatmap transactions={transactions} />\n          </div>\n        </div>\n      </div>\n\n      <CaseDrawer \n        isOpen={isDrawerOpen} \n        onClose={handleDrawerClose}\n        transaction={selectedTransaction}\n      />\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,uBAAuB,QAAQ,iBAAiB;AACzD,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACc,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM;IAAEoB;EAAY,CAAC,GAAGd,YAAY,CAAC,CAAC;EACtC,MAAMe,eAAe,GAAGnB,MAAM,CAAC,EAAE,CAAC;EAElCD,SAAS,CAAC,MAAM;IACd;IACA,MAAMqB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACFH,YAAY,CAAC,IAAI,CAAC;QAClB,MAAMI,IAAI,GAAG,MAAMhB,uBAAuB,CAAC,GAAG,CAAC;QAC/CM,eAAe,CAACU,IAAI,CAAC;QACrBF,eAAe,CAACG,OAAO,GAAGD,IAAI;MAChC,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,SAAS;QACRN,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDG,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAENrB,SAAS,CAAC,MAAM;IACd;IACA,IAAImB,WAAW,EAAE;MACf,IAAI;QACF,MAAMO,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACT,WAAW,CAAC;QAC3C,IAAIO,WAAW,CAACG,WAAW,IAAIH,WAAW,CAACI,UAAU,KAAKC,SAAS,EAAE;UACnE,MAAMC,cAAc,GAAG;YACrB,GAAGN,WAAW,CAACG,WAAW;YAC1BC,UAAU,EAAEJ,WAAW,CAACI,UAAU;YAClCG,SAAS,EAAEP,WAAW,CAACO;UACzB,CAAC;;UAED;UACA,MAAMC,mBAAmB,GAAG,CAACF,cAAc,EAAE,GAAGZ,eAAe,CAACG,OAAO,CAAC,CAACY,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC;UACvFvB,eAAe,CAACsB,mBAAmB,CAAC;UACpCd,eAAe,CAACG,OAAO,GAAGW,mBAAmB;QAC/C;MACF,CAAC,CAAC,OAAOV,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC7D;IACF;EACF,CAAC,EAAE,CAACL,WAAW,CAAC,CAAC;EAEjB,MAAMiB,uBAAuB,GAAIP,WAAW,IAAK;IAC/Cf,sBAAsB,CAACe,WAAW,CAAC;IACnCb,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMqB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BrB,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,oBACER,OAAA;IAAK8B,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClC/B,OAAA;MAAK8B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B/B,OAAA;QAAA+B,QAAA,EAAI;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClCnC,OAAA;QAAK8B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B/B,OAAA;UAAK8B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/B,OAAA;YAAA+B,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BnC,OAAA;YAAA+B,QAAA,EAAI5B,YAAY,CAACiC;UAAM;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACNnC,OAAA;UAAK8B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/B,OAAA;YAAA+B,QAAA,EAAI;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClBnC,OAAA;YAAA+B,QAAA,EAAI5B,YAAY,CAACkC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,UAAU,IAAI,GAAG,CAAC,CAACc;UAAM;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACNnC,OAAA;UAAK8B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB/B,OAAA;YAAA+B,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBnC,OAAA;YAAA+B,QAAA,EAAI5B,YAAY,CAACkC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,UAAU,IAAI,GAAG,IAAIgB,CAAC,CAAChB,UAAU,GAAG,GAAG,CAAC,CAACc;UAAM;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnC,OAAA;MAAK8B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC/B,OAAA;QAAK8B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B/B,OAAA;UAAK8B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C/B,OAAA;YAAA+B,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BnC,OAAA,CAACN,gBAAgB;YACfS,YAAY,EAAEA,YAAa;YAC3BoC,mBAAmB,EAAEX,uBAAwB;YAC7CnB,SAAS,EAAEA;UAAU;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnC,OAAA;QAAK8B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChC/B,OAAA;UAAK8B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB/B,OAAA;YAAA+B,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBnC,OAAA,CAACL,WAAW;YAACQ,YAAY,EAAEA;UAAa;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnC,OAAA,CAACJ,UAAU;MACT4C,MAAM,EAAEjC,YAAa;MACrBkC,OAAO,EAAEZ,iBAAkB;MAC3BR,WAAW,EAAEhB;IAAoB;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACjC,EAAA,CAzGID,SAAS;EAAA,QAKWJ,YAAY;AAAA;AAAA6C,EAAA,GALhCzC,SAAS;AA2Gf,eAAeA,SAAS;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}