"""
Database models for case management.
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, Enum, ForeignKey, Text, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
import datetime
import enum

Base = declarative_base()

class UserRole(enum.Enum):
    """User role enum"""
    ANALYST = "analyst"
    AUDITOR = "auditor"
    ADMIN = "admin"

class CaseStatus(enum.Enum):
    """Case status enum"""
    OPEN = "open"
    CLOSED = "closed"
    PENDING = "pending"

class CaseTag(enum.Enum):
    """Case tag enum"""
    FALSE_POSITIVE = "FP"
    CONFIRMED = "CONFIRMED"
    SUSPICIOUS = "SUSPICIOUS"
    NEEDS_REVIEW = "NEEDS_REVIEW"

class User(Base):
    """User model"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    role = Column(Enum(UserRole), nullable=False, default=UserRole.ANALYST)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)

    # Relationships
    cases = relationship("Case", back_populates="user")

class Case(Base):
    """Case model"""
    __tablename__ = "cases"

    id = Column(Integer, primary_key=True)
    transaction_id = Column(String(100), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    tag = Column(Enum(CaseTag), nullable=False)
    comment = Column(Text, nullable=True)
    status = Column(Enum(CaseStatus), nullable=False, default=CaseStatus.OPEN)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="cases")
    audit_logs = relationship("AuditLog", back_populates="case")

class AuditLog(Base):
    """Audit log model"""
    __tablename__ = "audit_logs"

    id = Column(Integer, primary_key=True)
    case_id = Column(Integer, ForeignKey("cases.id"), nullable=False)
    action = Column(String(50), nullable=False)
    action_metadata = Column(JSON, nullable=True)
    timestamp = Column(DateTime, default=datetime.datetime.utcnow)

    # Relationships
    case = relationship("Case", back_populates="audit_logs")

class Transaction(Base):
    """Transaction model for storing scored transactions"""
    __tablename__ = "transactions"

    id = Column(Integer, primary_key=True)
    transaction_id = Column(String(100), nullable=False, unique=True, index=True)
    step = Column(Integer, nullable=False)
    type = Column(String(20), nullable=False)
    amount = Column(Float, nullable=False)
    name_orig = Column(String(100), nullable=False)
    old_balance_orig = Column(Float, nullable=False)
    new_balance_orig = Column(Float, nullable=False)
    name_dest = Column(String(100), nullable=False)
    old_balance_dest = Column(Float, nullable=False)
    new_balance_dest = Column(Float, nullable=False)
    risk_score = Column(Float, nullable=False)
    is_fraud = Column(Integer, nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
