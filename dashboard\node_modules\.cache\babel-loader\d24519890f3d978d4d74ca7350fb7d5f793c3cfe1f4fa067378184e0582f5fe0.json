{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\services\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { loginUser } from './api';\nimport { UserRole } from '../types';\n\n// Create auth context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(null);\n// Auth provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing auth on mount\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const token = localStorage.getItem('token');\n    if (storedUser && token) {\n      try {\n        const parsedUser = JSON.parse(storedUser);\n        setUser(parsedUser);\n      } catch (error) {\n        console.error('Error parsing stored user:', error);\n        localStorage.removeItem('user');\n        localStorage.removeItem('token');\n      }\n    }\n    setLoading(false);\n  }, []);\n\n  // Login function\n  const login = async (username, password) => {\n    try {\n      const response = await loginUser(username, password);\n      const {\n        access_token\n      } = response;\n\n      // For demo purposes, create a mock user object\n      // In production, you would decode the JWT or make a separate request\n      const mockUser = {\n        id: 1,\n        username,\n        role: username.includes('admin') ? UserRole.ADMIN : UserRole.ANALYST,\n        created_at: new Date().toISOString()\n      };\n\n      // Store auth data\n      localStorage.setItem('token', access_token);\n      localStorage.setItem('user', JSON.stringify(mockUser));\n      setUser(mockUser);\n      return true;\n    } catch (error) {\n      console.error('Login failed:', error);\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n  };\n\n  // Context value\n  const value = {\n    user,\n    login,\n    logout,\n    isAuthenticated: !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: !loading && children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook for using auth context\n_s(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "loginUser", "UserRole", "jsxDEV", "_jsxDEV", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "loading", "setLoading", "storedUser", "localStorage", "getItem", "token", "parsedUser", "JSON", "parse", "error", "console", "removeItem", "login", "username", "password", "response", "access_token", "mockUser", "id", "role", "includes", "ADMIN", "ANALYST", "created_at", "Date", "toISOString", "setItem", "stringify", "logout", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/services/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { loginUser } from './api';\nimport { User, UserRole, AuthContextType } from '../types';\n\n// Create auth context\nconst AuthContext = createContext<AuthContextType | null>(null);\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\n// Auth provider component\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState<boolean>(true);\n\n  // Check for existing auth on mount\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const token = localStorage.getItem('token');\n\n    if (storedUser && token) {\n      try {\n        const parsedUser = JSON.parse(storedUser) as User;\n        setUser(parsedUser);\n      } catch (error) {\n        console.error('Error parsing stored user:', error);\n        localStorage.removeItem('user');\n        localStorage.removeItem('token');\n      }\n    }\n\n    setLoading(false);\n  }, []);\n\n  // Login function\n  const login = async (username: string, password: string): Promise<boolean> => {\n    try {\n      const response = await loginUser(username, password);\n      const { access_token } = response;\n\n      // For demo purposes, create a mock user object\n      // In production, you would decode the JWT or make a separate request\n      const mockUser: User = {\n        id: 1,\n        username,\n        role: username.includes('admin') ? UserRole.ADMIN : UserRole.ANALYST,\n        created_at: new Date().toISOString()\n      };\n\n      // Store auth data\n      localStorage.setItem('token', access_token);\n      localStorage.setItem('user', JSON.stringify(mockUser));\n\n      setUser(mockUser);\n      return true;\n    } catch (error) {\n      console.error('Login failed:', error);\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = (): void => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n  };\n\n  // Context value\n  const value: AuthContextType = {\n    user,\n    login,\n    logout,\n    isAuthenticated: !!user\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {!loading && children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook for using auth context\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AACxF,SAASC,SAAS,QAAQ,OAAO;AACjC,SAAeC,QAAQ,QAAyB,UAAU;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,gBAAGR,aAAa,CAAyB,IAAI,CAAC;AAM/D;AACA,OAAO,MAAMS,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGX,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAU,IAAI,CAAC;;EAErD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMa,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,MAAMC,KAAK,GAAGF,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAIF,UAAU,IAAIG,KAAK,EAAE;MACvB,IAAI;QACF,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACN,UAAU,CAAS;QACjDH,OAAO,CAACO,UAAU,CAAC;MACrB,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDN,YAAY,CAACQ,UAAU,CAAC,MAAM,CAAC;QAC/BR,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;MAClC;IACF;IAEAV,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,KAAK,GAAG,MAAAA,CAAOC,QAAgB,EAAEC,QAAgB,KAAuB;IAC5E,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMzB,SAAS,CAACuB,QAAQ,EAAEC,QAAQ,CAAC;MACpD,MAAM;QAAEE;MAAa,CAAC,GAAGD,QAAQ;;MAEjC;MACA;MACA,MAAME,QAAc,GAAG;QACrBC,EAAE,EAAE,CAAC;QACLL,QAAQ;QACRM,IAAI,EAAEN,QAAQ,CAACO,QAAQ,CAAC,OAAO,CAAC,GAAG7B,QAAQ,CAAC8B,KAAK,GAAG9B,QAAQ,CAAC+B,OAAO;QACpEC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC;;MAED;MACAtB,YAAY,CAACuB,OAAO,CAAC,OAAO,EAAEV,YAAY,CAAC;MAC3Cb,YAAY,CAACuB,OAAO,CAAC,MAAM,EAAEnB,IAAI,CAACoB,SAAS,CAACV,QAAQ,CAAC,CAAC;MAEtDlB,OAAO,CAACkB,QAAQ,CAAC;MACjB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMmB,MAAM,GAAGA,CAAA,KAAY;IACzBzB,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;IAChCR,YAAY,CAACQ,UAAU,CAAC,MAAM,CAAC;IAC/BZ,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;;EAED;EACA,MAAM8B,KAAsB,GAAG;IAC7B/B,IAAI;IACJc,KAAK;IACLgB,MAAM;IACNE,eAAe,EAAE,CAAC,CAAChC;EACrB,CAAC;EAED,oBACEL,OAAA,CAACC,WAAW,CAACqC,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAjC,QAAA,EAChC,CAACI,OAAO,IAAIJ;EAAQ;IAAAoC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAE3B,CAAC;;AAED;AAAAtC,EAAA,CAxEaF,YAAyC;AAAAyC,EAAA,GAAzCzC,YAAyC;AAyEtD,OAAO,MAAM0C,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAGpD,UAAU,CAACO,WAAW,CAAC;EACvC,IAAI,CAAC6C,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}