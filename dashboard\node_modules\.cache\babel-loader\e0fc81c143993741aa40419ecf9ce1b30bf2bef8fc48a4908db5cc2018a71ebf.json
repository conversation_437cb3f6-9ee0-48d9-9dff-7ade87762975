{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\pages\\\\Login.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../services/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport '../styles/Login.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setIsLoading(true);\n    try {\n      await login(username, password);\n      navigate('/');\n    } catch (err) {\n      setError('Invalid username or password');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Fraud Detection Platform\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"username\",\n            children: \"Username\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"username\",\n            value: username,\n            onChange: e => setUsername(e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"password\",\n            value: password,\n            onChange: e => setPassword(e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"login-button\",\n          disabled: isLoading,\n          children: isLoading ? 'Logging in...' : 'Login'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"A0fCS3cLHjkRUvrmkD1rBp0dYDM=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "useNavigate", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "username", "setUsername", "password", "setPassword", "error", "setError", "isLoading", "setIsLoading", "login", "navigate", "handleSubmit", "e", "preventDefault", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "value", "onChange", "target", "required", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/pages/Login.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../services/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport '../styles/Login.css';\n\nconst Login = () => {\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const { login } = useAuth();\n  const navigate = useNavigate();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setIsLoading(true);\n\n    try {\n      await login(username, password);\n      navigate('/');\n    } catch (err) {\n      setError('Invalid username or password');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"login-container\">\n      <div className=\"login-card\">\n        <div className=\"login-header\">\n          <h1>Fraud Detection Platform</h1>\n          <h2>Login</h2>\n        </div>\n        {error && <div className=\"error-message\">{error}</div>}\n        <form onSubmit={handleSubmit}>\n          <div className=\"form-group\">\n            <label htmlFor=\"username\">Username</label>\n            <input\n              type=\"text\"\n              id=\"username\"\n              value={username}\n              onChange={(e) => setUsername(e.target.value)}\n              required\n            />\n          </div>\n          <div className=\"form-group\">\n            <label htmlFor=\"password\">Password</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              required\n            />\n          </div>\n          <button type=\"submit\" className=\"login-button\" disabled={isLoading}>\n            {isLoading ? 'Logging in...' : 'Login'}\n          </button>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAEe;EAAM,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC3B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,MAAMe,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBP,QAAQ,CAAC,EAAE,CAAC;IACZE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMC,KAAK,CAACR,QAAQ,EAAEE,QAAQ,CAAC;MAC/BO,QAAQ,CAAC,GAAG,CAAC;IACf,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZR,QAAQ,CAAC,8BAA8B,CAAC;IAC1C,CAAC,SAAS;MACRE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEV,OAAA;IAAKiB,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BlB,OAAA;MAAKiB,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBlB,OAAA;QAAKiB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BlB,OAAA;UAAAkB,QAAA,EAAI;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjCtB,OAAA;UAAAkB,QAAA,EAAI;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,EACLf,KAAK,iBAAIP,OAAA;QAAKiB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEX;MAAK;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtDtB,OAAA;QAAMuB,QAAQ,EAAEV,YAAa;QAAAK,QAAA,gBAC3BlB,OAAA;UAAKiB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBlB,OAAA;YAAOwB,OAAO,EAAC,UAAU;YAAAN,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1CtB,OAAA;YACEyB,IAAI,EAAC,MAAM;YACXC,EAAE,EAAC,UAAU;YACbC,KAAK,EAAExB,QAAS;YAChByB,QAAQ,EAAGd,CAAC,IAAKV,WAAW,CAACU,CAAC,CAACe,MAAM,CAACF,KAAK,CAAE;YAC7CG,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtB,OAAA;UAAKiB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBlB,OAAA;YAAOwB,OAAO,EAAC,UAAU;YAAAN,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1CtB,OAAA;YACEyB,IAAI,EAAC,UAAU;YACfC,EAAE,EAAC,UAAU;YACbC,KAAK,EAAEtB,QAAS;YAChBuB,QAAQ,EAAGd,CAAC,IAAKR,WAAW,CAACQ,CAAC,CAACe,MAAM,CAACF,KAAK,CAAE;YAC7CG,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtB,OAAA;UAAQyB,IAAI,EAAC,QAAQ;UAACR,SAAS,EAAC,cAAc;UAACc,QAAQ,EAAEtB,SAAU;UAAAS,QAAA,EAChET,SAAS,GAAG,eAAe,GAAG;QAAO;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpB,EAAA,CA3DID,KAAK;EAAA,QAKSJ,OAAO,EACRC,WAAW;AAAA;AAAAkC,EAAA,GANxB/B,KAAK;AA6DX,eAAeA,KAAK;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}