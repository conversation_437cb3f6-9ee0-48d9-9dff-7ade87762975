import axios, { AxiosResponse, AxiosError } from 'axios';

// Type definitions
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
}

export interface ApiError {
  message: string;
  status: number;
  details?: any;
}

// API base URL
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 10000
});

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError) => {
    const apiError: ApiError = {
      message: error.message || 'An error occurred',
      status: error.response?.status || 500,
      details: error.response?.data
    };

    // Handle authentication errors
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }

    return Promise.reject(apiError);
  }
);

// Authentication API
export const loginUser = async (username: string, password: string): Promise<LoginResponse> => {
  const formData = new FormData();
  formData.append('username', username);
  formData.append('password', password);

  const response = await api.post<LoginResponse>('/auth/login', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
  return response.data;
};

// Import types
import {
  Transaction,
  Case,
  CaseCreate,
  CaseUpdate,
  CaseFilter,
  CaseStats,
  FeatureImportance,
  FraudTrendData,
  ModelInfo,
  HealthStatus,
  ServiceStats,
  PaginatedResponse
} from '../types';

// Transaction API
export const fetchLatestTransactions = async (limit: number = 50): Promise<Transaction[]> => {
  const response = await api.get<Transaction[]>(`/transactions/latest?n=${limit}`);
  return response.data;
};

export const fetchTransaction = async (id: string): Promise<Transaction> => {
  const response = await api.get<Transaction>(`/transactions/${id}`);
  return response.data;
};

// Case Management API
export const fetchCases = async (filter: CaseFilter = {}): Promise<Case[]> => {
  let url = '/cases';
  const params = new URLSearchParams();

  if (filter.status && filter.status !== 'all') {
    params.append('status', filter.status);
  }

  if (filter.transaction_id) {
    params.append('transaction_id', filter.transaction_id);
  }

  if (filter.tag) {
    params.append('tag', filter.tag);
  }

  if (filter.user_id) {
    params.append('user_id', filter.user_id.toString());
  }

  if (filter.date_from) {
    params.append('date_from', filter.date_from);
  }

  if (filter.date_to) {
    params.append('date_to', filter.date_to);
  }

  if (params.toString()) {
    url += `?${params.toString()}`;
  }

  const response = await api.get<Case[]>(url);
  return response.data;
};

export const fetchCase = async (id: number): Promise<Case> => {
  const response = await api.get<Case>(`/cases/${id}`);
  return response.data;
};

export const createCase = async (caseData: CaseCreate): Promise<Case> => {
  const response = await api.post<Case>('/cases', caseData);
  return response.data;
};

export const updateCase = async (id: number, caseData: CaseUpdate): Promise<Case> => {
  const response = await api.put<Case>(`/cases/${id}`, caseData);
  return response.data;
};

export const deleteCase = async (id: number): Promise<void> => {
  await api.delete(`/cases/${id}`);
};

// Analytics API
export const fetchCaseStats = async (): Promise<CaseStats> => {
  const response = await api.get<CaseStats>('/cases/stats');
  return response.data;
};

export const fetchFeatureImportance = async (): Promise<FeatureImportance[]> => {
  try {
    const response = await api.get<FeatureImportance[]>('/feature-importance');
    return response.data;
  } catch (error) {
    // Return mock data if endpoint is not available
    return [
      { feature: 'type_TRANSFER', importance: 0.35 },
      { feature: 'amount', importance: 0.25 },
      { feature: 'oldbalanceOrg', importance: 0.15 },
      { feature: 'newbalanceOrig', importance: 0.10 },
      { feature: 'oldbalanceDest', importance: 0.08 },
      { feature: 'newbalanceDest', importance: 0.07 }
    ];
  }
};

export const fetchFraudTrend = async (dateRange: string = 'week'): Promise<FraudTrendData[]> => {
  try {
    const response = await api.get<FraudTrendData[]>(`/fraud-trend?range=${dateRange}`);
    return response.data;
  } catch (error) {
    // Return mock data if endpoint is not available
    return generateMockTrendData(dateRange);
  }
};

// Model and Health API
export const fetchModelInfo = async (): Promise<ModelInfo> => {
  const response = await api.get<ModelInfo>('/model/info');
  return response.data;
};

export const fetchHealthStatus = async (): Promise<HealthStatus> => {
  const response = await api.get<HealthStatus>('/health');
  return response.data;
};

export const fetchServiceStats = async (): Promise<ServiceStats> => {
  const response = await api.get<ServiceStats>('/stats');
  return response.data;
};

// Helper function to generate mock trend data for demo purposes
const generateMockTrendData = (dateRange: string): FraudTrendData[] => {
  const now = new Date();
  const data: FraudTrendData[] = [];
  let days: number;

  switch (dateRange) {
    case 'day':
      days = 1;
      break;
    case 'week':
      days = 7;
      break;
    case 'month':
      days = 30;
      break;
    case 'year':
      days = 365;
      break;
    default:
      days = 7;
  }

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);

    data.push({
      date: date.toISOString().split('T')[0],
      count: Math.floor(Math.random() * 10) + 1
    });
  }

  return data;
};

export default api;
