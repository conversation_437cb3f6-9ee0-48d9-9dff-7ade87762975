# Fraud Detection Platform - Frontend

A comprehensive TypeScript React frontend for the fraud detection platform, featuring real-time transaction monitoring, case management, and analytics.

## 🚀 Features

- **Real-time Transaction Monitoring**: Live WebSocket updates for incoming transactions
- **Risk Assessment**: Visual risk scoring and fraud detection alerts
- **Case Management**: Create, update, and track fraud investigation cases
- **Analytics Dashboard**: Interactive charts and fraud trend analysis
- **Role-based Access Control**: Different access levels for analysts, auditors, and administrators
- **Responsive Design**: Mobile-friendly interface with modern UI components
- **Type Safety**: Full TypeScript implementation with comprehensive type definitions

## 🛠 Technology Stack

- **React 18** with TypeScript
- **React Router** for navigation
- **WebSocket** for real-time updates
- **Axios** for API communication
- **React Virtual** for performance optimization
- **Chart.js** for data visualization
- **Tailwind CSS** for styling
- **Jest & React Testing Library** for testing

## 📁 Project Structure

```
dashboard/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── ErrorBoundary.tsx
│   │   ├── Loading.tsx
│   │   ├── ProtectedRoute.tsx
│   │   └── TransactionTable.tsx
│   ├── hooks/              # Custom React hooks
│   │   └── useApi.ts
│   ├── pages/              # Page components
│   │   ├── Dashboard.tsx
│   │   ├── Login.tsx
│   │   ├── CaseManagement.tsx
│   │   ├── Analytics.tsx
│   │   └── Unauthorized.tsx
│   ├── services/           # API and context providers
│   │   ├── api.ts
│   │   ├── AuthContext.tsx
│   │   └── WebSocketContext.tsx
│   ├── types/              # TypeScript type definitions
│   │   └── index.ts
│   ├── utils/              # Utility functions
│   │   ├── formatters.ts
│   │   ├── storage.ts
│   │   └── validation.ts
│   ├── styles/             # CSS stylesheets
│   └── __tests__/          # Test files
├── public/
├── package.json
├── tsconfig.json
└── README.md
```

## 🔧 Installation & Setup

### Prerequisites

- Node.js 16+ and npm/yarn
- Backend API running on port 8000
- WebSocket service running on port 9000

### Installation

1. **Install dependencies**:
   ```bash
   cd dashboard
   npm install
   ```

2. **Environment Configuration**:
   Create a `.env` file in the dashboard directory:
   ```env
   REACT_APP_API_URL=http://localhost:8000
   REACT_APP_WS_URL=ws://localhost:9000/ws/txns
   ```

3. **Start the development server**:
   ```bash
   npm start
   ```

4. **Access the application**:
   Open [http://localhost:3000](http://localhost:3000) in your browser

## 🔐 Authentication

### Demo Credentials

- **Analyst**: `username: analyst`, `password: password`
- **Admin**: `username: admin`, `password: password`

### Role-based Access

- **Analyst**: Access to dashboard, cases, and analytics
- **Auditor**: Access to dashboard and cases (read-only)
- **Admin**: Full access to all features

## 📊 Key Components

### Dashboard
- Real-time transaction monitoring
- Risk score visualization
- Transaction filtering and search
- Live statistics and alerts

### Case Management
- Create fraud investigation cases
- Update case status and tags
- Add comments and notes
- Filter and search cases

### Analytics
- Fraud trend analysis
- Feature importance charts
- Model performance metrics
- Statistical insights

## 🔌 API Integration

The frontend integrates with the backend through:

- **REST API**: CRUD operations for cases, transactions, and analytics
- **WebSocket**: Real-time transaction updates
- **Authentication**: JWT-based authentication system

### API Endpoints

```typescript
// Authentication
POST /auth/login

// Transactions
GET /transactions/latest?n={limit}
GET /transactions/{id}

// Cases
GET /cases
POST /cases
PUT /cases/{id}
DELETE /cases/{id}
GET /cases/stats

// Analytics
GET /feature-importance
GET /fraud-trend?range={period}
GET /model/info
GET /health
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Test Coverage

The test suite includes:
- Component rendering tests
- User interaction tests
- API integration tests
- TypeScript type safety tests
- Error handling tests
- Accessibility tests

## 🎨 Styling & Theming

The application uses a combination of:
- **Tailwind CSS** for utility-first styling
- **Custom CSS** for component-specific styles
- **CSS Variables** for theming support

### Color Scheme

- **Primary**: Blue (#3B82F6)
- **Success**: Green (#10B981)
- **Warning**: Orange (#F59E0B)
- **Danger**: Red (#EF4444)
- **Info**: Indigo (#6366F1)

## 🔧 Configuration

### TypeScript Configuration

The project uses strict TypeScript settings:
- Strict null checks
- No implicit any
- Unused locals detection
- Exact optional property types

### Build Configuration

```bash
# Development build
npm run build:dev

# Production build
npm run build

# Analyze bundle size
npm run analyze
```

## 📱 Responsive Design

The application is fully responsive with breakpoints:
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🚀 Performance Optimizations

- **Virtual Scrolling**: For large transaction lists
- **Code Splitting**: Lazy loading of route components
- **Memoization**: React.memo and useMemo for expensive operations
- **Bundle Optimization**: Tree shaking and minification

## 🔒 Security Features

- **XSS Protection**: Input sanitization and validation
- **CSRF Protection**: Token-based authentication
- **Role-based Access**: Protected routes and components
- **Secure Storage**: Encrypted local storage for sensitive data

## 🐛 Error Handling

- **Error Boundaries**: Catch and handle React errors
- **API Error Handling**: Graceful degradation for API failures
- **Validation**: Client-side form validation
- **Logging**: Error reporting and debugging

## 📈 Monitoring & Analytics

- **Performance Monitoring**: Core Web Vitals tracking
- **Error Tracking**: Automatic error reporting
- **User Analytics**: Usage patterns and behavior
- **API Monitoring**: Request/response tracking

## 🤝 Contributing

1. Follow TypeScript best practices
2. Write comprehensive tests for new features
3. Use proper type definitions
4. Follow the existing code style
5. Update documentation as needed

## 📄 License

This project is part of the fraud detection platform and follows the same licensing terms.

## 🆘 Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**:
   - Check if the WebSocket service is running
   - Verify the WebSocket URL in environment variables

2. **API Connection Issues**:
   - Ensure the backend API is running
   - Check CORS configuration
   - Verify API URL in environment variables

3. **Authentication Problems**:
   - Clear browser storage and try again
   - Check if the backend authentication service is working

4. **Build Errors**:
   - Clear node_modules and reinstall dependencies
   - Check TypeScript configuration
   - Verify all imports and exports

### Getting Help

- Check the browser console for error messages
- Review the network tab for API request failures
- Check the backend logs for server-side issues
- Refer to the component documentation and type definitions
