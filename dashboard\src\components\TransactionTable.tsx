import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { Transaction, SortConfig } from '../types';
import '../styles/TransactionTable.css';

interface TransactionTableProps {
  transactions: Transaction[];
  onSelectTransaction: (transaction: Transaction) => void;
  isLoading: boolean;
}

const TransactionTable: React.FC<TransactionTableProps> = ({
  transactions,
  onSelectTransaction,
  isLoading
}) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: 'timestamp',
    direction: 'desc'
  });
  const [sortedTransactions, setSortedTransactions] = useState<Transaction[]>([]);

  const parentRef = useRef<HTMLDivElement>(null);

  // Apply sorting
  useEffect(() => {
    const sorted = [...transactions].sort((a, b) => {
      const aValue = a[sortConfig.key as keyof Transaction];
      const bValue = b[sortConfig.key as keyof Transaction];

      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return 1;
      if (bValue == null) return -1;

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });

    setSortedTransactions(sorted);
  }, [transactions, sortConfig]);

  // Set up virtualization
  const rowVirtualizer = useVirtualizer({
    count: sortedTransactions.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 60,
    overscan: 10
  });

  const handleSort = useCallback((key: keyof Transaction) => {
    setSortConfig(prevConfig => ({
      key: key as string,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  }, []);

  const getRiskClass = useCallback((riskScore: number | undefined): string => {
    if (!riskScore) return 'low-risk';
    if (riskScore >= 0.8) return 'high-risk';
    if (riskScore >= 0.5) return 'medium-risk';
    return 'low-risk';
  }, []);

  const formatCurrency = useCallback((amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }, []);

  const formatDate = useCallback((dateString: string | undefined): string => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString();
  }, []);

  return (
    <div className="transaction-table-wrapper">
      {isLoading ? (
        <div className="loading-indicator">Loading transactions...</div>
      ) : (
        <>
          <div className="transaction-table-header">
            <div className="header-cell" onClick={() => handleSort('transaction_id')}>
              ID {sortConfig.key === 'transaction_id' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
            </div>
            <div className="header-cell" onClick={() => handleSort('type')}>
              Type {sortConfig.key === 'type' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
            </div>
            <div className="header-cell" onClick={() => handleSort('amount')}>
              Amount {sortConfig.key === 'amount' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
            </div>
            <div className="header-cell" onClick={() => handleSort('risk_score')}>
              Risk Score {sortConfig.key === 'risk_score' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
            </div>
            <div className="header-cell" onClick={() => handleSort('timestamp')}>
              Timestamp {sortConfig.key === 'timestamp' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
            </div>
            <div className="header-cell">Actions</div>
          </div>

          <div
            ref={parentRef}
            className="transaction-table-body"
            style={{ height: '500px', overflow: 'auto' }}
          >
            <div
              style={{
                height: `${rowVirtualizer.getTotalSize()}px`,
                width: '100%',
                position: 'relative'
              }}
            >
              {rowVirtualizer.getVirtualItems().map(virtualRow => {
                const transaction = sortedTransactions[virtualRow.index];
                return (
                  <div
                    key={virtualRow.index}
                    className="transaction-row"
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: `${virtualRow.size}px`,
                      transform: `translateY(${virtualRow.start}px)`
                    }}
                  >
                    <div className="cell">{transaction.transaction_id || transaction.nameOrig}</div>
                    <div className="cell">{transaction.type}</div>
                    <div className="cell">{formatCurrency(transaction.amount)}</div>
                    <div className="cell">
                      <span className={`risk-badge ${getRiskClass(transaction.risk_score)}`}>
                        {((transaction.risk_score || 0) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="cell">{formatDate(transaction.timestamp)}</div>
                    <div className="cell">
                      <button
                        className="view-button"
                        onClick={() => onSelectTransaction(transaction)}
                      >
                        View
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {sortedTransactions.length === 0 && !isLoading && (
            <div className="no-data">No transactions found</div>
          )}
        </>
      )}
    </div>
  );
};

export default TransactionTable;
