{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\components\\\\CaseDrawer.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport '../styles/CaseDrawer.css';\nimport { createCase } from '../services/api';\nimport { useAuth } from '../services/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CaseDrawer = ({\n  isOpen,\n  onClose,\n  transaction\n}) => {\n  _s();\n  const [tag, setTag] = useState('NEEDS_REVIEW');\n  const [comment, setComment] = useState('');\n  const [status, setStatus] = useState('open');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const {\n    user\n  } = useAuth();\n  if (!isOpen || !transaction) {\n    return null;\n  }\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n    setIsSubmitting(true);\n    try {\n      await createCase({\n        transaction_id: transaction.transaction_id || transaction.nameOrig,\n        tag,\n        comment,\n        status\n      });\n      setSuccess('Case created successfully');\n      setComment('');\n      setTimeout(() => {\n        onClose();\n        setSuccess('');\n      }, 2000);\n    } catch (err) {\n      setError('Failed to create case. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  const getRiskClass = riskScore => {\n    if (riskScore >= 0.8) return 'high-risk';\n    if (riskScore >= 0.5) return 'medium-risk';\n    return 'low-risk';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `case-drawer ${isOpen ? 'open' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"drawer-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Transaction Details\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"close-button\",\n        onClick: onClose,\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"drawer-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transaction-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Basic Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Transaction ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value\",\n              children: transaction.transaction_id || transaction.nameOrig\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Type:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value\",\n              children: transaction.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Amount:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value\",\n              children: formatCurrency(transaction.amount)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Risk Score:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `detail-value risk-badge ${getRiskClass(transaction.risk_score)}`,\n              children: [(transaction.risk_score * 100).toFixed(1), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Account Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Origin Account:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value\",\n              children: transaction.nameOrig\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Origin Old Balance:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value\",\n              children: formatCurrency(transaction.oldbalanceOrg)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Origin New Balance:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value\",\n              children: formatCurrency(transaction.newbalanceOrig)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Destination Account:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value\",\n              children: transaction.nameDest\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Destination Old Balance:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value\",\n              children: formatCurrency(transaction.oldbalanceDest)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Destination New Balance:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value\",\n              children: formatCurrency(transaction.newbalanceDest)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), transaction.merchantFlag !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Additional Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Merchant Flag:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value\",\n              children: transaction.merchantFlag ? 'Yes' : 'No'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this), transaction.balanceDiffOrig !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Origin Balance Diff:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value\",\n              children: formatCurrency(transaction.balanceDiffOrig)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 17\n          }, this), transaction.balanceDiffDest !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Dest Balance Diff:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value\",\n              children: formatCurrency(transaction.balanceDiffDest)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), user && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"case-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Create Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 23\n        }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-message\",\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"tag\",\n              children: \"Tag:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"tag\",\n              value: tag,\n              onChange: e => setTag(e.target.value),\n              disabled: isSubmitting,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"NEEDS_REVIEW\",\n                children: \"Needs Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"SUSPICIOUS\",\n                children: \"Suspicious\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"CONFIRMED\",\n                children: \"Confirmed Fraud\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"FP\",\n                children: \"False Positive\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"status\",\n              children: \"Status:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"status\",\n              value: status,\n              onChange: e => setStatus(e.target.value),\n              disabled: isSubmitting,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"open\",\n                children: \"Open\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"pending\",\n                children: \"Pending\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"closed\",\n                children: \"Closed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"comment\",\n              children: \"Comment:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"comment\",\n              value: comment,\n              onChange: e => setComment(e.target.value),\n              disabled: isSubmitting,\n              rows: 4\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"submit-button\",\n            disabled: isSubmitting,\n            children: isSubmitting ? 'Creating...' : 'Create Case'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(CaseDrawer, \"Uwu4OAXTQYGO2xZJyOxCOkS/6hc=\", false, function () {\n  return [useAuth];\n});\n_c = CaseDrawer;\nexport default CaseDrawer;\nvar _c;\n$RefreshReg$(_c, \"CaseDrawer\");", "map": {"version": 3, "names": ["React", "useState", "createCase", "useAuth", "jsxDEV", "_jsxDEV", "CaseDrawer", "isOpen", "onClose", "transaction", "_s", "tag", "setTag", "comment", "setComment", "status", "setStatus", "isSubmitting", "setIsSubmitting", "error", "setError", "success", "setSuccess", "user", "handleSubmit", "e", "preventDefault", "transaction_id", "name<PERSON><PERSON>", "setTimeout", "err", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getRiskClass", "riskScore", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "risk_score", "toFixed", "oldbalanceOrg", "newbalanceOrig", "nameDest", "oldbalanceDest", "newbalanceDest", "merchantFlag", "undefined", "balanceDiffOrig", "balanceDiffDest", "onSubmit", "htmlFor", "id", "value", "onChange", "target", "disabled", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/components/CaseDrawer.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport '../styles/CaseDrawer.css';\nimport { createCase } from '../services/api';\nimport { useAuth } from '../services/AuthContext';\n\nconst CaseDrawer = ({ isOpen, onClose, transaction }) => {\n  const [tag, setTag] = useState('NEEDS_REVIEW');\n  const [comment, setComment] = useState('');\n  const [status, setStatus] = useState('open');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const { user } = useAuth();\n\n  if (!isOpen || !transaction) {\n    return null;\n  }\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n    setIsSubmitting(true);\n\n    try {\n      await createCase({\n        transaction_id: transaction.transaction_id || transaction.nameOrig,\n        tag,\n        comment,\n        status\n      });\n      setSuccess('Case created successfully');\n      setComment('');\n      setTimeout(() => {\n        onClose();\n        setSuccess('');\n      }, 2000);\n    } catch (err) {\n      setError('Failed to create case. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n\n  const getRiskClass = (riskScore) => {\n    if (riskScore >= 0.8) return 'high-risk';\n    if (riskScore >= 0.5) return 'medium-risk';\n    return 'low-risk';\n  };\n\n  return (\n    <div className={`case-drawer ${isOpen ? 'open' : ''}`}>\n      <div className=\"drawer-header\">\n        <h2>Transaction Details</h2>\n        <button className=\"close-button\" onClick={onClose}>×</button>\n      </div>\n\n      <div className=\"drawer-content\">\n        <div className=\"transaction-details\">\n          <div className=\"detail-group\">\n            <h3>Basic Information</h3>\n            <div className=\"detail-row\">\n              <span className=\"detail-label\">Transaction ID:</span>\n              <span className=\"detail-value\">{transaction.transaction_id || transaction.nameOrig}</span>\n            </div>\n            <div className=\"detail-row\">\n              <span className=\"detail-label\">Type:</span>\n              <span className=\"detail-value\">{transaction.type}</span>\n            </div>\n            <div className=\"detail-row\">\n              <span className=\"detail-label\">Amount:</span>\n              <span className=\"detail-value\">{formatCurrency(transaction.amount)}</span>\n            </div>\n            <div className=\"detail-row\">\n              <span className=\"detail-label\">Risk Score:</span>\n              <span className={`detail-value risk-badge ${getRiskClass(transaction.risk_score)}`}>\n                {(transaction.risk_score * 100).toFixed(1)}%\n              </span>\n            </div>\n          </div>\n\n          <div className=\"detail-group\">\n            <h3>Account Information</h3>\n            <div className=\"detail-row\">\n              <span className=\"detail-label\">Origin Account:</span>\n              <span className=\"detail-value\">{transaction.nameOrig}</span>\n            </div>\n            <div className=\"detail-row\">\n              <span className=\"detail-label\">Origin Old Balance:</span>\n              <span className=\"detail-value\">{formatCurrency(transaction.oldbalanceOrg)}</span>\n            </div>\n            <div className=\"detail-row\">\n              <span className=\"detail-label\">Origin New Balance:</span>\n              <span className=\"detail-value\">{formatCurrency(transaction.newbalanceOrig)}</span>\n            </div>\n            <div className=\"detail-row\">\n              <span className=\"detail-label\">Destination Account:</span>\n              <span className=\"detail-value\">{transaction.nameDest}</span>\n            </div>\n            <div className=\"detail-row\">\n              <span className=\"detail-label\">Destination Old Balance:</span>\n              <span className=\"detail-value\">{formatCurrency(transaction.oldbalanceDest)}</span>\n            </div>\n            <div className=\"detail-row\">\n              <span className=\"detail-label\">Destination New Balance:</span>\n              <span className=\"detail-value\">{formatCurrency(transaction.newbalanceDest)}</span>\n            </div>\n          </div>\n\n          {transaction.merchantFlag !== undefined && (\n            <div className=\"detail-group\">\n              <h3>Additional Features</h3>\n              <div className=\"detail-row\">\n                <span className=\"detail-label\">Merchant Flag:</span>\n                <span className=\"detail-value\">{transaction.merchantFlag ? 'Yes' : 'No'}</span>\n              </div>\n              {transaction.balanceDiffOrig !== undefined && (\n                <div className=\"detail-row\">\n                  <span className=\"detail-label\">Origin Balance Diff:</span>\n                  <span className=\"detail-value\">{formatCurrency(transaction.balanceDiffOrig)}</span>\n                </div>\n              )}\n              {transaction.balanceDiffDest !== undefined && (\n                <div className=\"detail-row\">\n                  <span className=\"detail-label\">Dest Balance Diff:</span>\n                  <span className=\"detail-value\">{formatCurrency(transaction.balanceDiffDest)}</span>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n\n        {user && (\n          <div className=\"case-form\">\n            <h3>Create Case</h3>\n            {error && <div className=\"error-message\">{error}</div>}\n            {success && <div className=\"success-message\">{success}</div>}\n            <form onSubmit={handleSubmit}>\n              <div className=\"form-group\">\n                <label htmlFor=\"tag\">Tag:</label>\n                <select \n                  id=\"tag\" \n                  value={tag} \n                  onChange={(e) => setTag(e.target.value)}\n                  disabled={isSubmitting}\n                >\n                  <option value=\"NEEDS_REVIEW\">Needs Review</option>\n                  <option value=\"SUSPICIOUS\">Suspicious</option>\n                  <option value=\"CONFIRMED\">Confirmed Fraud</option>\n                  <option value=\"FP\">False Positive</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label htmlFor=\"status\">Status:</label>\n                <select \n                  id=\"status\" \n                  value={status} \n                  onChange={(e) => setStatus(e.target.value)}\n                  disabled={isSubmitting}\n                >\n                  <option value=\"open\">Open</option>\n                  <option value=\"pending\">Pending</option>\n                  <option value=\"closed\">Closed</option>\n                </select>\n              </div>\n              <div className=\"form-group\">\n                <label htmlFor=\"comment\">Comment:</label>\n                <textarea \n                  id=\"comment\" \n                  value={comment} \n                  onChange={(e) => setComment(e.target.value)}\n                  disabled={isSubmitting}\n                  rows={4}\n                ></textarea>\n              </div>\n              <button \n                type=\"submit\" \n                className=\"submit-button\"\n                disabled={isSubmitting}\n              >\n                {isSubmitting ? 'Creating...' : 'Create Case'}\n              </button>\n            </form>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CaseDrawer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,0BAA0B;AACjC,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,UAAU,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACvD,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAGX,QAAQ,CAAC,cAAc,CAAC;EAC9C,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACc,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAC,MAAM,CAAC;EAC5C,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM;IAAEsB;EAAK,CAAC,GAAGpB,OAAO,CAAC,CAAC;EAE1B,IAAI,CAACI,MAAM,IAAI,CAACE,WAAW,EAAE;IAC3B,OAAO,IAAI;EACb;EAEA,MAAMe,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBN,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IACdJ,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF,MAAMhB,UAAU,CAAC;QACfyB,cAAc,EAAElB,WAAW,CAACkB,cAAc,IAAIlB,WAAW,CAACmB,QAAQ;QAClEjB,GAAG;QACHE,OAAO;QACPE;MACF,CAAC,CAAC;MACFO,UAAU,CAAC,2BAA2B,CAAC;MACvCR,UAAU,CAAC,EAAE,CAAC;MACde,UAAU,CAAC,MAAM;QACfrB,OAAO,CAAC,CAAC;QACTc,UAAU,CAAC,EAAE,CAAC;MAChB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZV,QAAQ,CAAC,0CAA0C,CAAC;IACtD,CAAC,SAAS;MACRF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMa,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,YAAY,GAAIC,SAAS,IAAK;IAClC,IAAIA,SAAS,IAAI,GAAG,EAAE,OAAO,WAAW;IACxC,IAAIA,SAAS,IAAI,GAAG,EAAE,OAAO,aAAa;IAC1C,OAAO,UAAU;EACnB,CAAC;EAED,oBACElC,OAAA;IAAKmC,SAAS,EAAE,eAAejC,MAAM,GAAG,MAAM,GAAG,EAAE,EAAG;IAAAkC,QAAA,gBACpDpC,OAAA;MAAKmC,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BpC,OAAA;QAAAoC,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BxC,OAAA;QAAQmC,SAAS,EAAC,cAAc;QAACM,OAAO,EAAEtC,OAAQ;QAAAiC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eAENxC,OAAA;MAAKmC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BpC,OAAA;QAAKmC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClCpC,OAAA;UAAKmC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpC,OAAA;YAAAoC,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BxC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrDxC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEhC,WAAW,CAACkB,cAAc,IAAIlB,WAAW,CAACmB;YAAQ;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3CxC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEhC,WAAW,CAACsC;YAAI;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CxC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEV,cAAc,CAACtB,WAAW,CAACuB,MAAM;YAAC;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjDxC,OAAA;cAAMmC,SAAS,EAAE,2BAA2BF,YAAY,CAAC7B,WAAW,CAACuC,UAAU,CAAC,EAAG;cAAAP,QAAA,GAChF,CAAChC,WAAW,CAACuC,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAC7C;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxC,OAAA;UAAKmC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpC,OAAA;YAAAoC,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BxC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrDxC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEhC,WAAW,CAACmB;YAAQ;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzDxC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEV,cAAc,CAACtB,WAAW,CAACyC,aAAa;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzDxC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEV,cAAc,CAACtB,WAAW,CAAC0C,cAAc;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DxC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEhC,WAAW,CAAC2C;YAAQ;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9DxC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEV,cAAc,CAACtB,WAAW,CAAC4C,cAAc;YAAC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9DxC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEV,cAAc,CAACtB,WAAW,CAAC6C,cAAc;YAAC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELpC,WAAW,CAAC8C,YAAY,KAAKC,SAAS,iBACrCnD,OAAA;UAAKmC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BpC,OAAA;YAAAoC,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BxC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDxC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEhC,WAAW,CAAC8C,YAAY,GAAG,KAAK,GAAG;YAAI;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC,EACLpC,WAAW,CAACgD,eAAe,KAAKD,SAAS,iBACxCnD,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DxC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEV,cAAc,CAACtB,WAAW,CAACgD,eAAe;YAAC;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CACN,EACApC,WAAW,CAACiD,eAAe,KAAKF,SAAS,iBACxCnD,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDxC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEV,cAAc,CAACtB,WAAW,CAACiD,eAAe;YAAC;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAELtB,IAAI,iBACHlB,OAAA;QAAKmC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpC,OAAA;UAAAoC,QAAA,EAAI;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACnB1B,KAAK,iBAAId,OAAA;UAAKmC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEtB;QAAK;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACrDxB,OAAO,iBAAIhB,OAAA;UAAKmC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAEpB;QAAO;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5DxC,OAAA;UAAMsD,QAAQ,EAAEnC,YAAa;UAAAiB,QAAA,gBAC3BpC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAOuD,OAAO,EAAC,KAAK;cAAAnB,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjCxC,OAAA;cACEwD,EAAE,EAAC,KAAK;cACRC,KAAK,EAAEnD,GAAI;cACXoD,QAAQ,EAAGtC,CAAC,IAAKb,MAAM,CAACa,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;cACxCG,QAAQ,EAAEhD,YAAa;cAAAwB,QAAA,gBAEvBpC,OAAA;gBAAQyD,KAAK,EAAC,cAAc;gBAAArB,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClDxC,OAAA;gBAAQyD,KAAK,EAAC,YAAY;gBAAArB,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9CxC,OAAA;gBAAQyD,KAAK,EAAC,WAAW;gBAAArB,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClDxC,OAAA;gBAAQyD,KAAK,EAAC,IAAI;gBAAArB,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAOuD,OAAO,EAAC,QAAQ;cAAAnB,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvCxC,OAAA;cACEwD,EAAE,EAAC,QAAQ;cACXC,KAAK,EAAE/C,MAAO;cACdgD,QAAQ,EAAGtC,CAAC,IAAKT,SAAS,CAACS,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;cAC3CG,QAAQ,EAAEhD,YAAa;cAAAwB,QAAA,gBAEvBpC,OAAA;gBAAQyD,KAAK,EAAC,MAAM;gBAAArB,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCxC,OAAA;gBAAQyD,KAAK,EAAC,SAAS;gBAAArB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCxC,OAAA;gBAAQyD,KAAK,EAAC,QAAQ;gBAAArB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAOuD,OAAO,EAAC,SAAS;cAAAnB,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzCxC,OAAA;cACEwD,EAAE,EAAC,SAAS;cACZC,KAAK,EAAEjD,OAAQ;cACfkD,QAAQ,EAAGtC,CAAC,IAAKX,UAAU,CAACW,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;cAC5CG,QAAQ,EAAEhD,YAAa;cACvBiD,IAAI,EAAE;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNxC,OAAA;YACE0C,IAAI,EAAC,QAAQ;YACbP,SAAS,EAAC,eAAe;YACzByB,QAAQ,EAAEhD,YAAa;YAAAwB,QAAA,EAEtBxB,YAAY,GAAG,aAAa,GAAG;UAAa;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnC,EAAA,CA9LIJ,UAAU;EAAA,QAOGH,OAAO;AAAA;AAAAgE,EAAA,GAPpB7D,UAAU;AAgMhB,eAAeA,UAAU;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}