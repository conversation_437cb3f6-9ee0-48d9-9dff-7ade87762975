#!/usr/bin/env python3
"""
<PERSON>ript to create database tables for the fraud detection platform.
"""
import os
import sys
from pathlib import Path

# Add model-service src to path
model_service_path = Path(__file__).parent / "model-service" / "src"
sys.path.insert(0, str(model_service_path))

# Add ingest-service src to path
ingest_service_path = Path(__file__).parent / "ingest-service" / "src"
sys.path.insert(0, str(ingest_service_path))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Import models from both services
from app.models import Base as ModelBase
from models import Base as IngestBase

# Database configuration
DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "sqlite:///./fraud_detection.db"
)

def create_tables():
    """Create all database tables"""
    print("Creating database tables...")
    
    # Create engine
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False} if DATABASE_URL.startswith("sqlite") else {},
        echo=True
    )
    
    try:
        # Create all tables from model service
        print("Creating model service tables...")
        ModelBase.metadata.create_all(bind=engine)
        
        # Create all tables from ingest service
        print("Creating ingest service tables...")
        IngestBase.metadata.create_all(bind=engine)
        
        print("✅ All database tables created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False

if __name__ == "__main__":
    success = create_tables()
    if success:
        print("\n🎉 Database tables setup complete!")
    else:
        print("\n❌ Database tables setup failed!")
        sys.exit(1)
