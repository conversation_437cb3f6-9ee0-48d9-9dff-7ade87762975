{"ast": null, "code": "import axios from 'axios';\n\n// Type definitions\n\n// API base URL\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 10000\n});\n\n// Add request interceptor for authentication\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers['Authorization'] = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Add response interceptor for error handling\napi.interceptors.response.use(response => response, error => {\n  var _error$response, _error$response2, _error$response3;\n  const apiError = {\n    message: error.message || 'An error occurred',\n    status: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) || 500,\n    details: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data\n  };\n\n  // Handle authentication errors\n  if (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 401) {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n  return Promise.reject(apiError);\n});\n\n// Authentication API\nexport const loginUser = async (username, password) => {\n  const formData = new FormData();\n  formData.append('username', username);\n  formData.append('password', password);\n  const response = await api.post('/auth/login', formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  });\n  return response.data;\n};\n\n// Import types\n\n// Transaction API\nexport const fetchLatestTransactions = async (limit = 50) => {\n  const response = await api.get(`/transactions/latest?n=${limit}`);\n  return response.data;\n};\nexport const fetchTransaction = async id => {\n  const response = await api.get(`/transactions/${id}`);\n  return response.data;\n};\n\n// Case Management API\nexport const fetchCases = async (filter = {}) => {\n  let url = '/cases';\n  const params = new URLSearchParams();\n  if (filter.status && filter.status !== 'all') {\n    params.append('status', filter.status);\n  }\n  if (filter.transaction_id) {\n    params.append('transaction_id', filter.transaction_id);\n  }\n  if (filter.tag) {\n    params.append('tag', filter.tag);\n  }\n  if (filter.user_id) {\n    params.append('user_id', filter.user_id.toString());\n  }\n  if (filter.date_from) {\n    params.append('date_from', filter.date_from);\n  }\n  if (filter.date_to) {\n    params.append('date_to', filter.date_to);\n  }\n  if (params.toString()) {\n    url += `?${params.toString()}`;\n  }\n  const response = await api.get(url);\n  return response.data;\n};\nexport const fetchCase = async id => {\n  const response = await api.get(`/cases/${id}`);\n  return response.data;\n};\nexport const createCase = async caseData => {\n  const response = await api.post('/cases', caseData);\n  return response.data;\n};\nexport const updateCase = async (id, caseData) => {\n  const response = await api.put(`/cases/${id}`, caseData);\n  return response.data;\n};\nexport const deleteCase = async id => {\n  await api.delete(`/cases/${id}`);\n};\n\n// Analytics API\nexport const fetchCaseStats = async () => {\n  const response = await api.get('/cases/stats');\n  return response.data;\n};\nexport const fetchFeatureImportance = async () => {\n  try {\n    const response = await api.get('/feature-importance');\n    return response.data;\n  } catch (error) {\n    // Return mock data if endpoint is not available\n    return [{\n      feature: 'type_TRANSFER',\n      importance: 0.35\n    }, {\n      feature: 'amount',\n      importance: 0.25\n    }, {\n      feature: 'oldbalanceOrg',\n      importance: 0.15\n    }, {\n      feature: 'newbalanceOrig',\n      importance: 0.10\n    }, {\n      feature: 'oldbalanceDest',\n      importance: 0.08\n    }, {\n      feature: 'newbalanceDest',\n      importance: 0.07\n    }];\n  }\n};\nexport const fetchFraudTrend = async (dateRange = 'week') => {\n  try {\n    const response = await api.get(`/fraud-trend?range=${dateRange}`);\n    return response.data;\n  } catch (error) {\n    // Return mock data if endpoint is not available\n    return generateMockTrendData(dateRange);\n  }\n};\n\n// Model and Health API\nexport const fetchModelInfo = async () => {\n  const response = await api.get('/model/info');\n  return response.data;\n};\nexport const fetchHealthStatus = async () => {\n  const response = await api.get('/health');\n  return response.data;\n};\nexport const fetchServiceStats = async () => {\n  const response = await api.get('/stats');\n  return response.data;\n};\n\n// Helper function to generate mock trend data for demo purposes\nconst generateMockTrendData = dateRange => {\n  const now = new Date();\n  const data = [];\n  let days;\n  switch (dateRange) {\n    case 'day':\n      days = 1;\n      break;\n    case 'week':\n      days = 7;\n      break;\n    case 'month':\n      days = 30;\n      break;\n    case 'year':\n      days = 365;\n      break;\n    default:\n      days = 7;\n  }\n  for (let i = days - 1; i >= 0; i--) {\n    const date = new Date(now);\n    date.setDate(date.getDate() - i);\n    data.push({\n      date: date.toISOString().split('T')[0],\n      count: Math.floor(Math.random() * 10) + 1\n    });\n  }\n  return data;\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "timeout", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "error", "Promise", "reject", "response", "_error$response", "_error$response2", "_error$response3", "apiError", "message", "status", "details", "data", "removeItem", "window", "location", "href", "loginUser", "username", "password", "formData", "FormData", "append", "post", "fetchLatestTransactions", "limit", "get", "fetchTransaction", "id", "fetchCases", "filter", "url", "params", "URLSearchParams", "transaction_id", "tag", "user_id", "toString", "date_from", "date_to", "fetchCase", "createCase", "caseData", "updateCase", "put", "deleteCase", "delete", "fetchCaseStats", "fetchFeatureImportance", "feature", "importance", "fetchFraudTrend", "date<PERSON><PERSON><PERSON>", "generateMockTrendData", "fetchModelInfo", "fetchHealthStatus", "fetchServiceStats", "now", "Date", "days", "i", "date", "setDate", "getDate", "push", "toISOString", "split", "count", "Math", "floor", "random"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosResponse, AxiosError } from 'axios';\n\n// Type definitions\nexport interface LoginRequest {\n  username: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  access_token: string;\n  token_type: string;\n}\n\nexport interface ApiError {\n  message: string;\n  status: number;\n  details?: any;\n}\n\n// API base URL\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 10000\n});\n\n// Add request interceptor for authentication\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers['Authorization'] = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error: AxiosError) => {\n    return Promise.reject(error);\n  }\n);\n\n// Add response interceptor for error handling\napi.interceptors.response.use(\n  (response: AxiosResponse) => response,\n  (error: AxiosError) => {\n    const apiError: ApiError = {\n      message: error.message || 'An error occurred',\n      status: error.response?.status || 500,\n      details: error.response?.data\n    };\n\n    // Handle authentication errors\n    if (error.response?.status === 401) {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n\n    return Promise.reject(apiError);\n  }\n);\n\n// Authentication API\nexport const loginUser = async (username: string, password: string): Promise<LoginResponse> => {\n  const formData = new FormData();\n  formData.append('username', username);\n  formData.append('password', password);\n\n  const response = await api.post<LoginResponse>('/auth/login', formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  });\n  return response.data;\n};\n\n// Import types\nimport {\n  Transaction,\n  Case,\n  CaseCreate,\n  CaseUpdate,\n  CaseFilter,\n  CaseStats,\n  FeatureImportance,\n  FraudTrendData,\n  ModelInfo,\n  HealthStatus,\n  ServiceStats,\n  PaginatedResponse\n} from '../types';\n\n// Transaction API\nexport const fetchLatestTransactions = async (limit: number = 50): Promise<Transaction[]> => {\n  const response = await api.get<Transaction[]>(`/transactions/latest?n=${limit}`);\n  return response.data;\n};\n\nexport const fetchTransaction = async (id: string): Promise<Transaction> => {\n  const response = await api.get<Transaction>(`/transactions/${id}`);\n  return response.data;\n};\n\n// Case Management API\nexport const fetchCases = async (filter: CaseFilter = {}): Promise<Case[]> => {\n  let url = '/cases';\n  const params = new URLSearchParams();\n\n  if (filter.status && filter.status !== 'all') {\n    params.append('status', filter.status);\n  }\n\n  if (filter.transaction_id) {\n    params.append('transaction_id', filter.transaction_id);\n  }\n\n  if (filter.tag) {\n    params.append('tag', filter.tag);\n  }\n\n  if (filter.user_id) {\n    params.append('user_id', filter.user_id.toString());\n  }\n\n  if (filter.date_from) {\n    params.append('date_from', filter.date_from);\n  }\n\n  if (filter.date_to) {\n    params.append('date_to', filter.date_to);\n  }\n\n  if (params.toString()) {\n    url += `?${params.toString()}`;\n  }\n\n  const response = await api.get<Case[]>(url);\n  return response.data;\n};\n\nexport const fetchCase = async (id: number): Promise<Case> => {\n  const response = await api.get<Case>(`/cases/${id}`);\n  return response.data;\n};\n\nexport const createCase = async (caseData: CaseCreate): Promise<Case> => {\n  const response = await api.post<Case>('/cases', caseData);\n  return response.data;\n};\n\nexport const updateCase = async (id: number, caseData: CaseUpdate): Promise<Case> => {\n  const response = await api.put<Case>(`/cases/${id}`, caseData);\n  return response.data;\n};\n\nexport const deleteCase = async (id: number): Promise<void> => {\n  await api.delete(`/cases/${id}`);\n};\n\n// Analytics API\nexport const fetchCaseStats = async (): Promise<CaseStats> => {\n  const response = await api.get<CaseStats>('/cases/stats');\n  return response.data;\n};\n\nexport const fetchFeatureImportance = async (): Promise<FeatureImportance[]> => {\n  try {\n    const response = await api.get<FeatureImportance[]>('/feature-importance');\n    return response.data;\n  } catch (error) {\n    // Return mock data if endpoint is not available\n    return [\n      { feature: 'type_TRANSFER', importance: 0.35 },\n      { feature: 'amount', importance: 0.25 },\n      { feature: 'oldbalanceOrg', importance: 0.15 },\n      { feature: 'newbalanceOrig', importance: 0.10 },\n      { feature: 'oldbalanceDest', importance: 0.08 },\n      { feature: 'newbalanceDest', importance: 0.07 }\n    ];\n  }\n};\n\nexport const fetchFraudTrend = async (dateRange: string = 'week'): Promise<FraudTrendData[]> => {\n  try {\n    const response = await api.get<FraudTrendData[]>(`/fraud-trend?range=${dateRange}`);\n    return response.data;\n  } catch (error) {\n    // Return mock data if endpoint is not available\n    return generateMockTrendData(dateRange);\n  }\n};\n\n// Model and Health API\nexport const fetchModelInfo = async (): Promise<ModelInfo> => {\n  const response = await api.get<ModelInfo>('/model/info');\n  return response.data;\n};\n\nexport const fetchHealthStatus = async (): Promise<HealthStatus> => {\n  const response = await api.get<HealthStatus>('/health');\n  return response.data;\n};\n\nexport const fetchServiceStats = async (): Promise<ServiceStats> => {\n  const response = await api.get<ServiceStats>('/stats');\n  return response.data;\n};\n\n// Helper function to generate mock trend data for demo purposes\nconst generateMockTrendData = (dateRange: string): FraudTrendData[] => {\n  const now = new Date();\n  const data: FraudTrendData[] = [];\n  let days: number;\n\n  switch (dateRange) {\n    case 'day':\n      days = 1;\n      break;\n    case 'week':\n      days = 7;\n      break;\n    case 'month':\n      days = 30;\n      break;\n    case 'year':\n      days = 365;\n      break;\n    default:\n      days = 7;\n  }\n\n  for (let i = days - 1; i >= 0; i--) {\n    const date = new Date(now);\n    date.setDate(date.getDate() - i);\n\n    data.push({\n      date: date.toISOString().split('T')[0],\n      count: Math.floor(Math.random() * 10) + 1\n    });\n  }\n\n  return data;\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAqC,OAAO;;AAExD;;AAiBA;AACA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAExE;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,OAAO;EAChBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACL,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUM,KAAK,EAAE;EACrD;EACA,OAAOD,MAAM;AACf,CAAC,EACAI,KAAiB,IAAK;EACrB,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACK,YAAY,CAACU,QAAQ,CAACR,GAAG,CAC1BQ,QAAuB,IAAKA,QAAQ,EACpCH,KAAiB,IAAK;EAAA,IAAAI,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;EACrB,MAAMC,QAAkB,GAAG;IACzBC,OAAO,EAAER,KAAK,CAACQ,OAAO,IAAI,mBAAmB;IAC7CC,MAAM,EAAE,EAAAL,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBK,MAAM,KAAI,GAAG;IACrCC,OAAO,GAAAL,gBAAA,GAAEL,KAAK,CAACG,QAAQ,cAAAE,gBAAA,uBAAdA,gBAAA,CAAgBM;EAC3B,CAAC;;EAED;EACA,IAAI,EAAAL,gBAAA,GAAAN,KAAK,CAACG,QAAQ,cAAAG,gBAAA,uBAAdA,gBAAA,CAAgBG,MAAM,MAAK,GAAG,EAAE;IAClCX,YAAY,CAACc,UAAU,CAAC,OAAO,CAAC;IAChCd,YAAY,CAACc,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EAEA,OAAOd,OAAO,CAACC,MAAM,CAACK,QAAQ,CAAC;AACjC,CACF,CAAC;;AAED;AACA,OAAO,MAAMS,SAAS,GAAG,MAAAA,CAAOC,QAAgB,EAAEC,QAAgB,KAA6B;EAC7F,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,QAAQ,CAAC;EACrCE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,QAAQ,CAAC;EAErC,MAAMf,QAAQ,GAAG,MAAMf,GAAG,CAACkC,IAAI,CAAgB,aAAa,EAAEH,QAAQ,EAAE;IACtE5B,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACF,OAAOY,QAAQ,CAACQ,IAAI;AACtB,CAAC;;AAED;;AAgBA;AACA,OAAO,MAAMY,uBAAuB,GAAG,MAAAA,CAAOC,KAAa,GAAG,EAAE,KAA6B;EAC3F,MAAMrB,QAAQ,GAAG,MAAMf,GAAG,CAACqC,GAAG,CAAgB,0BAA0BD,KAAK,EAAE,CAAC;EAChF,OAAOrB,QAAQ,CAACQ,IAAI;AACtB,CAAC;AAED,OAAO,MAAMe,gBAAgB,GAAG,MAAOC,EAAU,IAA2B;EAC1E,MAAMxB,QAAQ,GAAG,MAAMf,GAAG,CAACqC,GAAG,CAAc,iBAAiBE,EAAE,EAAE,CAAC;EAClE,OAAOxB,QAAQ,CAACQ,IAAI;AACtB,CAAC;;AAED;AACA,OAAO,MAAMiB,UAAU,GAAG,MAAAA,CAAOC,MAAkB,GAAG,CAAC,CAAC,KAAsB;EAC5E,IAAIC,GAAG,GAAG,QAAQ;EAClB,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;EAEpC,IAAIH,MAAM,CAACpB,MAAM,IAAIoB,MAAM,CAACpB,MAAM,KAAK,KAAK,EAAE;IAC5CsB,MAAM,CAACV,MAAM,CAAC,QAAQ,EAAEQ,MAAM,CAACpB,MAAM,CAAC;EACxC;EAEA,IAAIoB,MAAM,CAACI,cAAc,EAAE;IACzBF,MAAM,CAACV,MAAM,CAAC,gBAAgB,EAAEQ,MAAM,CAACI,cAAc,CAAC;EACxD;EAEA,IAAIJ,MAAM,CAACK,GAAG,EAAE;IACdH,MAAM,CAACV,MAAM,CAAC,KAAK,EAAEQ,MAAM,CAACK,GAAG,CAAC;EAClC;EAEA,IAAIL,MAAM,CAACM,OAAO,EAAE;IAClBJ,MAAM,CAACV,MAAM,CAAC,SAAS,EAAEQ,MAAM,CAACM,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC;EACrD;EAEA,IAAIP,MAAM,CAACQ,SAAS,EAAE;IACpBN,MAAM,CAACV,MAAM,CAAC,WAAW,EAAEQ,MAAM,CAACQ,SAAS,CAAC;EAC9C;EAEA,IAAIR,MAAM,CAACS,OAAO,EAAE;IAClBP,MAAM,CAACV,MAAM,CAAC,SAAS,EAAEQ,MAAM,CAACS,OAAO,CAAC;EAC1C;EAEA,IAAIP,MAAM,CAACK,QAAQ,CAAC,CAAC,EAAE;IACrBN,GAAG,IAAI,IAAIC,MAAM,CAACK,QAAQ,CAAC,CAAC,EAAE;EAChC;EAEA,MAAMjC,QAAQ,GAAG,MAAMf,GAAG,CAACqC,GAAG,CAASK,GAAG,CAAC;EAC3C,OAAO3B,QAAQ,CAACQ,IAAI;AACtB,CAAC;AAED,OAAO,MAAM4B,SAAS,GAAG,MAAOZ,EAAU,IAAoB;EAC5D,MAAMxB,QAAQ,GAAG,MAAMf,GAAG,CAACqC,GAAG,CAAO,UAAUE,EAAE,EAAE,CAAC;EACpD,OAAOxB,QAAQ,CAACQ,IAAI;AACtB,CAAC;AAED,OAAO,MAAM6B,UAAU,GAAG,MAAOC,QAAoB,IAAoB;EACvE,MAAMtC,QAAQ,GAAG,MAAMf,GAAG,CAACkC,IAAI,CAAO,QAAQ,EAAEmB,QAAQ,CAAC;EACzD,OAAOtC,QAAQ,CAACQ,IAAI;AACtB,CAAC;AAED,OAAO,MAAM+B,UAAU,GAAG,MAAAA,CAAOf,EAAU,EAAEc,QAAoB,KAAoB;EACnF,MAAMtC,QAAQ,GAAG,MAAMf,GAAG,CAACuD,GAAG,CAAO,UAAUhB,EAAE,EAAE,EAAEc,QAAQ,CAAC;EAC9D,OAAOtC,QAAQ,CAACQ,IAAI;AACtB,CAAC;AAED,OAAO,MAAMiC,UAAU,GAAG,MAAOjB,EAAU,IAAoB;EAC7D,MAAMvC,GAAG,CAACyD,MAAM,CAAC,UAAUlB,EAAE,EAAE,CAAC;AAClC,CAAC;;AAED;AACA,OAAO,MAAMmB,cAAc,GAAG,MAAAA,CAAA,KAAgC;EAC5D,MAAM3C,QAAQ,GAAG,MAAMf,GAAG,CAACqC,GAAG,CAAY,cAAc,CAAC;EACzD,OAAOtB,QAAQ,CAACQ,IAAI;AACtB,CAAC;AAED,OAAO,MAAMoC,sBAAsB,GAAG,MAAAA,CAAA,KAA0C;EAC9E,IAAI;IACF,MAAM5C,QAAQ,GAAG,MAAMf,GAAG,CAACqC,GAAG,CAAsB,qBAAqB,CAAC;IAC1E,OAAOtB,QAAQ,CAACQ,IAAI;EACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;IACd;IACA,OAAO,CACL;MAAEgD,OAAO,EAAE,eAAe;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC9C;MAAED,OAAO,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAK,CAAC,EACvC;MAAED,OAAO,EAAE,eAAe;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC9C;MAAED,OAAO,EAAE,gBAAgB;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC/C;MAAED,OAAO,EAAE,gBAAgB;MAAEC,UAAU,EAAE;IAAK,CAAC,EAC/C;MAAED,OAAO,EAAE,gBAAgB;MAAEC,UAAU,EAAE;IAAK,CAAC,CAChD;EACH;AACF,CAAC;AAED,OAAO,MAAMC,eAAe,GAAG,MAAAA,CAAOC,SAAiB,GAAG,MAAM,KAAgC;EAC9F,IAAI;IACF,MAAMhD,QAAQ,GAAG,MAAMf,GAAG,CAACqC,GAAG,CAAmB,sBAAsB0B,SAAS,EAAE,CAAC;IACnF,OAAOhD,QAAQ,CAACQ,IAAI;EACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;IACd;IACA,OAAOoD,qBAAqB,CAACD,SAAS,CAAC;EACzC;AACF,CAAC;;AAED;AACA,OAAO,MAAME,cAAc,GAAG,MAAAA,CAAA,KAAgC;EAC5D,MAAMlD,QAAQ,GAAG,MAAMf,GAAG,CAACqC,GAAG,CAAY,aAAa,CAAC;EACxD,OAAOtB,QAAQ,CAACQ,IAAI;AACtB,CAAC;AAED,OAAO,MAAM2C,iBAAiB,GAAG,MAAAA,CAAA,KAAmC;EAClE,MAAMnD,QAAQ,GAAG,MAAMf,GAAG,CAACqC,GAAG,CAAe,SAAS,CAAC;EACvD,OAAOtB,QAAQ,CAACQ,IAAI;AACtB,CAAC;AAED,OAAO,MAAM4C,iBAAiB,GAAG,MAAAA,CAAA,KAAmC;EAClE,MAAMpD,QAAQ,GAAG,MAAMf,GAAG,CAACqC,GAAG,CAAe,QAAQ,CAAC;EACtD,OAAOtB,QAAQ,CAACQ,IAAI;AACtB,CAAC;;AAED;AACA,MAAMyC,qBAAqB,GAAID,SAAiB,IAAuB;EACrE,MAAMK,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;EACtB,MAAM9C,IAAsB,GAAG,EAAE;EACjC,IAAI+C,IAAY;EAEhB,QAAQP,SAAS;IACf,KAAK,KAAK;MACRO,IAAI,GAAG,CAAC;MACR;IACF,KAAK,MAAM;MACTA,IAAI,GAAG,CAAC;MACR;IACF,KAAK,OAAO;MACVA,IAAI,GAAG,EAAE;MACT;IACF,KAAK,MAAM;MACTA,IAAI,GAAG,GAAG;MACV;IACF;MACEA,IAAI,GAAG,CAAC;EACZ;EAEA,KAAK,IAAIC,CAAC,GAAGD,IAAI,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAClC,MAAMC,IAAI,GAAG,IAAIH,IAAI,CAACD,GAAG,CAAC;IAC1BI,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,GAAGH,CAAC,CAAC;IAEhChD,IAAI,CAACoD,IAAI,CAAC;MACRH,IAAI,EAAEA,IAAI,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtCC,KAAK,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;IAC1C,CAAC,CAAC;EACJ;EAEA,OAAO1D,IAAI;AACb,CAAC;AAED,eAAevB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}