{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../services/AuthContext';\nimport { UserRole } from '../types';\nimport { PageLoading } from './Loading';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  requiredRole,\n  fallbackPath = '/login'\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    user\n  } = useAuth();\n  const location = useLocation();\n\n  // Show loading while authentication state is being determined\n  if (user === undefined) {\n    return /*#__PURE__*/_jsxDEV(PageLoading, {\n      message: \"Checking authentication...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated || !user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: fallbackPath,\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Check role-based access if required\n  if (requiredRole && user.role !== requiredRole) {\n    // Check if user has admin role (admin can access everything)\n    if (user.role !== UserRole.ADMIN) {\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/unauthorized\",\n        state: {\n          from: location,\n          requiredRole\n        },\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this);\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s(ProtectedRoute, \"eBZQushvzp9k1PDvsZlyRAWOAfg=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "UserRole", "PageLoading", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProtectedRoute", "children", "requiredRole", "fallback<PERSON><PERSON>", "_s", "isAuthenticated", "user", "location", "undefined", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "role", "ADMIN", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/components/ProtectedRoute.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../services/AuthContext';\nimport { UserRole } from '../types';\nimport { PageLoading } from './Loading';\n\ninterface ProtectedRouteProps {\n  children: ReactNode;\n  requiredRole?: UserRole;\n  fallbackPath?: string;\n}\n\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = ({\n  children,\n  requiredRole,\n  fallbackPath = '/login'\n}) => {\n  const { isAuthenticated, user } = useAuth();\n  const location = useLocation();\n\n  // Show loading while authentication state is being determined\n  if (user === undefined) {\n    return <PageLoading message=\"Checking authentication...\" />;\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated || !user) {\n    return (\n      <Navigate\n        to={fallbackPath}\n        state={{ from: location }}\n        replace\n      />\n    );\n  }\n\n  // Check role-based access if required\n  if (requiredRole && user.role !== requiredRole) {\n    // Check if user has admin role (admin can access everything)\n    if (user.role !== UserRole.ADMIN) {\n      return (\n        <Navigate\n          to=\"/unauthorized\"\n          state={{ from: location, requiredRole }}\n          replace\n        />\n      );\n    }\n  }\n\n  return <>{children}</>;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAqB,OAAO;AACxC,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,UAAU;AACnC,SAASC,WAAW,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQxC,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,QAAQ;EACRC,YAAY;EACZC,YAAY,GAAG;AACjB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC3C,MAAMc,QAAQ,GAAGf,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIc,IAAI,KAAKE,SAAS,EAAE;IACtB,oBAAOX,OAAA,CAACF,WAAW;MAACc,OAAO,EAAC;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7D;;EAEA;EACA,IAAI,CAACR,eAAe,IAAI,CAACC,IAAI,EAAE;IAC7B,oBACET,OAAA,CAACN,QAAQ;MACPuB,EAAE,EAAEX,YAAa;MACjBY,KAAK,EAAE;QAAEC,IAAI,EAAET;MAAS,CAAE;MAC1BU,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEN;;EAEA;EACA,IAAIX,YAAY,IAAII,IAAI,CAACY,IAAI,KAAKhB,YAAY,EAAE;IAC9C;IACA,IAAII,IAAI,CAACY,IAAI,KAAKxB,QAAQ,CAACyB,KAAK,EAAE;MAChC,oBACEtB,OAAA,CAACN,QAAQ;QACPuB,EAAE,EAAC,eAAe;QAClBC,KAAK,EAAE;UAAEC,IAAI,EAAET,QAAQ;UAAEL;QAAa,CAAE;QACxCe,OAAO;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAEN;EACF;EAEA,oBAAOhB,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACG,EAAA,CAvCIJ,cAA6C;EAAA,QAKfP,OAAO,EACxBD,WAAW;AAAA;AAAA4B,EAAA,GANxBpB,cAA6C;AAyCnD,eAAeA,cAAc;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}