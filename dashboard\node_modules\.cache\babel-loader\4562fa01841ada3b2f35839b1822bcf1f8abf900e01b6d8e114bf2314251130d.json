{"ast": null, "code": "var _s = $RefreshSig$();\n/**\n * Validation utilities for forms and data\n */\n\n/**\n * Validate a single field against a rule\n */\nexport const validateField = (value, rule, fieldName) => {\n  // Required validation\n  if (rule.required && (value === null || value === undefined || value === '')) {\n    return `${fieldName} is required`;\n  }\n\n  // Skip other validations if value is empty and not required\n  if (!rule.required && (value === null || value === undefined || value === '')) {\n    return null;\n  }\n\n  // String validations\n  if (typeof value === 'string') {\n    if (rule.minLength && value.length < rule.minLength) {\n      return `${fieldName} must be at least ${rule.minLength} characters`;\n    }\n    if (rule.maxLength && value.length > rule.maxLength) {\n      return `${fieldName} must be no more than ${rule.maxLength} characters`;\n    }\n    if (rule.pattern && !rule.pattern.test(value)) {\n      return `${fieldName} format is invalid`;\n    }\n  }\n\n  // Number validations\n  if (typeof value === 'number') {\n    if (rule.min !== undefined && value < rule.min) {\n      return `${fieldName} must be at least ${rule.min}`;\n    }\n    if (rule.max !== undefined && value > rule.max) {\n      return `${fieldName} must be no more than ${rule.max}`;\n    }\n  }\n\n  // Custom validation\n  if (rule.custom) {\n    return rule.custom(value);\n  }\n  return null;\n};\n\n/**\n * Validate an object against a schema\n */\nexport const validateSchema = (data, schema) => {\n  const errors = {};\n  for (const [field, rule] of Object.entries(schema)) {\n    const error = validateField(data[field], rule, field);\n    if (error) {\n      errors[field] = error;\n    }\n  }\n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors\n  };\n};\n\n/**\n * Common validation patterns\n */\nexport const patterns = {\n  email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  phone: /^\\+?[\\d\\s\\-\\(\\)]+$/,\n  url: /^https?:\\/\\/.+/,\n  alphanumeric: /^[a-zA-Z0-9]+$/,\n  numeric: /^\\d+$/,\n  decimal: /^\\d+(\\.\\d+)?$/,\n  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/,\n  transactionId: /^[A-Za-z0-9_-]+$/,\n  accountNumber: /^[A-Za-z0-9]+$/\n};\n\n/**\n * Pre-defined validation rules\n */\nexport const rules = {\n  required: {\n    required: true\n  },\n  email: {\n    required: true,\n    pattern: patterns.email\n  },\n  password: {\n    required: true,\n    minLength: 8,\n    pattern: patterns.password\n  },\n  phone: {\n    pattern: patterns.phone\n  },\n  url: {\n    pattern: patterns.url\n  },\n  transactionId: {\n    required: true,\n    pattern: patterns.transactionId,\n    minLength: 1,\n    maxLength: 50\n  },\n  amount: {\n    required: true,\n    min: 0,\n    custom: value => {\n      if (typeof value !== 'number' || isNaN(value)) {\n        return 'Amount must be a valid number';\n      }\n      return null;\n    }\n  },\n  riskScore: {\n    min: 0,\n    max: 1,\n    custom: value => {\n      if (value !== undefined && (typeof value !== 'number' || isNaN(value))) {\n        return 'Risk score must be a valid number';\n      }\n      return null;\n    }\n  }\n};\n\n/**\n * Validation schemas for common forms\n */\nexport const schemas = {\n  login: {\n    username: rules.required,\n    password: rules.required\n  },\n  case: {\n    transaction_id: rules.transactionId,\n    tag: rules.required,\n    comment: {\n      maxLength: 500\n    }\n  },\n  transaction: {\n    transaction_id: rules.transactionId,\n    type: rules.required,\n    amount: rules.amount,\n    nameOrig: rules.required,\n    nameDest: rules.required\n  },\n  user: {\n    username: {\n      required: true,\n      minLength: 3,\n      maxLength: 50,\n      pattern: patterns.alphanumeric\n    },\n    password: rules.password,\n    role: rules.required\n  }\n};\n\n/**\n * Sanitize input data\n */\nexport const sanitize = {\n  /**\n   * Remove HTML tags and dangerous characters\n   */\n  html: input => {\n    if (!input) return '';\n    return input.replace(/<[^>]*>/g, '') // Remove HTML tags\n    .replace(/[<>'\"&]/g, '') // Remove dangerous characters\n    .trim();\n  },\n  /**\n   * Sanitize for SQL-like queries (basic protection)\n   */\n  sql: input => {\n    if (!input) return '';\n    return input.replace(/[';--]/g, '') // Remove SQL injection patterns\n    .trim();\n  },\n  /**\n   * Sanitize numeric input\n   */\n  number: input => {\n    if (input === null || input === undefined || input === '') {\n      return null;\n    }\n    const num = Number(input);\n    return isNaN(num) ? null : num;\n  },\n  /**\n   * Sanitize boolean input\n   */\n  boolean: input => {\n    if (typeof input === 'boolean') return input;\n    if (typeof input === 'string') {\n      return input.toLowerCase() === 'true';\n    }\n    return Boolean(input);\n  },\n  /**\n   * Sanitize array input\n   */\n  array: input => {\n    if (Array.isArray(input)) return input;\n    if (input === null || input === undefined) return [];\n    return [input];\n  }\n};\n\n/**\n * Data type validators\n */\nexport const isValid = {\n  email: email => patterns.email.test(email),\n  phone: phone => patterns.phone.test(phone),\n  url: url => patterns.url.test(url),\n  date: date => !isNaN(Date.parse(date)),\n  json: json => {\n    try {\n      JSON.parse(json);\n      return true;\n    } catch {\n      return false;\n    }\n  },\n  uuid: uuid => {\n    const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n    return uuidPattern.test(uuid);\n  },\n  creditCard: cardNumber => {\n    // Luhn algorithm for credit card validation\n    const cleaned = cardNumber.replace(/\\D/g, '');\n    if (cleaned.length < 13 || cleaned.length > 19) return false;\n    let sum = 0;\n    let isEven = false;\n    for (let i = cleaned.length - 1; i >= 0; i--) {\n      let digit = parseInt(cleaned[i]);\n      if (isEven) {\n        digit *= 2;\n        if (digit > 9) {\n          digit -= 9;\n        }\n      }\n      sum += digit;\n      isEven = !isEven;\n    }\n    return sum % 10 === 0;\n  }\n};\n\n/**\n * Form validation hook\n */\nimport { useState, useCallback } from 'react';\nexport function useValidation(schema) {\n  _s();\n  const [errors, setErrors] = useState({});\n  const validate = useCallback(data => {\n    const result = validateSchema(data, schema);\n    setErrors(result.errors);\n    return result.isValid;\n  }, [schema]);\n  const validateField = useCallback((field, value) => {\n    const rule = schema[field];\n    if (!rule) return true;\n    const error = validateField(value, rule, field);\n    setErrors(prev => ({\n      ...prev,\n      [field]: error || ''\n    }));\n    return !error;\n  }, [schema]);\n  const clearErrors = useCallback(() => {\n    setErrors({});\n  }, []);\n  const clearFieldError = useCallback(field => {\n    setErrors(prev => {\n      const newErrors = {\n        ...prev\n      };\n      delete newErrors[field];\n      return newErrors;\n    });\n  }, []);\n  return {\n    errors,\n    validate,\n    validateField,\n    clearErrors,\n    clearFieldError,\n    hasErrors: Object.keys(errors).length > 0\n  };\n}\n_s(useValidation, \"H0Ms6/FAXX1f6JfI/QVXt9exQz4=\");", "map": {"version": 3, "names": ["validateField", "value", "rule", "fieldName", "required", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "length", "max<PERSON><PERSON><PERSON>", "pattern", "test", "min", "max", "custom", "validateSchema", "data", "schema", "errors", "field", "Object", "entries", "error", "<PERSON><PERSON><PERSON><PERSON>", "keys", "patterns", "email", "phone", "url", "alphanumeric", "numeric", "decimal", "password", "transactionId", "accountNumber", "rules", "amount", "isNaN", "riskScore", "schemas", "login", "username", "case", "transaction_id", "tag", "comment", "transaction", "type", "name<PERSON><PERSON>", "nameDest", "user", "role", "sanitize", "html", "input", "replace", "trim", "sql", "number", "num", "Number", "boolean", "toLowerCase", "Boolean", "array", "Array", "isArray", "date", "Date", "parse", "json", "JSON", "uuid", "uuidPattern", "creditCard", "cardNumber", "cleaned", "sum", "isEven", "i", "digit", "parseInt", "useState", "useCallback", "useValidation", "_s", "setErrors", "validate", "result", "prev", "clearErrors", "clearFieldError", "newErrors", "hasErrors"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/utils/validation.ts"], "sourcesContent": ["/**\n * Validation utilities for forms and data\n */\n\nexport interface ValidationRule {\n  required?: boolean;\n  minLength?: number;\n  maxLength?: number;\n  min?: number;\n  max?: number;\n  pattern?: RegExp;\n  custom?: (value: any) => string | null;\n}\n\nexport interface ValidationSchema {\n  [field: string]: ValidationRule;\n}\n\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: Record<string, string>;\n}\n\n/**\n * Validate a single field against a rule\n */\nexport const validateField = (value: any, rule: ValidationRule, fieldName: string): string | null => {\n  // Required validation\n  if (rule.required && (value === null || value === undefined || value === '')) {\n    return `${fieldName} is required`;\n  }\n  \n  // Skip other validations if value is empty and not required\n  if (!rule.required && (value === null || value === undefined || value === '')) {\n    return null;\n  }\n  \n  // String validations\n  if (typeof value === 'string') {\n    if (rule.minLength && value.length < rule.minLength) {\n      return `${fieldName} must be at least ${rule.minLength} characters`;\n    }\n    \n    if (rule.maxLength && value.length > rule.maxLength) {\n      return `${fieldName} must be no more than ${rule.maxLength} characters`;\n    }\n    \n    if (rule.pattern && !rule.pattern.test(value)) {\n      return `${fieldName} format is invalid`;\n    }\n  }\n  \n  // Number validations\n  if (typeof value === 'number') {\n    if (rule.min !== undefined && value < rule.min) {\n      return `${fieldName} must be at least ${rule.min}`;\n    }\n    \n    if (rule.max !== undefined && value > rule.max) {\n      return `${fieldName} must be no more than ${rule.max}`;\n    }\n  }\n  \n  // Custom validation\n  if (rule.custom) {\n    return rule.custom(value);\n  }\n  \n  return null;\n};\n\n/**\n * Validate an object against a schema\n */\nexport const validateSchema = (data: Record<string, any>, schema: ValidationSchema): ValidationResult => {\n  const errors: Record<string, string> = {};\n  \n  for (const [field, rule] of Object.entries(schema)) {\n    const error = validateField(data[field], rule, field);\n    if (error) {\n      errors[field] = error;\n    }\n  }\n  \n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors\n  };\n};\n\n/**\n * Common validation patterns\n */\nexport const patterns = {\n  email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  phone: /^\\+?[\\d\\s\\-\\(\\)]+$/,\n  url: /^https?:\\/\\/.+/,\n  alphanumeric: /^[a-zA-Z0-9]+$/,\n  numeric: /^\\d+$/,\n  decimal: /^\\d+(\\.\\d+)?$/,\n  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/,\n  transactionId: /^[A-Za-z0-9_-]+$/,\n  accountNumber: /^[A-Za-z0-9]+$/\n};\n\n/**\n * Pre-defined validation rules\n */\nexport const rules = {\n  required: { required: true },\n  email: { \n    required: true, \n    pattern: patterns.email \n  },\n  password: { \n    required: true, \n    minLength: 8, \n    pattern: patterns.password \n  },\n  phone: { \n    pattern: patterns.phone \n  },\n  url: { \n    pattern: patterns.url \n  },\n  transactionId: {\n    required: true,\n    pattern: patterns.transactionId,\n    minLength: 1,\n    maxLength: 50\n  },\n  amount: {\n    required: true,\n    min: 0,\n    custom: (value: any) => {\n      if (typeof value !== 'number' || isNaN(value)) {\n        return 'Amount must be a valid number';\n      }\n      return null;\n    }\n  },\n  riskScore: {\n    min: 0,\n    max: 1,\n    custom: (value: any) => {\n      if (value !== undefined && (typeof value !== 'number' || isNaN(value))) {\n        return 'Risk score must be a valid number';\n      }\n      return null;\n    }\n  }\n};\n\n/**\n * Validation schemas for common forms\n */\nexport const schemas = {\n  login: {\n    username: rules.required,\n    password: rules.required\n  },\n  \n  case: {\n    transaction_id: rules.transactionId,\n    tag: rules.required,\n    comment: { maxLength: 500 }\n  },\n  \n  transaction: {\n    transaction_id: rules.transactionId,\n    type: rules.required,\n    amount: rules.amount,\n    nameOrig: rules.required,\n    nameDest: rules.required\n  },\n  \n  user: {\n    username: {\n      required: true,\n      minLength: 3,\n      maxLength: 50,\n      pattern: patterns.alphanumeric\n    },\n    password: rules.password,\n    role: rules.required\n  }\n};\n\n/**\n * Sanitize input data\n */\nexport const sanitize = {\n  /**\n   * Remove HTML tags and dangerous characters\n   */\n  html: (input: string): string => {\n    if (!input) return '';\n    return input\n      .replace(/<[^>]*>/g, '') // Remove HTML tags\n      .replace(/[<>'\"&]/g, '') // Remove dangerous characters\n      .trim();\n  },\n  \n  /**\n   * Sanitize for SQL-like queries (basic protection)\n   */\n  sql: (input: string): string => {\n    if (!input) return '';\n    return input\n      .replace(/[';--]/g, '') // Remove SQL injection patterns\n      .trim();\n  },\n  \n  /**\n   * Sanitize numeric input\n   */\n  number: (input: any): number | null => {\n    if (input === null || input === undefined || input === '') {\n      return null;\n    }\n    \n    const num = Number(input);\n    return isNaN(num) ? null : num;\n  },\n  \n  /**\n   * Sanitize boolean input\n   */\n  boolean: (input: any): boolean => {\n    if (typeof input === 'boolean') return input;\n    if (typeof input === 'string') {\n      return input.toLowerCase() === 'true';\n    }\n    return Boolean(input);\n  },\n  \n  /**\n   * Sanitize array input\n   */\n  array: (input: any): any[] => {\n    if (Array.isArray(input)) return input;\n    if (input === null || input === undefined) return [];\n    return [input];\n  }\n};\n\n/**\n * Data type validators\n */\nexport const isValid = {\n  email: (email: string): boolean => patterns.email.test(email),\n  phone: (phone: string): boolean => patterns.phone.test(phone),\n  url: (url: string): boolean => patterns.url.test(url),\n  date: (date: string): boolean => !isNaN(Date.parse(date)),\n  json: (json: string): boolean => {\n    try {\n      JSON.parse(json);\n      return true;\n    } catch {\n      return false;\n    }\n  },\n  uuid: (uuid: string): boolean => {\n    const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n    return uuidPattern.test(uuid);\n  },\n  creditCard: (cardNumber: string): boolean => {\n    // Luhn algorithm for credit card validation\n    const cleaned = cardNumber.replace(/\\D/g, '');\n    if (cleaned.length < 13 || cleaned.length > 19) return false;\n    \n    let sum = 0;\n    let isEven = false;\n    \n    for (let i = cleaned.length - 1; i >= 0; i--) {\n      let digit = parseInt(cleaned[i]);\n      \n      if (isEven) {\n        digit *= 2;\n        if (digit > 9) {\n          digit -= 9;\n        }\n      }\n      \n      sum += digit;\n      isEven = !isEven;\n    }\n    \n    return sum % 10 === 0;\n  }\n};\n\n/**\n * Form validation hook\n */\nimport { useState, useCallback } from 'react';\n\nexport function useValidation(schema: ValidationSchema) {\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  \n  const validate = useCallback((data: Record<string, any>): boolean => {\n    const result = validateSchema(data, schema);\n    setErrors(result.errors);\n    return result.isValid;\n  }, [schema]);\n  \n  const validateField = useCallback((field: string, value: any): boolean => {\n    const rule = schema[field];\n    if (!rule) return true;\n    \n    const error = validateField(value, rule, field);\n    setErrors(prev => ({\n      ...prev,\n      [field]: error || ''\n    }));\n    \n    return !error;\n  }, [schema]);\n  \n  const clearErrors = useCallback(() => {\n    setErrors({});\n  }, []);\n  \n  const clearFieldError = useCallback((field: string) => {\n    setErrors(prev => {\n      const newErrors = { ...prev };\n      delete newErrors[field];\n      return newErrors;\n    });\n  }, []);\n  \n  return {\n    errors,\n    validate,\n    validateField,\n    clearErrors,\n    clearFieldError,\n    hasErrors: Object.keys(errors).length > 0\n  };\n}\n"], "mappings": ";AAAA;AACA;AACA;;AAqBA;AACA;AACA;AACA,OAAO,MAAMA,aAAa,GAAGA,CAACC,KAAU,EAAEC,IAAoB,EAAEC,SAAiB,KAAoB;EACnG;EACA,IAAID,IAAI,CAACE,QAAQ,KAAKH,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKI,SAAS,IAAIJ,KAAK,KAAK,EAAE,CAAC,EAAE;IAC5E,OAAO,GAAGE,SAAS,cAAc;EACnC;;EAEA;EACA,IAAI,CAACD,IAAI,CAACE,QAAQ,KAAKH,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKI,SAAS,IAAIJ,KAAK,KAAK,EAAE,CAAC,EAAE;IAC7E,OAAO,IAAI;EACb;;EAEA;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAIC,IAAI,CAACI,SAAS,IAAIL,KAAK,CAACM,MAAM,GAAGL,IAAI,CAACI,SAAS,EAAE;MACnD,OAAO,GAAGH,SAAS,qBAAqBD,IAAI,CAACI,SAAS,aAAa;IACrE;IAEA,IAAIJ,IAAI,CAACM,SAAS,IAAIP,KAAK,CAACM,MAAM,GAAGL,IAAI,CAACM,SAAS,EAAE;MACnD,OAAO,GAAGL,SAAS,yBAAyBD,IAAI,CAACM,SAAS,aAAa;IACzE;IAEA,IAAIN,IAAI,CAACO,OAAO,IAAI,CAACP,IAAI,CAACO,OAAO,CAACC,IAAI,CAACT,KAAK,CAAC,EAAE;MAC7C,OAAO,GAAGE,SAAS,oBAAoB;IACzC;EACF;;EAEA;EACA,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAIC,IAAI,CAACS,GAAG,KAAKN,SAAS,IAAIJ,KAAK,GAAGC,IAAI,CAACS,GAAG,EAAE;MAC9C,OAAO,GAAGR,SAAS,qBAAqBD,IAAI,CAACS,GAAG,EAAE;IACpD;IAEA,IAAIT,IAAI,CAACU,GAAG,KAAKP,SAAS,IAAIJ,KAAK,GAAGC,IAAI,CAACU,GAAG,EAAE;MAC9C,OAAO,GAAGT,SAAS,yBAAyBD,IAAI,CAACU,GAAG,EAAE;IACxD;EACF;;EAEA;EACA,IAAIV,IAAI,CAACW,MAAM,EAAE;IACf,OAAOX,IAAI,CAACW,MAAM,CAACZ,KAAK,CAAC;EAC3B;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMa,cAAc,GAAGA,CAACC,IAAyB,EAAEC,MAAwB,KAAuB;EACvG,MAAMC,MAA8B,GAAG,CAAC,CAAC;EAEzC,KAAK,MAAM,CAACC,KAAK,EAAEhB,IAAI,CAAC,IAAIiB,MAAM,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;IAClD,MAAMK,KAAK,GAAGrB,aAAa,CAACe,IAAI,CAACG,KAAK,CAAC,EAAEhB,IAAI,EAAEgB,KAAK,CAAC;IACrD,IAAIG,KAAK,EAAE;MACTJ,MAAM,CAACC,KAAK,CAAC,GAAGG,KAAK;IACvB;EACF;EAEA,OAAO;IACLC,OAAO,EAAEH,MAAM,CAACI,IAAI,CAACN,MAAM,CAAC,CAACV,MAAM,KAAK,CAAC;IACzCU;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMO,QAAQ,GAAG;EACtBC,KAAK,EAAE,4BAA4B;EACnCC,KAAK,EAAE,oBAAoB;EAC3BC,GAAG,EAAE,gBAAgB;EACrBC,YAAY,EAAE,gBAAgB;EAC9BC,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,eAAe;EACxBC,QAAQ,EAAE,uDAAuD;EACjEC,aAAa,EAAE,kBAAkB;EACjCC,aAAa,EAAE;AACjB,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,KAAK,GAAG;EACnB9B,QAAQ,EAAE;IAAEA,QAAQ,EAAE;EAAK,CAAC;EAC5BqB,KAAK,EAAE;IACLrB,QAAQ,EAAE,IAAI;IACdK,OAAO,EAAEe,QAAQ,CAACC;EACpB,CAAC;EACDM,QAAQ,EAAE;IACR3B,QAAQ,EAAE,IAAI;IACdE,SAAS,EAAE,CAAC;IACZG,OAAO,EAAEe,QAAQ,CAACO;EACpB,CAAC;EACDL,KAAK,EAAE;IACLjB,OAAO,EAAEe,QAAQ,CAACE;EACpB,CAAC;EACDC,GAAG,EAAE;IACHlB,OAAO,EAAEe,QAAQ,CAACG;EACpB,CAAC;EACDK,aAAa,EAAE;IACb5B,QAAQ,EAAE,IAAI;IACdK,OAAO,EAAEe,QAAQ,CAACQ,aAAa;IAC/B1B,SAAS,EAAE,CAAC;IACZE,SAAS,EAAE;EACb,CAAC;EACD2B,MAAM,EAAE;IACN/B,QAAQ,EAAE,IAAI;IACdO,GAAG,EAAE,CAAC;IACNE,MAAM,EAAGZ,KAAU,IAAK;MACtB,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAImC,KAAK,CAACnC,KAAK,CAAC,EAAE;QAC7C,OAAO,+BAA+B;MACxC;MACA,OAAO,IAAI;IACb;EACF,CAAC;EACDoC,SAAS,EAAE;IACT1B,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,MAAM,EAAGZ,KAAU,IAAK;MACtB,IAAIA,KAAK,KAAKI,SAAS,KAAK,OAAOJ,KAAK,KAAK,QAAQ,IAAImC,KAAK,CAACnC,KAAK,CAAC,CAAC,EAAE;QACtE,OAAO,mCAAmC;MAC5C;MACA,OAAO,IAAI;IACb;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMqC,OAAO,GAAG;EACrBC,KAAK,EAAE;IACLC,QAAQ,EAAEN,KAAK,CAAC9B,QAAQ;IACxB2B,QAAQ,EAAEG,KAAK,CAAC9B;EAClB,CAAC;EAEDqC,IAAI,EAAE;IACJC,cAAc,EAAER,KAAK,CAACF,aAAa;IACnCW,GAAG,EAAET,KAAK,CAAC9B,QAAQ;IACnBwC,OAAO,EAAE;MAAEpC,SAAS,EAAE;IAAI;EAC5B,CAAC;EAEDqC,WAAW,EAAE;IACXH,cAAc,EAAER,KAAK,CAACF,aAAa;IACnCc,IAAI,EAAEZ,KAAK,CAAC9B,QAAQ;IACpB+B,MAAM,EAAED,KAAK,CAACC,MAAM;IACpBY,QAAQ,EAAEb,KAAK,CAAC9B,QAAQ;IACxB4C,QAAQ,EAAEd,KAAK,CAAC9B;EAClB,CAAC;EAED6C,IAAI,EAAE;IACJT,QAAQ,EAAE;MACRpC,QAAQ,EAAE,IAAI;MACdE,SAAS,EAAE,CAAC;MACZE,SAAS,EAAE,EAAE;MACbC,OAAO,EAAEe,QAAQ,CAACI;IACpB,CAAC;IACDG,QAAQ,EAAEG,KAAK,CAACH,QAAQ;IACxBmB,IAAI,EAAEhB,KAAK,CAAC9B;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM+C,QAAQ,GAAG;EACtB;AACF;AACA;EACEC,IAAI,EAAGC,KAAa,IAAa;IAC/B,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;IACrB,OAAOA,KAAK,CACTC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;IAAA,CACxBA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;IAAA,CACxBC,IAAI,CAAC,CAAC;EACX,CAAC;EAED;AACF;AACA;EACEC,GAAG,EAAGH,KAAa,IAAa;IAC9B,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;IACrB,OAAOA,KAAK,CACTC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;IAAA,CACvBC,IAAI,CAAC,CAAC;EACX,CAAC;EAED;AACF;AACA;EACEE,MAAM,EAAGJ,KAAU,IAAoB;IACrC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKhD,SAAS,IAAIgD,KAAK,KAAK,EAAE,EAAE;MACzD,OAAO,IAAI;IACb;IAEA,MAAMK,GAAG,GAAGC,MAAM,CAACN,KAAK,CAAC;IACzB,OAAOjB,KAAK,CAACsB,GAAG,CAAC,GAAG,IAAI,GAAGA,GAAG;EAChC,CAAC;EAED;AACF;AACA;EACEE,OAAO,EAAGP,KAAU,IAAc;IAChC,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE,OAAOA,KAAK;IAC5C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOA,KAAK,CAACQ,WAAW,CAAC,CAAC,KAAK,MAAM;IACvC;IACA,OAAOC,OAAO,CAACT,KAAK,CAAC;EACvB,CAAC;EAED;AACF;AACA;EACEU,KAAK,EAAGV,KAAU,IAAY;IAC5B,IAAIW,KAAK,CAACC,OAAO,CAACZ,KAAK,CAAC,EAAE,OAAOA,KAAK;IACtC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKhD,SAAS,EAAE,OAAO,EAAE;IACpD,OAAO,CAACgD,KAAK,CAAC;EAChB;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM/B,OAAO,GAAG;EACrBG,KAAK,EAAGA,KAAa,IAAcD,QAAQ,CAACC,KAAK,CAACf,IAAI,CAACe,KAAK,CAAC;EAC7DC,KAAK,EAAGA,KAAa,IAAcF,QAAQ,CAACE,KAAK,CAAChB,IAAI,CAACgB,KAAK,CAAC;EAC7DC,GAAG,EAAGA,GAAW,IAAcH,QAAQ,CAACG,GAAG,CAACjB,IAAI,CAACiB,GAAG,CAAC;EACrDuC,IAAI,EAAGA,IAAY,IAAc,CAAC9B,KAAK,CAAC+B,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC,CAAC;EACzDG,IAAI,EAAGA,IAAY,IAAc;IAC/B,IAAI;MACFC,IAAI,CAACF,KAAK,CAACC,IAAI,CAAC;MAChB,OAAO,IAAI;IACb,CAAC,CAAC,MAAM;MACN,OAAO,KAAK;IACd;EACF,CAAC;EACDE,IAAI,EAAGA,IAAY,IAAc;IAC/B,MAAMC,WAAW,GAAG,4EAA4E;IAChG,OAAOA,WAAW,CAAC9D,IAAI,CAAC6D,IAAI,CAAC;EAC/B,CAAC;EACDE,UAAU,EAAGC,UAAkB,IAAc;IAC3C;IACA,MAAMC,OAAO,GAAGD,UAAU,CAACpB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC7C,IAAIqB,OAAO,CAACpE,MAAM,GAAG,EAAE,IAAIoE,OAAO,CAACpE,MAAM,GAAG,EAAE,EAAE,OAAO,KAAK;IAE5D,IAAIqE,GAAG,GAAG,CAAC;IACX,IAAIC,MAAM,GAAG,KAAK;IAElB,KAAK,IAAIC,CAAC,GAAGH,OAAO,CAACpE,MAAM,GAAG,CAAC,EAAEuE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC5C,IAAIC,KAAK,GAAGC,QAAQ,CAACL,OAAO,CAACG,CAAC,CAAC,CAAC;MAEhC,IAAID,MAAM,EAAE;QACVE,KAAK,IAAI,CAAC;QACV,IAAIA,KAAK,GAAG,CAAC,EAAE;UACbA,KAAK,IAAI,CAAC;QACZ;MACF;MAEAH,GAAG,IAAIG,KAAK;MACZF,MAAM,GAAG,CAACA,MAAM;IAClB;IAEA,OAAOD,GAAG,GAAG,EAAE,KAAK,CAAC;EACvB;AACF,CAAC;;AAED;AACA;AACA;AACA,SAASK,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAE7C,OAAO,SAASC,aAAaA,CAACnE,MAAwB,EAAE;EAAAoE,EAAA;EACtD,MAAM,CAACnE,MAAM,EAAEoE,SAAS,CAAC,GAAGJ,QAAQ,CAAyB,CAAC,CAAC,CAAC;EAEhE,MAAMK,QAAQ,GAAGJ,WAAW,CAAEnE,IAAyB,IAAc;IACnE,MAAMwE,MAAM,GAAGzE,cAAc,CAACC,IAAI,EAAEC,MAAM,CAAC;IAC3CqE,SAAS,CAACE,MAAM,CAACtE,MAAM,CAAC;IACxB,OAAOsE,MAAM,CAACjE,OAAO;EACvB,CAAC,EAAE,CAACN,MAAM,CAAC,CAAC;EAEZ,MAAMhB,aAAa,GAAGkF,WAAW,CAAC,CAAChE,KAAa,EAAEjB,KAAU,KAAc;IACxE,MAAMC,IAAI,GAAGc,MAAM,CAACE,KAAK,CAAC;IAC1B,IAAI,CAAChB,IAAI,EAAE,OAAO,IAAI;IAEtB,MAAMmB,KAAK,GAAGrB,aAAa,CAACC,KAAK,EAAEC,IAAI,EAAEgB,KAAK,CAAC;IAC/CmE,SAAS,CAACG,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACtE,KAAK,GAAGG,KAAK,IAAI;IACpB,CAAC,CAAC,CAAC;IAEH,OAAO,CAACA,KAAK;EACf,CAAC,EAAE,CAACL,MAAM,CAAC,CAAC;EAEZ,MAAMyE,WAAW,GAAGP,WAAW,CAAC,MAAM;IACpCG,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,eAAe,GAAGR,WAAW,CAAEhE,KAAa,IAAK;IACrDmE,SAAS,CAACG,IAAI,IAAI;MAChB,MAAMG,SAAS,GAAG;QAAE,GAAGH;MAAK,CAAC;MAC7B,OAAOG,SAAS,CAACzE,KAAK,CAAC;MACvB,OAAOyE,SAAS;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACL1E,MAAM;IACNqE,QAAQ;IACRtF,aAAa;IACbyF,WAAW;IACXC,eAAe;IACfE,SAAS,EAAEzE,MAAM,CAACI,IAAI,CAACN,MAAM,CAAC,CAACV,MAAM,GAAG;EAC1C,CAAC;AACH;AAAC6E,EAAA,CA1CeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}