{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\pages\\\\CaseManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { fetchCases, createCase, updateCase } from '../services/api';\nimport CaseTable from '../components/CaseTable';\nimport CaseForm from '../components/CaseForm';\nimport '../styles/CaseManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CaseManagement = () => {\n  _s();\n  const [cases, setCases] = useState([]);\n  const [selectedCase, setSelectedCase] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [filter, setFilter] = useState({\n    status: 'all'\n  });\n  const [isFormOpen, setIsFormOpen] = useState(false);\n  useEffect(() => {\n    loadCases();\n  }, [filter]);\n  const loadCases = async () => {\n    setIsLoading(true);\n    try {\n      const data = await fetchCases(filter);\n      setCases(data);\n    } catch (error) {\n      console.error('Error fetching cases:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleCaseSelect = caseItem => {\n    setSelectedCase(caseItem);\n    setIsFormOpen(true);\n  };\n  const handleFilterChange = e => {\n    setFilter({\n      ...filter,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleCreateCase = async caseData => {\n    try {\n      await createCase(caseData);\n      loadCases();\n      setIsFormOpen(false);\n    } catch (error) {\n      console.error('Error creating case:', error);\n    }\n  };\n  const handleUpdateCase = async (caseId, caseData) => {\n    try {\n      await updateCase(caseId, caseData);\n      loadCases();\n      setIsFormOpen(false);\n      setSelectedCase(null);\n    } catch (error) {\n      console.error('Error updating case:', error);\n    }\n  };\n  const handleNewCase = () => {\n    setSelectedCase(null);\n    setIsFormOpen(true);\n  };\n  const handleCloseForm = () => {\n    setIsFormOpen(false);\n    setSelectedCase(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"case-management-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"case-management-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Case Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"case-management-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"primary-button\",\n          onClick: handleNewCase,\n          children: \"New Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"statusFilter\",\n            children: \"Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"statusFilter\",\n            name: \"status\",\n            value: filter.status,\n            onChange: handleFilterChange,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"open\",\n              children: \"Open\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"closed\",\n              children: \"Closed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"pending\",\n              children: \"Pending\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"case-management-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(CaseTable, {\n          cases: cases,\n          onSelectCase: handleCaseSelect,\n          isLoading: isLoading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), isFormOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"case-form-modal\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"case-form-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-button\",\n          onClick: handleCloseForm,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CaseForm, {\n          initialCase: selectedCase,\n          onSubmit: selectedCase ? data => handleUpdateCase(selectedCase.id, data) : handleCreateCase\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s(CaseManagement, \"cZV/P2OwBhi7LF4nv2VlKqlf7Ms=\");\n_c = CaseManagement;\nexport default CaseManagement;\nvar _c;\n$RefreshReg$(_c, \"CaseManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "fetchCases", "createCase", "updateCase", "CaseTable", "CaseForm", "jsxDEV", "_jsxDEV", "CaseManagement", "_s", "cases", "setCases", "selectedCase", "setSelectedCase", "isLoading", "setIsLoading", "filter", "setFilter", "status", "isFormOpen", "setIsFormOpen", "loadCases", "data", "error", "console", "handleCaseSelect", "caseItem", "handleFilterChange", "e", "target", "name", "value", "handleCreateCase", "caseData", "handleUpdateCase", "caseId", "handleNewCase", "handleCloseForm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "htmlFor", "id", "onChange", "onSelectCase", "initialCase", "onSubmit", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/pages/CaseManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { fetchCases, createCase, updateCase } from '../services/api';\nimport CaseTable from '../components/CaseTable';\nimport CaseForm from '../components/CaseForm';\nimport '../styles/CaseManagement.css';\n\nconst CaseManagement = () => {\n  const [cases, setCases] = useState([]);\n  const [selectedCase, setSelectedCase] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [filter, setFilter] = useState({ status: 'all' });\n  const [isFormOpen, setIsFormOpen] = useState(false);\n\n  useEffect(() => {\n    loadCases();\n  }, [filter]);\n\n  const loadCases = async () => {\n    setIsLoading(true);\n    try {\n      const data = await fetchCases(filter);\n      setCases(data);\n    } catch (error) {\n      console.error('Error fetching cases:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCaseSelect = (caseItem) => {\n    setSelectedCase(caseItem);\n    setIsFormOpen(true);\n  };\n\n  const handleFilterChange = (e) => {\n    setFilter({\n      ...filter,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleCreateCase = async (caseData) => {\n    try {\n      await createCase(caseData);\n      loadCases();\n      setIsFormOpen(false);\n    } catch (error) {\n      console.error('Error creating case:', error);\n    }\n  };\n\n  const handleUpdateCase = async (caseId, caseData) => {\n    try {\n      await updateCase(caseId, caseData);\n      loadCases();\n      setIsFormOpen(false);\n      setSelectedCase(null);\n    } catch (error) {\n      console.error('Error updating case:', error);\n    }\n  };\n\n  const handleNewCase = () => {\n    setSelectedCase(null);\n    setIsFormOpen(true);\n  };\n\n  const handleCloseForm = () => {\n    setIsFormOpen(false);\n    setSelectedCase(null);\n  };\n\n  return (\n    <div className=\"case-management-container\">\n      <div className=\"case-management-header\">\n        <h1>Case Management</h1>\n        <div className=\"case-management-actions\">\n          <button className=\"primary-button\" onClick={handleNewCase}>\n            New Case\n          </button>\n          <div className=\"filter-controls\">\n            <label htmlFor=\"statusFilter\">Status:</label>\n            <select \n              id=\"statusFilter\" \n              name=\"status\" \n              value={filter.status} \n              onChange={handleFilterChange}\n            >\n              <option value=\"all\">All</option>\n              <option value=\"open\">Open</option>\n              <option value=\"closed\">Closed</option>\n              <option value=\"pending\">Pending</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"case-management-content\">\n        <div className=\"card\">\n          <CaseTable \n            cases={cases} \n            onSelectCase={handleCaseSelect}\n            isLoading={isLoading}\n          />\n        </div>\n      </div>\n\n      {isFormOpen && (\n        <div className=\"case-form-modal\">\n          <div className=\"case-form-container\">\n            <button className=\"close-button\" onClick={handleCloseForm}>×</button>\n            <CaseForm \n              initialCase={selectedCase}\n              onSubmit={selectedCase ? \n                (data) => handleUpdateCase(selectedCase.id, data) : \n                handleCreateCase\n              }\n            />\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CaseManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,UAAU,EAAEC,UAAU,QAAQ,iBAAiB;AACpE,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiB,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAC;IAAEmB,MAAM,EAAE;EAAM,CAAC,CAAC;EACvD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACdqB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACL,MAAM,CAAC,CAAC;EAEZ,MAAMK,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BN,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMO,IAAI,GAAG,MAAMrB,UAAU,CAACe,MAAM,CAAC;MACrCL,QAAQ,CAACW,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRR,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMU,gBAAgB,GAAIC,QAAQ,IAAK;IACrCb,eAAe,CAACa,QAAQ,CAAC;IACzBN,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMO,kBAAkB,GAAIC,CAAC,IAAK;IAChCX,SAAS,CAAC;MACR,GAAGD,MAAM;MACT,CAACY,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAOC,QAAQ,IAAK;IAC3C,IAAI;MACF,MAAM/B,UAAU,CAAC+B,QAAQ,CAAC;MAC1BZ,SAAS,CAAC,CAAC;MACXD,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAMW,gBAAgB,GAAG,MAAAA,CAAOC,MAAM,EAAEF,QAAQ,KAAK;IACnD,IAAI;MACF,MAAM9B,UAAU,CAACgC,MAAM,EAAEF,QAAQ,CAAC;MAClCZ,SAAS,CAAC,CAAC;MACXD,aAAa,CAAC,KAAK,CAAC;MACpBP,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAMa,aAAa,GAAGA,CAAA,KAAM;IAC1BvB,eAAe,CAAC,IAAI,CAAC;IACrBO,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMiB,eAAe,GAAGA,CAAA,KAAM;IAC5BjB,aAAa,CAAC,KAAK,CAAC;IACpBP,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,oBACEN,OAAA;IAAK+B,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxChC,OAAA;MAAK+B,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrChC,OAAA;QAAAgC,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBpC,OAAA;QAAK+B,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtChC,OAAA;UAAQ+B,SAAS,EAAC,gBAAgB;UAACM,OAAO,EAAER,aAAc;UAAAG,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpC,OAAA;UAAK+B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BhC,OAAA;YAAOsC,OAAO,EAAC,cAAc;YAAAN,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7CpC,OAAA;YACEuC,EAAE,EAAC,cAAc;YACjBhB,IAAI,EAAC,QAAQ;YACbC,KAAK,EAAEf,MAAM,CAACE,MAAO;YACrB6B,QAAQ,EAAEpB,kBAAmB;YAAAY,QAAA,gBAE7BhC,OAAA;cAAQwB,KAAK,EAAC,KAAK;cAAAQ,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChCpC,OAAA;cAAQwB,KAAK,EAAC,MAAM;cAAAQ,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClCpC,OAAA;cAAQwB,KAAK,EAAC,QAAQ;cAAAQ,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCpC,OAAA;cAAQwB,KAAK,EAAC,SAAS;cAAAQ,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpC,OAAA;MAAK+B,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtChC,OAAA;QAAK+B,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBhC,OAAA,CAACH,SAAS;UACRM,KAAK,EAAEA,KAAM;UACbsC,YAAY,EAAEvB,gBAAiB;UAC/BX,SAAS,EAAEA;QAAU;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELxB,UAAU,iBACTZ,OAAA;MAAK+B,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BhC,OAAA;QAAK+B,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClChC,OAAA;UAAQ+B,SAAS,EAAC,cAAc;UAACM,OAAO,EAAEP,eAAgB;UAAAE,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACrEpC,OAAA,CAACF,QAAQ;UACP4C,WAAW,EAAErC,YAAa;UAC1BsC,QAAQ,EAAEtC,YAAY,GACnBU,IAAI,IAAKY,gBAAgB,CAACtB,YAAY,CAACkC,EAAE,EAAExB,IAAI,CAAC,GACjDU;QACD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClC,EAAA,CArHID,cAAc;AAAA2C,EAAA,GAAd3C,cAAc;AAuHpB,eAAeA,cAAc;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}