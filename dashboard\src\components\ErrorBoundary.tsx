import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

/**
 * Error Boundary component to catch and handle React errors
 */
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // In production, you might want to send this to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to error reporting service
      // errorReportingService.captureException(error, { extra: errorInfo });
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI
      return (
        <div className="error-boundary">
          <div className="error-boundary-content">
            <h2>Something went wrong</h2>
            <p>We're sorry, but something unexpected happened.</p>
            
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="error-details">
                <summary>Error Details (Development Only)</summary>
                <pre className="error-stack">
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </details>
            )}
            
            <div className="error-actions">
              <button 
                onClick={this.handleRetry}
                className="retry-button"
              >
                Try Again
              </button>
              <button 
                onClick={() => window.location.reload()}
                className="reload-button"
              >
                Reload Page
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook-based error boundary for functional components
 */
import { useState, useEffect } from 'react';

interface UseErrorHandlerReturn {
  error: Error | null;
  resetError: () => void;
  captureError: (error: Error) => void;
}

export function useErrorHandler(): UseErrorHandlerReturn {
  const [error, setError] = useState<Error | null>(null);

  const resetError = () => {
    setError(null);
  };

  const captureError = (error: Error) => {
    setError(error);
    console.error('Error captured:', error);
  };

  // Reset error when component unmounts
  useEffect(() => {
    return () => {
      setError(null);
    };
  }, []);

  return { error, resetError, captureError };
}

/**
 * Higher-order component to wrap components with error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: ErrorInfo) => void
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback} onError={onError}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Async error boundary for handling promise rejections
 */
export function useAsyncError() {
  const [, setError] = useState();
  
  return (error: Error) => {
    setError(() => {
      throw error;
    });
  };
}

/**
 * Error fallback components
 */
export const ErrorFallback: React.FC<{
  error?: Error;
  resetError?: () => void;
  message?: string;
}> = ({ error, resetError, message = "Something went wrong" }) => (
  <div className="error-fallback">
    <h3>{message}</h3>
    {error && (
      <p className="error-message">{error.message}</p>
    )}
    {resetError && (
      <button onClick={resetError} className="retry-button">
        Try Again
      </button>
    )}
  </div>
);

export const LoadingErrorFallback: React.FC<{
  error?: Error;
  retry?: () => void;
}> = ({ error, retry }) => (
  <div className="loading-error-fallback">
    <div className="error-icon">⚠️</div>
    <h3>Failed to Load</h3>
    <p>{error?.message || "Unable to load the requested data"}</p>
    {retry && (
      <button onClick={retry} className="retry-button">
        Retry
      </button>
    )}
  </div>
);

export const NetworkErrorFallback: React.FC<{
  retry?: () => void;
}> = ({ retry }) => (
  <div className="network-error-fallback">
    <div className="error-icon">🌐</div>
    <h3>Network Error</h3>
    <p>Please check your internet connection and try again.</p>
    {retry && (
      <button onClick={retry} className="retry-button">
        Retry
      </button>
    )}
  </div>
);

/**
 * Global error handler for unhandled promise rejections
 */
export const setupGlobalErrorHandlers = () => {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    
    // Prevent the default browser behavior
    event.preventDefault();
    
    // You can send this to an error reporting service
    // errorReportingService.captureException(event.reason);
  });

  // Handle general JavaScript errors
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    
    // You can send this to an error reporting service
    // errorReportingService.captureException(event.error);
  });
};

/**
 * Error reporting utilities
 */
export const errorReporting = {
  captureException: (error: Error, context?: Record<string, any>) => {
    console.error('Error captured:', error, context);
    
    // In production, send to error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example implementations:
      // Sentry.captureException(error, { extra: context });
      // LogRocket.captureException(error);
      // Bugsnag.notify(error, context);
    }
  },

  captureMessage: (message: string, level: 'info' | 'warning' | 'error' = 'info') => {
    console[level]('Message captured:', message);
    
    // In production, send to error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry.captureMessage(message, level);
    }
  },

  setUserContext: (user: { id: string; username: string; email?: string }) => {
    // Set user context for error reporting
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry.setUser(user);
    }
  },

  addBreadcrumb: (message: string, category?: string, data?: Record<string, any>) => {
    // Add breadcrumb for debugging
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry.addBreadcrumb({ message, category, data });
    }
  }
};

export default ErrorBoundary;
