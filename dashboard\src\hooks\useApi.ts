import { useState, useEffect, useCallback } from 'react';
import { ApiError } from '../types';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: ApiError | null;
}

interface UseApiOptions {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: ApiError) => void;
}

/**
 * Custom hook for handling API calls with loading, error, and success states
 */
export function useApi<T>(
  apiFunction: (...args: any[]) => Promise<T>,
  options: UseApiOptions = {}
) {
  const { immediate = false, onSuccess, onError } = options;
  
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null
  });

  const execute = useCallback(async (...args: any[]): Promise<T | null> => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const result = await apiFunction(...args);
      setState({ data: result, loading: false, error: null });
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (error) {
      const apiError = error as ApiError;
      setState(prev => ({ ...prev, loading: false, error: apiError }));
      
      if (onError) {
        onError(apiError);
      }
      
      return null;
    }
  }, [apiFunction, onSuccess, onError]);

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null });
  }, []);

  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [immediate, execute]);

  return {
    ...state,
    execute,
    reset
  };
}

/**
 * Hook for handling paginated API calls
 */
export function usePaginatedApi<T>(
  apiFunction: (page: number, size: number, ...args: any[]) => Promise<{ items: T[]; total: number; pages: number }>,
  initialPage: number = 1,
  pageSize: number = 20
) {
  const [page, setPage] = useState(initialPage);
  const [items, setItems] = useState<T[]>([]);
  const [total, setTotal] = useState(0);
  const [pages, setPages] = useState(0);

  const { loading, error, execute } = useApi(apiFunction, {
    onSuccess: (data) => {
      setItems(data.items);
      setTotal(data.total);
      setPages(data.pages);
    }
  });

  const loadPage = useCallback((newPage: number, ...args: any[]) => {
    setPage(newPage);
    return execute(newPage, pageSize, ...args);
  }, [execute, pageSize]);

  const nextPage = useCallback((...args: any[]) => {
    if (page < pages) {
      return loadPage(page + 1, ...args);
    }
    return Promise.resolve(null);
  }, [page, pages, loadPage]);

  const prevPage = useCallback((...args: any[]) => {
    if (page > 1) {
      return loadPage(page - 1, ...args);
    }
    return Promise.resolve(null);
  }, [page, loadPage]);

  const refresh = useCallback((...args: any[]) => {
    return loadPage(page, ...args);
  }, [page, loadPage]);

  return {
    items,
    total,
    pages,
    page,
    loading,
    error,
    loadPage,
    nextPage,
    prevPage,
    refresh,
    hasNext: page < pages,
    hasPrev: page > 1
  };
}

/**
 * Hook for handling form submissions with API calls
 */
export function useApiForm<T, R>(
  apiFunction: (data: T) => Promise<R>,
  options: UseApiOptions & {
    resetOnSuccess?: boolean;
    validateFn?: (data: T) => Record<string, string> | null;
  } = {}
) {
  const { resetOnSuccess = false, validateFn, ...apiOptions } = options;
  const [formData, setFormData] = useState<Partial<T>>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const { loading, error, execute, reset: resetApi } = useApi(apiFunction, {
    ...apiOptions,
    onSuccess: (data) => {
      if (resetOnSuccess) {
        setFormData({});
        setValidationErrors({});
      }
      if (apiOptions.onSuccess) {
        apiOptions.onSuccess(data);
      }
    }
  });

  const updateField = useCallback((field: keyof T, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear validation error for this field
    if (validationErrors[field as string]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field as string];
        return newErrors;
      });
    }
  }, [validationErrors]);

  const submit = useCallback(async (data?: T): Promise<R | null> => {
    const submitData = data || (formData as T);
    
    // Validate if validation function is provided
    if (validateFn) {
      const errors = validateFn(submitData);
      if (errors) {
        setValidationErrors(errors);
        return null;
      }
    }
    
    setValidationErrors({});
    return execute(submitData);
  }, [formData, validateFn, execute]);

  const reset = useCallback(() => {
    setFormData({});
    setValidationErrors({});
    resetApi();
  }, [resetApi]);

  return {
    formData,
    validationErrors,
    loading,
    error,
    updateField,
    submit,
    reset,
    setFormData
  };
}

/**
 * Hook for handling real-time data updates
 */
export function useRealTimeData<T>(
  initialData: T[],
  maxItems: number = 1000
) {
  const [data, setData] = useState<T[]>(initialData);

  const addItem = useCallback((item: T) => {
    setData(prev => [item, ...prev].slice(0, maxItems));
  }, [maxItems]);

  const addItems = useCallback((items: T[]) => {
    setData(prev => [...items, ...prev].slice(0, maxItems));
  }, [maxItems]);

  const updateItem = useCallback((predicate: (item: T) => boolean, updater: (item: T) => T) => {
    setData(prev => prev.map(item => predicate(item) ? updater(item) : item));
  }, []);

  const removeItem = useCallback((predicate: (item: T) => boolean) => {
    setData(prev => prev.filter(item => !predicate(item)));
  }, []);

  const clear = useCallback(() => {
    setData([]);
  }, []);

  return {
    data,
    addItem,
    addItems,
    updateItem,
    removeItem,
    clear,
    setData
  };
}
