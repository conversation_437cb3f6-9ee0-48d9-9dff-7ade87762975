{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\fraud-platform\\\\dashboard\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport Login from './pages/Login.tsx';\nimport Dashboard from './pages/Dashboard.tsx';\nimport CaseManagement from './pages/CaseManagement.tsx';\nimport Analytics from './pages/Analytics.tsx';\nimport Navbar from './components/Navbar.tsx';\nimport ProtectedRoute from './components/ProtectedRoute.tsx';\nimport { AuthProvider } from './services/AuthContext.tsx';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"app\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"app-container\",\n                children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 22,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"content\",\n                  children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 24,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 23,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cases\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"app-container\",\n                children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"content\",\n                  children: /*#__PURE__*/_jsxDEV(CaseManagement, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 34,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 33,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/analytics\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"app-container\",\n                children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 42,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"content\",\n                  children: /*#__PURE__*/_jsxDEV(Analytics, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 44,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 43,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON><PERSON>", "Dashboard", "CaseManagement", "Analytics", "<PERSON><PERSON><PERSON>", "ProtectedRoute", "<PERSON>th<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport Login from './pages/Login.tsx';\nimport Dashboard from './pages/Dashboard.tsx';\nimport CaseManagement from './pages/CaseManagement.tsx';\nimport Analytics from './pages/Analytics.tsx';\nimport Navbar from './components/Navbar.tsx';\nimport ProtectedRoute from './components/ProtectedRoute.tsx';\nimport { AuthProvider } from './services/AuthContext.tsx';\nimport './App.css';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"app\">\n          <Routes>\n            <Route path=\"/login\" element={<Login />} />\n            <Route path=\"/\" element={\n              <ProtectedRoute>\n                <div className=\"app-container\">\n                  <Navbar />\n                  <div className=\"content\">\n                    <Dashboard />\n                  </div>\n                </div>\n              </ProtectedRoute>\n            } />\n            <Route path=\"/cases\" element={\n              <ProtectedRoute>\n                <div className=\"app-container\">\n                  <Navbar />\n                  <div className=\"content\">\n                    <CaseManagement />\n                  </div>\n                </div>\n              </ProtectedRoute>\n            } />\n            <Route path=\"/analytics\" element={\n              <ProtectedRoute>\n                <div className=\"app-container\">\n                  <Navbar />\n                  <div className=\"content\">\n                    <Analytics />\n                  </div>\n                </div>\n              </ProtectedRoute>\n            } />\n            <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n          </Routes>\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,SAASC,YAAY,QAAQ,4BAA4B;AACzD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACF,YAAY;IAAAI,QAAA,eACXF,OAAA,CAACZ,MAAM;MAAAc,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,KAAK;QAAAD,QAAA,eAClBF,OAAA,CAACX,MAAM;UAAAa,QAAA,gBACLF,OAAA,CAACV,KAAK;YAACc,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEL,OAAA,CAACR,KAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CT,OAAA,CAACV,KAAK;YAACc,IAAI,EAAC,GAAG;YAACC,OAAO,eACrBL,OAAA,CAACH,cAAc;cAAAK,QAAA,eACbF,OAAA;gBAAKG,SAAS,EAAC,eAAe;gBAAAD,QAAA,gBAC5BF,OAAA,CAACJ,MAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACVT,OAAA;kBAAKG,SAAS,EAAC,SAAS;kBAAAD,QAAA,eACtBF,OAAA,CAACP,SAAS;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJT,OAAA,CAACV,KAAK;YAACc,IAAI,EAAC,QAAQ;YAACC,OAAO,eAC1BL,OAAA,CAACH,cAAc;cAAAK,QAAA,eACbF,OAAA;gBAAKG,SAAS,EAAC,eAAe;gBAAAD,QAAA,gBAC5BF,OAAA,CAACJ,MAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACVT,OAAA;kBAAKG,SAAS,EAAC,SAAS;kBAAAD,QAAA,eACtBF,OAAA,CAACN,cAAc;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJT,OAAA,CAACV,KAAK;YAACc,IAAI,EAAC,YAAY;YAACC,OAAO,eAC9BL,OAAA,CAACH,cAAc;cAAAK,QAAA,eACbF,OAAA;gBAAKG,SAAS,EAAC,eAAe;gBAAAD,QAAA,gBAC5BF,OAAA,CAACJ,MAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACVT,OAAA;kBAAKG,SAAS,EAAC,SAAS;kBAAAD,QAAA,eACtBF,OAAA,CAACL,SAAS;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJT,OAAA,CAACV,KAAK;YAACc,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEL,OAAA,CAACT,QAAQ;cAACmB,EAAE,EAAC,GAAG;cAACC,OAAO;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACG,EAAA,GA3CQX,GAAG;AA6CZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}