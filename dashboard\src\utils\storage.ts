/**
 * Utility functions for localStorage management with TypeScript support
 */

export class StorageManager {
  private prefix: string;

  constructor(prefix: string = 'fraud-platform') {
    this.prefix = prefix;
  }

  private getKey(key: string): string {
    return `${this.prefix}:${key}`;
  }

  /**
   * Store data in localStorage with JSON serialization
   */
  set<T>(key: string, value: T): boolean {
    try {
      const serializedValue = JSON.stringify(value);
      localStorage.setItem(this.getKey(key), serializedValue);
      return true;
    } catch (error) {
      console.error('Error storing data in localStorage:', error);
      return false;
    }
  }

  /**
   * Retrieve data from localStorage with JSON deserialization
   */
  get<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = localStorage.getItem(this.getKey(key));
      if (item === null) {
        return defaultValue || null;
      }
      return JSON.parse(item) as T;
    } catch (error) {
      console.error('Error retrieving data from localStorage:', error);
      return defaultValue || null;
    }
  }

  /**
   * Remove item from localStorage
   */
  remove(key: string): boolean {
    try {
      localStorage.removeItem(this.getKey(key));
      return true;
    } catch (error) {
      console.error('Error removing data from localStorage:', error);
      return false;
    }
  }

  /**
   * Clear all items with the current prefix
   */
  clear(): boolean {
    try {
      const keys = Object.keys(localStorage);
      const prefixedKeys = keys.filter(key => key.startsWith(`${this.prefix}:`));
      
      prefixedKeys.forEach(key => {
        localStorage.removeItem(key);
      });
      
      return true;
    } catch (error) {
      console.error('Error clearing localStorage:', error);
      return false;
    }
  }

  /**
   * Check if a key exists in localStorage
   */
  exists(key: string): boolean {
    return localStorage.getItem(this.getKey(key)) !== null;
  }

  /**
   * Get all keys with the current prefix
   */
  getAllKeys(): string[] {
    try {
      const keys = Object.keys(localStorage);
      return keys
        .filter(key => key.startsWith(`${this.prefix}:`))
        .map(key => key.replace(`${this.prefix}:`, ''));
    } catch (error) {
      console.error('Error getting localStorage keys:', error);
      return [];
    }
  }

  /**
   * Get storage size in bytes
   */
  getSize(): number {
    try {
      let total = 0;
      const keys = Object.keys(localStorage);
      const prefixedKeys = keys.filter(key => key.startsWith(`${this.prefix}:`));
      
      prefixedKeys.forEach(key => {
        const value = localStorage.getItem(key);
        if (value) {
          total += key.length + value.length;
        }
      });
      
      return total;
    } catch (error) {
      console.error('Error calculating localStorage size:', error);
      return 0;
    }
  }
}

// Create default instance
export const storage = new StorageManager();

/**
 * Hook for using localStorage with React state
 */
import { useState, useEffect, useCallback } from 'react';

export function useLocalStorage<T>(
  key: string,
  defaultValue: T,
  storageManager: StorageManager = storage
): [T, (value: T) => void, () => void] {
  const [value, setValue] = useState<T>(() => {
    return storageManager.get<T>(key, defaultValue);
  });

  const setStoredValue = useCallback((newValue: T) => {
    setValue(newValue);
    storageManager.set(key, newValue);
  }, [key, storageManager]);

  const removeStoredValue = useCallback(() => {
    setValue(defaultValue);
    storageManager.remove(key);
  }, [key, defaultValue, storageManager]);

  // Listen for storage changes from other tabs
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === storageManager['getKey'](key)) {
        if (e.newValue === null) {
          setValue(defaultValue);
        } else {
          try {
            setValue(JSON.parse(e.newValue));
          } catch (error) {
            console.error('Error parsing storage value:', error);
          }
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key, defaultValue, storageManager]);

  return [value, setStoredValue, removeStoredValue];
}

/**
 * Utility functions for specific data types
 */
export const authStorage = {
  getToken: (): string | null => storage.get<string>('token'),
  setToken: (token: string): boolean => storage.set('token', token),
  removeToken: (): boolean => storage.remove('token'),
  
  getUser: (): any | null => storage.get('user'),
  setUser: (user: any): boolean => storage.set('user', user),
  removeUser: (): boolean => storage.remove('user'),
  
  clearAuth: (): boolean => {
    storage.remove('token');
    storage.remove('user');
    return true;
  }
};

export const settingsStorage = {
  getTheme: (): string => storage.get<string>('theme', 'light'),
  setTheme: (theme: string): boolean => storage.set('theme', theme),
  
  getLanguage: (): string => storage.get<string>('language', 'en'),
  setLanguage: (language: string): boolean => storage.set('language', language),
  
  getNotifications: (): boolean => storage.get<boolean>('notifications', true),
  setNotifications: (enabled: boolean): boolean => storage.set('notifications', enabled),
  
  getDashboardLayout: (): any => storage.get('dashboardLayout', {}),
  setDashboardLayout: (layout: any): boolean => storage.set('dashboardLayout', layout)
};

export const cacheStorage = {
  set: <T>(key: string, data: T, ttl: number = 3600000): boolean => { // 1 hour default TTL
    const item = {
      data,
      timestamp: Date.now(),
      ttl
    };
    return storage.set(`cache:${key}`, item);
  },
  
  get: <T>(key: string): T | null => {
    const item = storage.get<{data: T; timestamp: number; ttl: number}>(`cache:${key}`);
    
    if (!item) {
      return null;
    }
    
    // Check if item has expired
    if (Date.now() - item.timestamp > item.ttl) {
      storage.remove(`cache:${key}`);
      return null;
    }
    
    return item.data;
  },
  
  remove: (key: string): boolean => storage.remove(`cache:${key}`),
  
  clear: (): boolean => {
    const keys = storage.getAllKeys();
    const cacheKeys = keys.filter(key => key.startsWith('cache:'));
    
    cacheKeys.forEach(key => {
      storage.remove(key);
    });
    
    return true;
  }
};

/**
 * Session storage utilities (for temporary data)
 */
export class SessionStorageManager {
  private prefix: string;

  constructor(prefix: string = 'fraud-platform') {
    this.prefix = prefix;
  }

  private getKey(key: string): string {
    return `${this.prefix}:${key}`;
  }

  set<T>(key: string, value: T): boolean {
    try {
      const serializedValue = JSON.stringify(value);
      sessionStorage.setItem(this.getKey(key), serializedValue);
      return true;
    } catch (error) {
      console.error('Error storing data in sessionStorage:', error);
      return false;
    }
  }

  get<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = sessionStorage.getItem(this.getKey(key));
      if (item === null) {
        return defaultValue || null;
      }
      return JSON.parse(item) as T;
    } catch (error) {
      console.error('Error retrieving data from sessionStorage:', error);
      return defaultValue || null;
    }
  }

  remove(key: string): boolean {
    try {
      sessionStorage.removeItem(this.getKey(key));
      return true;
    } catch (error) {
      console.error('Error removing data from sessionStorage:', error);
      return false;
    }
  }

  clear(): boolean {
    try {
      const keys = Object.keys(sessionStorage);
      const prefixedKeys = keys.filter(key => key.startsWith(`${this.prefix}:`));
      
      prefixedKeys.forEach(key => {
        sessionStorage.removeItem(key);
      });
      
      return true;
    } catch (error) {
      console.error('Error clearing sessionStorage:', error);
      return false;
    }
  }
}

export const sessionStorage = new SessionStorageManager();
