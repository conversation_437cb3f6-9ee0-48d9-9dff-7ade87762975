import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './services/AuthContext';
import { WebSocketProvider } from './services/WebSocketContext';
import ErrorBoundary, { setupGlobalErrorHandlers } from './components/ErrorBoundary';
import ProtectedRoute from './components/ProtectedRoute';
import Navbar from './components/Navbar';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import CaseManagement from './pages/CaseManagement';
import Analytics from './pages/Analytics';
import Unauthorized from './pages/Unauthorized';
import { UserRole } from './types';
import './App.css';

// Setup global error handlers
setupGlobalErrorHandlers();

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <WebSocketProvider>
          <Router>
            <div className="app">
              <Routes>
                <Route path="/login" element={<Login />} />
                <Route path="/unauthorized" element={<Unauthorized />} />

                <Route path="/" element={
                  <ProtectedRoute>
                    <div className="app-container">
                      <Navbar />
                      <div className="content">
                        <Dashboard />
                      </div>
                    </div>
                  </ProtectedRoute>
                } />

                <Route path="/cases" element={
                  <ProtectedRoute>
                    <div className="app-container">
                      <Navbar />
                      <div className="content">
                        <CaseManagement />
                      </div>
                    </div>
                  </ProtectedRoute>
                } />

                <Route path="/analytics" element={
                  <ProtectedRoute requiredRole={UserRole.ANALYST}>
                    <div className="app-container">
                      <Navbar />
                      <div className="content">
                        <Analytics />
                      </div>
                    </div>
                  </ProtectedRoute>
                } />

                {/* Catch all route */}
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </div>
          </Router>
        </WebSocketProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
};

export default App;
