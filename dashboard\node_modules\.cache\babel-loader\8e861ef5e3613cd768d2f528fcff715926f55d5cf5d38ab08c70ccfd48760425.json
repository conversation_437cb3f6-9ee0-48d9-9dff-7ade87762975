{"ast": null, "code": "/**\n * Core type definitions for the Fraud Detection Platform\n */\n\n// User and Authentication Types\n\nexport let UserRole = /*#__PURE__*/function (UserRole) {\n  UserRole[\"ANALYST\"] = \"analyst\";\n  UserRole[\"AUDITOR\"] = \"auditor\";\n  UserRole[\"ADMIN\"] = \"admin\";\n  return UserRole;\n}({});\n\n// Transaction Types\n\nexport let TransactionType = /*#__PURE__*/function (TransactionType) {\n  TransactionType[\"PAYMENT\"] = \"PAYMENT\";\n  TransactionType[\"TRANSFER\"] = \"TRANSFER\";\n  TransactionType[\"CASH_OUT\"] = \"CASH_OUT\";\n  TransactionType[\"DEBIT\"] = \"DEBIT\";\n  TransactionType[\"CASH_IN\"] = \"CASH_IN\";\n  return TransactionType;\n}({});\n\n// Case Management Types\n\nexport let CaseStatus = /*#__PURE__*/function (CaseStatus) {\n  CaseStatus[\"OPEN\"] = \"open\";\n  CaseStatus[\"CLOSED\"] = \"closed\";\n  CaseStatus[\"PENDING\"] = \"pending\";\n  return CaseStatus;\n}({});\nexport let CaseTag = /*#__PURE__*/function (CaseTag) {\n  CaseTag[\"FALSE_POSITIVE\"] = \"FP\";\n  CaseTag[\"CONFIRMED\"] = \"CONFIRMED\";\n  CaseTag[\"SUSPICIOUS\"] = \"SUSPICIOUS\";\n  CaseTag[\"NEEDS_REVIEW\"] = \"NEEDS_REVIEW\";\n  return CaseTag;\n}({});\n\n// Analytics Types\n\n// API Response Types\n\n// WebSocket Types\n\n// Component Props Types\n\n// Form Types\n\n// Error Types\n\n// Loading States\n\n// Chart Types\n\n// Health Check Types\n\n// Statistics Types\n\n// Navigation Types\n\n// Theme Types\n\n// Utility Types", "map": {"version": 3, "names": ["UserRole", "TransactionType", "CaseStatus", "CaseTag"], "sources": ["C:/Users/<USER>/Downloads/fraud-platform/dashboard/src/types/index.ts"], "sourcesContent": ["/**\n * Core type definitions for the Fraud Detection Platform\n */\n\n// User and Authentication Types\nexport interface User {\n  id: number;\n  username: string;\n  role: UserRole;\n  created_at: string;\n}\n\nexport enum UserRole {\n  ANALYST = 'analyst',\n  AUDITOR = 'auditor',\n  ADMIN = 'admin'\n}\n\nexport interface AuthContextType {\n  user: User | null;\n  login: (username: string, password: string) => Promise<boolean>;\n  logout: () => void;\n  isAuthenticated: boolean;\n}\n\n// Transaction Types\nexport interface Transaction {\n  transaction_id: string;\n  step: number;\n  type: TransactionType;\n  amount: number;\n  nameOrig: string;\n  oldbalanceOrg: number;\n  newbalanceOrig: number;\n  nameDest: string;\n  oldbalanceDest: number;\n  newbalanceDest: number;\n  risk_score?: number;\n  timestamp?: string;\n  isFraud?: boolean;\n  processed_at?: string;\n}\n\nexport enum TransactionType {\n  PAYMENT = 'PAYMENT',\n  TRANSFER = 'TRANSFER',\n  CASH_OUT = 'CASH_OUT',\n  DEBIT = 'DEBIT',\n  CASH_IN = 'CASH_IN'\n}\n\nexport interface TransactionRequest {\n  transactions: Transaction[];\n}\n\nexport interface RiskScore {\n  transaction_id: string;\n  risk: number;\n}\n\nexport interface RiskScoreResponse {\n  results: RiskScore[];\n}\n\n// Case Management Types\nexport interface Case {\n  id: number;\n  transaction_id: string;\n  user_id: number;\n  tag: CaseTag;\n  comment?: string;\n  status: CaseStatus;\n  created_at: string;\n  updated_at: string;\n  user?: User;\n  transaction?: Transaction;\n}\n\nexport enum CaseStatus {\n  OPEN = 'open',\n  CLOSED = 'closed',\n  PENDING = 'pending'\n}\n\nexport enum CaseTag {\n  FALSE_POSITIVE = 'FP',\n  CONFIRMED = 'CONFIRMED',\n  SUSPICIOUS = 'SUSPICIOUS',\n  NEEDS_REVIEW = 'NEEDS_REVIEW'\n}\n\nexport interface CaseCreate {\n  transaction_id: string;\n  tag: CaseTag;\n  comment?: string;\n  status?: CaseStatus;\n}\n\nexport interface CaseUpdate {\n  tag?: CaseTag;\n  comment?: string;\n  status?: CaseStatus;\n}\n\nexport interface CaseFilter {\n  status?: string;\n  transaction_id?: string;\n  tag?: CaseTag;\n  user_id?: number;\n  date_from?: string;\n  date_to?: string;\n}\n\n// Analytics Types\nexport interface CaseStats {\n  total_cases: number;\n  open_cases: number;\n  closed_cases: number;\n  pending_cases: number;\n  false_positives: number;\n  confirmed_fraud: number;\n  by_tag: Record<CaseTag, number>;\n  by_status: Record<CaseStatus, number>;\n}\n\nexport interface FeatureImportance {\n  feature: string;\n  importance: number;\n}\n\nexport interface FraudTrendData {\n  date: string;\n  count: number;\n}\n\nexport interface ModelInfo {\n  name: string;\n  version: string;\n  accuracy: number;\n  precision: number;\n  recall: number;\n  f1_score: number;\n  last_trained: string;\n}\n\n// API Response Types\nexport interface ApiResponse<T> {\n  data: T;\n  message?: string;\n  status: number;\n}\n\nexport interface PaginatedResponse<T> {\n  items: T[];\n  total: number;\n  page: number;\n  size: number;\n  pages: number;\n}\n\n// WebSocket Types\nexport interface WebSocketMessage {\n  type: 'transaction' | 'alert' | 'status';\n  data: any;\n  timestamp: string;\n}\n\nexport interface TransactionWebSocketMessage extends WebSocketMessage {\n  type: 'transaction';\n  data: {\n    transaction: Transaction;\n    risk_score: number;\n    timestamp: string;\n  };\n}\n\n// Component Props Types\nexport interface TableColumn<T> {\n  key: keyof T;\n  label: string;\n  sortable?: boolean;\n  render?: (value: any, item: T) => React.ReactNode;\n}\n\nexport interface SortConfig {\n  key: string;\n  direction: 'asc' | 'desc';\n}\n\n// Form Types\nexport interface FormField {\n  name: string;\n  label: string;\n  type: 'text' | 'email' | 'password' | 'select' | 'textarea' | 'number';\n  required?: boolean;\n  options?: { value: string; label: string }[];\n  validation?: (value: any) => string | undefined;\n}\n\n// Error Types\nexport interface ValidationError {\n  field: string;\n  message: string;\n}\n\nexport interface FormErrors {\n  [key: string]: string;\n}\n\n// Loading States\nexport interface LoadingState {\n  isLoading: boolean;\n  error?: string | null;\n}\n\n// Chart Types\nexport interface ChartData {\n  labels: string[];\n  datasets: {\n    label: string;\n    data: number[];\n    backgroundColor?: string | string[];\n    borderColor?: string | string[];\n    borderWidth?: number;\n  }[];\n}\n\n// Health Check Types\nexport interface HealthStatus {\n  status: 'ok' | 'error';\n  services?: {\n    model: boolean;\n    ingest: boolean;\n    database?: boolean;\n  };\n  timestamp: string;\n  error?: string;\n}\n\n// Statistics Types\nexport interface ServiceStats {\n  total_processed: number;\n  total_model_requests: number;\n  total_model_errors: number;\n  total_alerts: number;\n  success_rate: number;\n  avg_processing_time: number;\n  uptime: number;\n}\n\n// Navigation Types\nexport interface NavItem {\n  path: string;\n  label: string;\n  icon?: string;\n  roles?: UserRole[];\n}\n\n// Theme Types\nexport interface Theme {\n  colors: {\n    primary: string;\n    secondary: string;\n    success: string;\n    warning: string;\n    danger: string;\n    info: string;\n    light: string;\n    dark: string;\n  };\n  spacing: {\n    xs: string;\n    sm: string;\n    md: string;\n    lg: string;\n    xl: string;\n  };\n}\n\n// Utility Types\nexport type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;\nexport type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;\nexport type DeepPartial<T> = {\n  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA;;AAQA,WAAYA,QAAQ,0BAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAAA,OAARA,QAAQ;AAAA;;AAapB;;AAkBA,WAAYC,eAAe,0BAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAfA,eAAe;EAAA,OAAfA,eAAe;AAAA;;AAqB3B;;AAcA,WAAYC,UAAU,0BAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAA,OAAVA,UAAU;AAAA;AAMtB,WAAYC,OAAO,0BAAPA,OAAO;EAAPA,OAAO;EAAPA,OAAO;EAAPA,OAAO;EAAPA,OAAO;EAAA,OAAPA,OAAO;AAAA;;AA6BnB;;AAgCA;;AAeA;;AAgBA;;AAaA;;AAUA;;AAUA;;AAMA;;AAYA;;AAYA;;AAWA;;AAQA;;AAqBA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}