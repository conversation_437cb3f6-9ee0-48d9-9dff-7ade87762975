import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import App from '../App';
import { AuthProvider } from '../services/AuthContext';
import { WebSocketProvider } from '../services/WebSocketContext';

// Mock the API module
jest.mock('../services/api', () => ({
  loginUser: jest.fn(),
  fetchLatestTransactions: jest.fn(),
  fetchCases: jest.fn(),
  fetchCaseStats: jest.fn(),
  fetchFeatureImportance: jest.fn(),
  fetchFraudTrend: jest.fn(),
  ApiError: class ApiError extends Error {
    constructor(message: string, public status: number = 500) {
      super(message);
      this.name = 'ApiError';
    }
  }
}));

// Mock WebSocket
global.WebSocket = jest.fn().mockImplementation(() => ({
  onopen: jest.fn(),
  onmessage: jest.fn(),
  onerror: jest.fn(),
  onclose: jest.fn(),
  send: jest.fn(),
  close: jest.fn(),
  readyState: 1
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock as any;

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <AuthProvider>
      <WebSocketProvider>
        {children}
      </WebSocketProvider>
    </AuthProvider>
  </BrowserRouter>
);

describe('App Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  test('renders login page when not authenticated', () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    expect(screen.getByText('Fraud Detection Platform')).toBeInTheDocument();
    expect(screen.getByText('Login')).toBeInTheDocument();
    expect(screen.getByLabelText('Username')).toBeInTheDocument();
    expect(screen.getByLabelText('Password')).toBeInTheDocument();
  });

  test('shows demo credentials on login page', () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    expect(screen.getByText('Demo Credentials:')).toBeInTheDocument();
    expect(screen.getByText(/analyst.*password/)).toBeInTheDocument();
    expect(screen.getByText(/admin.*password/)).toBeInTheDocument();
  });

  test('handles login form validation', async () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    const loginButton = screen.getByRole('button', { name: /login/i });
    
    // Button should be disabled when fields are empty
    expect(loginButton).toBeDisabled();

    // Fill in username only
    const usernameInput = screen.getByLabelText('Username');
    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    
    // Button should still be disabled
    expect(loginButton).toBeDisabled();

    // Fill in password
    const passwordInput = screen.getByLabelText('Password');
    fireEvent.change(passwordInput, { target: { value: 'testpass' } });
    
    // Button should now be enabled
    expect(loginButton).not.toBeDisabled();
  });

  test('redirects to dashboard when authenticated', () => {
    // Mock authenticated state
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'fraud-platform:token') return 'mock-token';
      if (key === 'fraud-platform:user') return JSON.stringify({
        id: 1,
        username: 'testuser',
        role: 'analyst',
        created_at: '2023-01-01T00:00:00Z'
      });
      return null;
    });

    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    // Should show dashboard content instead of login
    expect(screen.queryByText('Login')).not.toBeInTheDocument();
  });
});

describe('TypeScript Type Safety', () => {
  test('API functions have correct return types', async () => {
    const { loginUser, fetchLatestTransactions } = require('../services/api');
    
    // Mock implementations with correct types
    loginUser.mockResolvedValue({
      access_token: 'token',
      token_type: 'bearer'
    });

    fetchLatestTransactions.mockResolvedValue([
      {
        transaction_id: 'test-123',
        step: 1,
        type: 'PAYMENT',
        amount: 100.50,
        nameOrig: 'Alice',
        oldbalanceOrg: 1000,
        newbalanceOrig: 899.50,
        nameDest: 'Bob',
        oldbalanceDest: 0,
        newbalanceDest: 100.50,
        risk_score: 0.3
      }
    ]);

    const loginResult = await loginUser('test', 'pass');
    expect(loginResult).toHaveProperty('access_token');
    expect(loginResult).toHaveProperty('token_type');

    const transactions = await fetchLatestTransactions(10);
    expect(Array.isArray(transactions)).toBe(true);
    if (transactions.length > 0) {
      expect(transactions[0]).toHaveProperty('transaction_id');
      expect(transactions[0]).toHaveProperty('type');
      expect(transactions[0]).toHaveProperty('amount');
    }
  });

  test('User roles are properly typed', () => {
    const { UserRole } = require('../types');
    
    expect(UserRole.ADMIN).toBe('admin');
    expect(UserRole.ANALYST).toBe('analyst');
    expect(UserRole.AUDITOR).toBe('auditor');
  });

  test('Transaction types are properly typed', () => {
    const { TransactionType } = require('../types');
    
    expect(TransactionType.PAYMENT).toBe('PAYMENT');
    expect(TransactionType.TRANSFER).toBe('TRANSFER');
    expect(TransactionType.CASH_OUT).toBe('CASH_OUT');
    expect(TransactionType.DEBIT).toBe('DEBIT');
    expect(TransactionType.CASH_IN).toBe('CASH_IN');
  });
});

describe('Error Handling', () => {
  test('handles API errors gracefully', async () => {
    const { loginUser } = require('../services/api');
    const apiError = new Error('Network error');
    loginUser.mockRejectedValue(apiError);

    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    const usernameInput = screen.getByLabelText('Username');
    const passwordInput = screen.getByLabelText('Password');
    const loginButton = screen.getByRole('button', { name: /login/i });

    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    fireEvent.change(passwordInput, { target: { value: 'wrongpass' } });
    fireEvent.click(loginButton);

    await waitFor(() => {
      expect(screen.getByText(/network error/i)).toBeInTheDocument();
    });
  });

  test('error boundary catches component errors', () => {
    // Mock console.error to avoid noise in test output
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    const ThrowError = () => {
      throw new Error('Test error');
    };

    render(
      <TestWrapper>
        <ThrowError />
      </TestWrapper>
    );

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    
    consoleSpy.mockRestore();
  });
});

describe('Accessibility', () => {
  test('login form has proper labels and accessibility attributes', () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    const usernameInput = screen.getByLabelText('Username');
    const passwordInput = screen.getByLabelText('Password');
    const loginButton = screen.getByRole('button', { name: /login/i });

    expect(usernameInput).toHaveAttribute('type', 'text');
    expect(usernameInput).toHaveAttribute('autoComplete', 'username');
    expect(passwordInput).toHaveAttribute('type', 'password');
    expect(passwordInput).toHaveAttribute('autoComplete', 'current-password');
    expect(loginButton).toHaveAttribute('type', 'submit');
  });

  test('navigation is keyboard accessible', () => {
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'fraud-platform:token') return 'mock-token';
      if (key === 'fraud-platform:user') return JSON.stringify({
        id: 1,
        username: 'testuser',
        role: 'analyst',
        created_at: '2023-01-01T00:00:00Z'
      });
      return null;
    });

    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    // Check that navigation links are focusable
    const navLinks = screen.getAllByRole('link');
    navLinks.forEach(link => {
      expect(link).toHaveAttribute('href');
    });
  });
});
